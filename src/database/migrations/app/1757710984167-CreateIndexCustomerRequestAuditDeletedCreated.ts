import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIndexCustomerRequestAuditDeletedCreated1757710984167
    implements MigrationInterface
{
    name = 'CreateIndexCustomerRequestAuditDeletedCreated1757710984167';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE INDEX \`idx_customer_request_audit_deleted_created\` ON \`customer_request\` (\`fk_auditor_framework_id\`, \`deleted_at\`, \`created_at\`)`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX \`idx_customer_request_audit_deleted_created\` ON \`customer_request\``,
        );
    }
}
