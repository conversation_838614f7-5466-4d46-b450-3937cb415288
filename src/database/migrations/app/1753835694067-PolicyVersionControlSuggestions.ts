import { MigrationInterface, QueryRunner } from "typeorm";

export class PolicyVersionControlSuggestions1753835694067 implements MigrationInterface {
    name = 'PolicyVersionControlSuggestions1753835694067'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`policy_version_control_suggestion\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`justification\` varchar(768) NOT NULL, \`score\` tinyint NOT NULL, \`is_relevant_suggestion\` tinyint NOT NULL DEFAULT 1, \`deleted_at\` datetime(6) NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`fk_policy_version_id\` int UNSIGNED NULL, \`fk_control_id\` int UNSIGNED NULL, \`fk_suggestion_execution_id\` int UNSIGNED NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`policy_version_control_suggestion_execution\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`status\` varchar(191) NOT NULL, \`errored_at\` datetime(6) NULL, \`errorType\` varchar(191) NULL, \`error_description\` varchar(191) NULL, \`execution_id\` varchar(191) NULL, \`feedback_sent_at\` datetime NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deleted_at\` datetime(6) NULL, \`fk_policy_version_id\` int UNSIGNED NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` ADD CONSTRAINT \`FK_fd2fff87151d57cc4f6d4112f5d\` FOREIGN KEY (\`fk_policy_version_id\`) REFERENCES \`policy_version\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` ADD CONSTRAINT \`FK_ba7989c55c9bfdad9f84688085e\` FOREIGN KEY (\`fk_control_id\`) REFERENCES \`control\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` ADD CONSTRAINT \`FK_86112967d85f6dea544edf0e177\` FOREIGN KEY (\`fk_suggestion_execution_id\`) REFERENCES \`policy_version_control_suggestion_execution\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion_execution\` ADD CONSTRAINT \`FK_a0bccbe493a2e7d66221a543974\` FOREIGN KEY (\`fk_policy_version_id\`) REFERENCES \`policy_version\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion_execution\` DROP FOREIGN KEY \`FK_a0bccbe493a2e7d66221a543974\``);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` DROP FOREIGN KEY \`FK_86112967d85f6dea544edf0e177\``);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` DROP FOREIGN KEY \`FK_ba7989c55c9bfdad9f84688085e\``);
        await queryRunner.query(`ALTER TABLE \`policy_version_control_suggestion\` DROP FOREIGN KEY \`FK_fd2fff87151d57cc4f6d4112f5d\``);
        await queryRunner.query(`DROP TABLE \`policy_version_control_suggestion_execution\``);
        await queryRunner.query(`DROP TABLE \`policy_version_control_suggestion\``);
    }

}
