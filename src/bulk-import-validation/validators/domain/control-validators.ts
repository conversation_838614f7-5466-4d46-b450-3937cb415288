import { ValidationType } from '@flatfile/api/api';
import { ControlBlueprintField } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.fields';
import { Control } from 'app/grc/entities/control.entity';
import { BaseValidator } from 'bulk-import-validation/classes/base-validator';
import { ValidationResult } from 'bulk-import-validation/types/validation-result.type';
import { capitalize } from 'commons/helpers/string.helper';
import moment from 'moment';

export class ControlApprovalDeadline<T> extends BaseValidator<T> {
    validate(value: unknown, data: T): ValidationResult {
        const today = moment().utc().format('YYYY-MM-DD');
        const isValid = moment(value as string)
            .utc()
            .isAfter(today, 'day');

        return {
            valid: isValid,
            field: this.field,
            messages: isValid
                ? []
                : [
                      {
                          field: this.field,
                          type: ValidationType.Error,
                          message: `The Approval Deadline must be in the future.`,
                      },
                  ],
            data,
        };
    }
}

export class ControlNameAndCodeValidator extends BaseValidator<unknown> {
    constructor(private readonly getControlBy: (code: string, name: string) => Promise<Control[]>) {
        super();
    }

    async validate(value: unknown, data: { [key: string]: unknown }): Promise<ValidationResult> {
        const code = String(data[ControlBlueprintField.CODE]);
        const name = String(value); 

        const [control] = await this.getControlBy(code, name);

        if (!control) {
            return {
                valid: true,
                data,
                field: this.field,
                messages: [],
            };
        }

        const isSame = control.name === String(value);

        const fieldName = capitalize(this.fieldName);

        if (isSame)
            return {
                valid: false,
                data: value,
                messages: [
                    {
                        field: this.field,
                        type: ValidationType.Warn,
                        message: `This ${fieldName} already exists. This Control will be updated with the provided values.`,
                    },
                ],
            };

        return {
            valid: true,
            field: this.field,
            messages: [],
            data,
        };
    }
}
