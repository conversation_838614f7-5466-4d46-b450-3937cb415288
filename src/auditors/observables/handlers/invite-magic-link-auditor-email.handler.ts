import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { TenantEventHandler } from 'app/events/tenant-event.handler';
import { InviteMagicLinkAuditorEvent } from 'auditors/observables/events/invite-magic-link-auditor.event';
import { AuthService } from 'auth/services/auth.service';
import { EmailConfig } from 'commons/configs/email.config';
import { currentYear } from 'commons/helpers/date.helper';
import { getServerRegion } from 'commons/helpers/environment.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import { encodeHTML } from 'commons/helpers/security.helper';
import { hasSpecialHtmlChars } from 'commons/helpers/string.helper';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType, EmailTimeSensitivity } from 'commons/types/email-options.type';
import config from 'config';
import { decodeHTML } from 'entities';
import { format } from 'util';

@EventsHandler(InviteMagicLinkAuditorEvent)
export class InviteMagicLinkAuditorEmailHandler extends TenantEventHandler<InviteMagicLinkAuditorEvent> {
    /**
     *
     * @param authService
     * @param emailConfig
     * @param emailService
     */
    constructor(
        private readonly authService: AuthService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
    ) {
        super();
    }

    /**
     *
     * @param event
     */
    async handleEvent(event: InviteMagicLinkAuditorEvent): Promise<void> {
        const { user, account } = event;
        const token = await this.authService.generateMagicLinkToken({
            email: user.email,
            isAuditor: true,
        });
        // get user language
        const emailLanguage = getLanguage(user.language, account.language);
        const magicLinkEmail = await this.emailConfig.magicLinkEmailAuditorInvite(emailLanguage);

        let companyName = encodeHTML(account.companyName);

        if (hasSpecialHtmlChars(companyName)) {
            companyName = decodeHTML(companyName);
        }

        magicLinkEmail.subject = format(magicLinkEmail.subject, companyName);

        const templateVariables = {
            title: format(magicLinkEmail.title, user.firstName),
            paragraphOne: format(magicLinkEmail.paragraphOne, companyName),
            paragraphBelowCtaBtn: format(magicLinkEmail.paragraphBelowCtaBtn, companyName),
            ctaUrl: `${config.get('url.webApp')}/magic-link?token=${
                token.id
            }&region=${getServerRegion()}`,
            btnText: magicLinkEmail.btnText,
            currentYear: currentYear(),
            buttonNotWorking: magicLinkEmail.buttonNotWorking,
            ...magicLinkEmail.templateCommon,
        };
        const emailOptions: EmailOptionsType = {
            ...magicLinkEmail,
            toEmail: user.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.HIGH,
        };
        this.emailService.sendEmail(emailOptions, account);
    }
}
