import { ComplianceCheckStatus, ComplianceCheckType } from '@drata/enums';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { Account } from 'auth/entities/account.entity';
import { program } from 'commander';
import { RestrictedTenantBackfill } from 'commons/backfill/restricted-tenant-backfill';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { CheckFrequency } from 'commons/enums/check-frequency.enum';
import { trimUniq } from 'commons/helpers/array.helper';
import { checkFrequencyNextExpiration } from 'commons/helpers/check-frequency.helper';
import { numberProcess } from 'commons/helpers/cli.helper';
import { isEmpty, toNumber } from 'lodash';

/**
 * This backfill is the initial step to fix an issue were a personnel (a valid one) does not
 * have compliance checks
 * Usually running this command will fix the issue:
 * ./bin/drata-cli complianceChecks -a compute -cc 1
 * ^ this backfill will verify what compliances checks you have missing and then it will create it, the catch is that
 * the personnel need to have at least 1 compliance check to be able to create the rest,
 * so this command will not work for personnel without ANY compliance check at all
 *
 * To fix this issue we need to run this backfill first, this will create the full compliance check for the users
 * then we should run the ./bin/drata-cli complianceChecks -a compute -cc 1
 * and it will create the rest
 *
 * NOTE: You need to make sure that the personnel is a current personnel
 */
class BackfillCreateFullComplianceCheckForUsers extends RestrictedTenantBackfill {
    protected async runTenantBackfill(
        account: Account,
        tenantConnection: DrataDataSource,
    ): Promise<void> {
        const personnelCheckRepository = tenantConnection.getRepository(Personnel);
        const complianceCheckRepository = tenantConnection.getRepository(ComplianceCheck);

        const query = personnelCheckRepository
            .createQueryBuilder('personnel')
            .leftJoinAndSelect('personnel.complianceChecks', 'complianceChecks')
            .where('complianceChecks.id IS NULL')
            .andWhere('personnel.id IN (:...personnelIds)', {
                personnelIds: this.getPersonnelIds(),
            });

        const personnelWIthoutFullComplianceCheck = await query.getMany();

        if (this.isDryRun) {
            this.logInfo({
                msg: `DRY RUN: Found ${personnelWIthoutFullComplianceCheck.length} users without full compliance check`,
                account,
            });

            return;
        }

        if (isEmpty(personnelWIthoutFullComplianceCheck)) {
            this.logInfo({
                msg: `No users without full compliance check found`,
                account,
            });

            return;
        }

        this.logInfo({
            msg: `Found ${personnelWIthoutFullComplianceCheck.length} users without full compliance check`,
            account,
        });

        for (const personnel of personnelWIthoutFullComplianceCheck) {
            const complianceCheck = new ComplianceCheck();
            complianceCheck.type = ComplianceCheckType.FULL_COMPLIANCE;
            complianceCheck.checkFrequency = CheckFrequency.DAILY;
            complianceCheck.personnel = personnel;
            complianceCheck.status = ComplianceCheckStatus.FAIL;
            complianceCheck.expiresAt = checkFrequencyNextExpiration(
                complianceCheck.checkFrequency,
            );

            this.logInfo({
                msg: `Creating full compliance check for personnel ${personnel.id}`,
                account,
            });
            // eslint-disable-next-line no-await-in-loop
            await complianceCheckRepository.save(complianceCheck);

            this.logInfo({
                msg: `Created full compliance check for personnel ${personnel.id}`,
                account,
            });
        }
    }

    protected beforeAllTenants(): Promise<void> {
        const { accountDomains } = program;

        if (isEmpty(accountDomains)) {
            throw new Error("-d, --account-domains [account-domains...] can't be empty");
        }

        const uniqueAccountIds = trimUniq(accountDomains).map(toNumber);

        if (uniqueAccountIds.length > 1) {
            throw new Error(
                '-d, --account-domains [account-domains...] can only have one account id, this is to avoid conflicting user ids',
            );
        }

        return Promise.resolve();
    }

    protected setDryRun() {
        this.isDryRun = true;
        this.logInfo({ msg: 'Dry Run! No data will be changed!' });
    }

    protected setScriptOptions(): void {
        super.setScriptOptions([
            {
                flags: '-p, --personnel-ids [personnel-ids...]',
                description: 'One or more personnel ids to run the backfill for (space-delimited)',
                fn: numberProcess,
                defaultValue: [],
            },
        ]);
    }

    private getPersonnelIds(): number[] {
        const { personnelIds } = program;

        if (isEmpty(personnelIds)) {
            throw new Error("-p, --personnel-ids [personnel-ids...] can't be empty");
        }

        return personnelIds;
    }
}

void new BackfillCreateFullComplianceCheckForUsers().runTenantsBackfill();
