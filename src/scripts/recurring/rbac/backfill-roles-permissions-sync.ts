import { Permission } from 'app/users/entities/permission.entity';
import { RoleEntity } from 'app/users/entities/role.entity';
import { RolesPermissions } from 'app/users/entities/roles-permissions.entity';
import { UserRole } from 'app/users/entities/user-role.entity';
import { UsersRoles } from 'app/users/entities/users-roles.entity';
import { WorkflowEnabledTenantBackfill } from 'app/worker/workflows/backfills/base-classes/workflow-enabled-tenant-backfill';
import { BackfillClassActivityMapRecord } from 'app/worker/workflows/backfills/helpers/backfill-activity.types';
import { Account } from 'auth/entities/account.entity';
import { TemporalBackfillLogger } from 'commons/backfill/temporal-backfill.service';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { Action } from 'commons/enums/users/action.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { stringProcess } from 'commons/helpers/cli.helper';
import { pathResolve } from 'commons/helpers/file.helper';
import { readYmlFile } from 'commons/helpers/yml-reader.helper';
import { CommanderProgramOptions } from 'commons/types/program-options.type';
import config from 'config';
import { isEmpty, isNil } from 'lodash';
import { validateRole } from 'scripts/recurring/rbac/role-validator';

/**
 * This backfill is used to populate the tables: role, permission, roles_permissions_map
 * The data comes from src/database/seeds/global/fixtures/extended-roles.yml and
 * role.enum.ts
 */
export class BackfillRolesPermissionSync extends WorkflowEnabledTenantBackfill {
    customOptions: CommanderProgramOptions[] = [
        {
            flags: '--role <string>',
            description: 'Role to update users_permissions_map for',
            fn: stringProcess,
            defaultValue: [],
        },
        {
            flags: '--isDeleting <boolean>',
            description: 'If set to true, will delete existing entries in roles_permissions_map',
            defaultValue: false,
        },
    ];

    async backfill({
        backfillName,
        tenantConnection,
        account,
        dryRun,
        customOptions,
    }): Promise<void> {
        TemporalBackfillLogger.logInfo({
            msg: `Starting backfill for: ${account.id}`,
            account,
            backfillName,
        });
        const { subjects, role, isDeleting } = customOptions;

        const { isValid: isValidRole, roleIds, error: roleError } = validateRole(role);
        if (!isValidRole && roleError) {
            TemporalBackfillLogger.logError({ msg: 'Invalid role', error: roleError });
            return;
        }

        await this.handleRoleEnumBackfills(tenantConnection, account, dryRun);
        await this.syncUsersRolesMap(tenantConnection, account, dryRun);

        const queryRunner = tenantConnection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const rolesPermissions: RolesPermissions[] = [];
            const existingPermissions = await queryRunner.manager.find(Permission);

            const createdAt = new Date();

            const permissionsToChange = this.getPermissions(
                subjects,
                existingPermissions,
                createdAt,
            );

            if (isEmpty(permissionsToChange)) {
                TemporalBackfillLogger.logInfo({
                    msg: `No permissions changes found: ${account.domain}`,
                    account,
                });
            } else {
                TemporalBackfillLogger.logInfo({
                    msg: ` ${permissionsToChange.length} permissions to be added in ${account.domain}`,
                    account,
                });
                await queryRunner.manager.insert(Permission, permissionsToChange);
            }

            const { extendedRoles } = await this.getLocalRoles();

            if (isNil(extendedRoles)) {
                TemporalBackfillLogger.logInfo({
                    msg: `YML file not found: ${account.domain}`,
                    account,
                });
            }

            if (isDeleting) {
                // isDeleting option exists so that when a cell in airtable is unchecked, those reductions of access work properly.
                if (roleIds) {
                    TemporalBackfillLogger.logInfo({
                        msg: `Deleting from permissions map role id: ${roleIds}.`,
                    });
                    await queryRunner.query(
                        'delete from roles_permissions_map where fk_role_id in (?)',
                        [roleIds],
                    );
                } else {
                    TemporalBackfillLogger.logInfo({
                        msg: 'Deleting all entries in roles_permissions_map',
                    });
                    await queryRunner.query('delete from roles_permissions_map');
                }
            }

            //reload permissions
            const dbPermissions = await queryRunner.manager.find(Permission);
            const dbRolesPermission = await queryRunner.manager.find(RolesPermissions);

            // Create a Set to track unique role-permission pairs
            const uniqueRolePermissionPairs = new Set();
            let id = 1;

            // Process the extendedRoles but prevent duplicates
            extendedRoles.forEach((r: { rolesPermissions: any[] }) => {
                r.rolesPermissions.forEach((p: any[]) => {
                    p.forEach((rolePermission: RolesPermissions) => {
                        //if has subject parameter and is not in the list return / continue
                        if (
                            !isEmpty(subjects) &&
                            !subjects.some(
                                (x: number) => x === Number(rolePermission.permission.subject),
                            )
                        ) {
                            return;
                        }

                        //find if exists in db
                        const permissionFound = dbPermissions.find(
                            x =>
                                x.subject === rolePermission.permission.subject &&
                                x.action === rolePermission.permission.action,
                        );

                        if (!isNil(permissionFound)) {
                            // Create a unique key for this role-permission pair
                            const uniqueKey = `${rolePermission.role.id}-${permissionFound.id}`;

                            // Check if we've already processed this pair
                            if (uniqueRolePermissionPairs.has(uniqueKey)) {
                                // Skip this duplicate
                                return;
                            }

                            // Mark this pair as processed
                            uniqueRolePermissionPairs.add(uniqueKey);

                            const rolesPermissionExists = dbRolesPermission.find(
                                x =>
                                    x.permissionId === permissionFound.id &&
                                    x.roleId === rolePermission.role.id,
                            );

                            if (isNil(rolesPermissionExists)) {
                                //roles permissions does not exists
                                rolesPermissions.push({
                                    id: roleIds ? undefined : isDeleting ? id : undefined,
                                    createdAt,
                                    roleId: rolePermission.role.id,
                                    permissionId: permissionFound.id,
                                } as RolesPermissions);
                                id++;
                            }
                        }
                    });
                });
            });

            // Add logging to show how many duplicates were prevented
            TemporalBackfillLogger.logInfo({
                msg: `Processed ${uniqueRolePermissionPairs.size} unique role-permission pairs from extended roles`,
                account,
            });

            if (!isEmpty(rolesPermissions)) {
                // Get existing role-permission mappings
                const existingRolePermissions = await queryRunner.manager.find(RolesPermissions, {
                    relations: ['permission'],
                });

                // Create lookup maps for existing permissions by role
                const existingPermissionsByRole = existingRolePermissions.reduce(
                    (acc, curr) => {
                        if (!acc[curr.roleId]) {
                            acc[curr.roleId] = new Set();
                        }
                        acc[curr.roleId].add(curr.permissionId);
                        return acc;
                    },
                    {} as Record<number, Set<number>>,
                );

                // Group new permissions by role and find differences
                const changes: Record<number, { added: string[] }> = {};

                for (const rp of rolesPermissions) {
                    if (!changes[rp.roleId]) {
                        changes[rp.roleId] = { added: [] };
                    }

                    const existing = existingPermissionsByRole[rp.roleId] || new Set();
                    if (!existing.has(rp.permissionId)) {
                        // eslint-disable-next-line no-await-in-loop
                        const permission = await queryRunner.manager.findOne(Permission, {
                            where: { id: rp.permissionId },
                        });
                        changes[rp.roleId].added.push(
                            `${Action[permission.action]} ${Subject[permission.subject]}`,
                        );
                    }
                }

                // Log changes for roles that have differences
                for (const [roleId, diff] of Object.entries(changes)) {
                    if (diff.added.length > 0) {
                        TemporalBackfillLogger.logInfo({
                            msg: `Role changes for ${Role[roleId]}:`,
                            account,
                            metadata: {
                                added: diff.added,
                            },
                        });
                    }
                }

                TemporalBackfillLogger.logInfo({
                    msg: `Total ${rolesPermissions.length} new 'roles_permissions_map' to be added in ${account.domain}`,
                    account,
                });

                if (!dryRun) {
                    await queryRunner.manager.insert(RolesPermissions, rolesPermissions);
                }
            }
            if (dryRun) {
                TemporalBackfillLogger.logInfo({ msg: 'dryRun true. Rolling back transaction.' });
                await queryRunner.rollbackTransaction();
            } else {
                await queryRunner.commitTransaction();
            }
        } catch (transactionError) {
            TemporalBackfillLogger.logError({
                msg: `ERROR: Something went wrong. Rolling back transaction`,
                error: transactionError,
            });
            await queryRunner.rollbackTransaction();
        }
    }

    private getPermissions(subjects: number[], existingPermissions: Permission[], createdAt: Date) {
        const permissions: Permission[] = [];
        for (const s in Subject) {
            if (!Number.isNaN(Number(s))) {
                //if has subject parameter and is not in the list return / continue
                if (!isEmpty(subjects) && !subjects.some((x: number) => x === Number(s))) {
                    continue;
                }
                //is in the subject list or this list is empty
                for (const a in Action) {
                    if (!Number.isNaN(Number(a))) {
                        //this permission already exists?
                        const permissionFound = existingPermissions.find(
                            pm => pm.action === Number(a) && pm.subject === Number(s),
                        );
                        if (isNil(permissionFound)) {
                            //this is a new permission
                            permissions.push({
                                subject: Number(s),
                                action: Number(a),
                                createdAt,
                            } as Permission);
                        }
                    }
                }
            }
        }
        return permissions;
    }

    private async handleRoleEnumBackfills(
        tenantConnection: DrataDataSource,
        account: Account,
        dryRun: boolean,
    ) {
        const roleRepository = tenantConnection.getRepository(RoleEntity);
        const rolesToSkip: RoleEntity[] = [];
        const rolesToInsert: RoleEntity[] = [];
        // get all existing roles so we can compare the enum to what is in the DB to decide what needs to be backfilled
        const existingRoles = await roleRepository.find({
            withDeleted: true,
        });
        for (const r in Role) {
            if (isNaN(Number(r))) {
                // skip string keys
                continue;
            }

            const existingRole = existingRoles.find((role: RoleEntity) => role.role === Number(r));
            if (existingRole !== undefined) {
                rolesToSkip.push(existingRole);
                continue;
            }

            const insertRole: RoleEntity = new RoleEntity();
            insertRole.role = Number(r);
            TemporalBackfillLogger.logInfo({
                msg: `Adding new role found to role insertion list: ${insertRole.role}`,
                account,
            });
            rolesToInsert.push(insertRole);
        }

        if (!isEmpty(rolesToSkip)) {
            TemporalBackfillLogger.logInfo({
                msg: `Skipping ${rolesToSkip.length} roles as they already exist or were already soft-deleted in the tenant database.`,
                account,
            });
        }

        if (!isEmpty(rolesToInsert)) {
            TemporalBackfillLogger.logInfo({
                msg: `Inserting ${rolesToInsert.length} roles`,
                account,
            });
            if (dryRun) {
                TemporalBackfillLogger.logInfo({ msg: 'dryRun true. No changes made to roles.' });
            } else {
                const inserted = await roleRepository.insert(rolesToInsert);
                TemporalBackfillLogger.logInfo({
                    msg: `Inserted ${inserted.identifiers.length} roles`,
                    account,
                });
            }
        } else {
            TemporalBackfillLogger.logInfo({
                msg: `No roles changes found: ${account.domain}`,
                account,
            });
        }
    }

    private async getLocalRoles() {
        return readYmlFile(pathResolve(config.get('fixtures.extendedRoles')));
    }

    private async syncUsersRolesMap(
        tenantConnection: DrataDataSource,
        account: Account,
        dryRun: boolean,
    ): Promise<void> {
        TemporalBackfillLogger.logInfo({
            msg: 'Starting users_roles_map sync',
            account,
        });

        const queryRunner = tenantConnection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // Get all entries from user_role table
            const userRoles = await queryRunner.manager.find(UserRole, {
                relations: ['user'],
            });

            // Get all entries from users_roles_map table
            const usersRolesMap = await queryRunner.manager.find(UsersRoles, {
                relations: ['user'],
                withDeleted: true,
            });

            const rolesToSync: UsersRoles[] = [];

            // For each user_role entry, ensure corresponding users_roles_map entry exists
            for (const userRole of userRoles) {
                const existingMapping = usersRolesMap.find(
                    urm =>
                        urm.userId === userRole.user.id &&
                        urm.roleId === userRole.role &&
                        urm.deletedAt === userRole.deletedAt,
                );

                const now = new Date();
                if (!existingMapping) {
                    const userRoleMap = new UsersRoles();
                    userRoleMap.user = userRole.user;
                    userRoleMap.userId = userRole.user.id;
                    userRoleMap.roleId = userRole.role;
                    userRoleMap.createdAt = userRole.createdAt || now;
                    userRoleMap.updatedAt = userRole.updatedAt || now;
                    userRoleMap.deletedAt = userRole.deletedAt;
                    rolesToSync.push(userRoleMap);
                }
            }

            if (!isEmpty(rolesToSync)) {
                TemporalBackfillLogger.logInfo({
                    msg: `Found ${rolesToSync.length} roles to sync in users_roles_map`,
                    account,
                });

                if (!dryRun) {
                    await queryRunner.manager.save(UsersRoles, rolesToSync);
                    TemporalBackfillLogger.logInfo({
                        msg: 'Successfully synced users_roles_map',
                        account,
                    });
                    queryRunner.rollbackTransaction();
                } else {
                    TemporalBackfillLogger.logInfo({
                        msg: 'Dry run - skipping users_roles_map sync',
                        account,
                    });
                    await queryRunner.commitTransaction();
                }
            } else {
                TemporalBackfillLogger.logInfo({
                    msg: 'No roles to sync in users_roles_map',
                    account,
                });
            }
        } catch (error) {
            TemporalBackfillLogger.logError({
                msg: 'Error syncing users_roles_map',
                error,
                account,
            });
            await queryRunner.rollbackTransaction();
            throw error;
        }
    }
}

export const temporalBackfillRolesPermissionsSync: BackfillClassActivityMapRecord = {
    backfillClass: new BackfillRolesPermissionSync(),
};
