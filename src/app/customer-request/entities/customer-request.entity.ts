import { CustomerRequestMessage } from 'app/customer-request/entities/customer-request-message.entity';
import { CustomerRequestStatus } from 'app/customer-request/enums/customer-request-status.enum';
import { Control } from 'app/grc/entities/control.entity';
import { User } from 'app/users/entities/user.entity';
import { IsIn, IsNotEmpty, IsOptional, IsUUID, MaxLength } from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import { getValues } from 'commons/helpers/enum.helper';
import config from 'config';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    Relation,
    UpdateDateColumn,
} from 'typeorm';

@Entity('customer_request')
@Index('idx_customer_request_audit_deleted_created', [
    'auditorFrameworkId',
    'deletedAt',
    'createdAt',
])
export class CustomerRequest extends BaseEntity {
    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ length: config.get('db.varcharLength') })
    code: string;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ length: config.get('db.varcharLength') })
    title: string;

    @IsOptional()
    @MaxLength(config.get('validation.maxLongText'))
    @Column({
        type: 'text',
        nullable: true,
    })
    description: string | null;

    @IsNotEmpty()
    @IsIn(getValues(CustomerRequestStatus))
    @Column({ type: BaseEntity.smallInt() })
    status: CustomerRequestStatus;

    @DeleteDateColumn({ type: BaseEntity.timestamp(), name: 'deleted_at' })
    deletedAt: Date | null;

    @Index()
    @CreateDateColumn({ type: BaseEntity.timestamp(), name: 'created_at' })
    createdAt: Date;

    @Index()
    @UpdateDateColumn({ type: BaseEntity.timestamp(), name: 'updated_at' })
    updatedAt: Date;

    @Index()
    @IsNotEmpty()
    @IsUUID()
    @Column({
        name: 'fk_auditor_framework_id',
        length: config.get('db.uuidLength'),
    })
    auditorFrameworkId: string;

    @ManyToMany(() => Control, control => control.customerRequests, {
        eager: false,
        onDelete: 'NO ACTION',
        onUpdate: 'NO ACTION',
    })
    @JoinTable({
        name: 'customer_request_evidence_map',
        joinColumn: {
            name: 'fk_customer_request_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'fk_control_id',
            referencedColumnName: 'id',
        },
    })
    controls: Relation<Control>[];

    @OneToMany(() => CustomerRequestMessage, history => history.customerRequest, {
        eager: true,
        nullable: false,
    })
    customerRequestMessages: CustomerRequestMessage[];

    @ManyToMany(() => User, user => user.customerRequest, { eager: false })
    @JoinTable({
        name: 'users_requests_map',
        joinColumn: {
            name: 'fk_customer_request_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'fk_user_id',
            referencedColumnName: 'id',
        },
    })
    owners: Relation<User>[];
}
