import { ErrorC<PERSON>, SentByReservedUUIDs } from '@drata/enums';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { AuditorCreateCustomerRequestsRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/auditor-create-customer-requests-request.auditor-api.dto';
import { CreateCustomerRequestMessageRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/create-customer-request-message-request.auditor-api.dto';
import { RequestMessagesListRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/request-messages-list-request.auditor-api.dto';
import { UpdateCustomerRequestStatusRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/update-customer-request-status-request.auditor-api.dto';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlService } from 'app/control/control.service';
import { CustomerRequestMessageBaseAdapter } from 'app/customer-request/adapters/customer-request-message-base.adapter';
import { DrataUpdateCustomerRequestMessageAdapter } from 'app/customer-request/adapters/drata-update-customer-request-message.adapter';
import { UserUpdateCustomerRequestMessageAdapter } from 'app/customer-request/adapters/user-update-customer-request-message.adapter';
import { auditHubEmailTypeToFeatureType } from 'app/customer-request/constants/audit-hub-email-type-to-feature-type-map.constant';
import { CustomerRequestListRequestDto } from 'app/customer-request/dtos/customer-request-list-request.dto';
import { CustomerRequestMessageFile } from 'app/customer-request/entities/customer-request-message-file.entity';
import { CustomerRequestMessageReferenceDocument } from 'app/customer-request/entities/customer-request-message-reference-document.entity';
import { CustomerRequestMessage } from 'app/customer-request/entities/customer-request-message.entity';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { AuditHubEmailType } from 'app/customer-request/enums/audit-hub-email-type.enum';
import { CustomerRequestStatus } from 'app/customer-request/enums/customer-request-status.enum';
import { RequestMessageSenderType } from 'app/customer-request/enums/request-message-sender-type.enum';
import { CreateCustomerRequestMessageParams } from 'app/customer-request/interface/create-customer-request-message.type';
import { AuditHubMessageEvent } from 'app/customer-request/observables/events/audit-hub-message.event';
import { CustomerRequestCreatedEvent } from 'app/customer-request/observables/events/customer-request-created.event';
import { NewAuditEvidenceMessageEvent } from 'app/customer-request/observables/events/new-audit-evidence-message.event';
import { NewAuditMessageEvent } from 'app/customer-request/observables/events/new-audit-message.event';
import { RequestCommentCreatedEvent } from 'app/customer-request/observables/events/request-comment-created.event';
import { RequestStatusChangedBulkEmailEvent } from 'app/customer-request/observables/events/request-status-changed-bulk.event';
import { RequestStatusChangedEmailEvent } from 'app/customer-request/observables/events/request-status-changed-email.event';
import { UpdatedAuditEvidenceMessageEvent } from 'app/customer-request/observables/events/updated-audit-evidence-message.event';
import { CustomerRequestListViewRepository } from 'app/customer-request/repositories/customer-request-list-view.repository';
import { CustomerRequestMessageRepository } from 'app/customer-request/repositories/customer-request-message.repository';
import { CustomerRequestRepository } from 'app/customer-request/repositories/customer-request.repository';
import { CustomerRequestDetailsType } from 'app/customer-request/types/customer-request-details.type';
import { CustomerRequestEvidenceType } from 'app/customer-request/types/customer-request-evidence.type';
import { CustomerRequestListItemType } from 'app/customer-request/types/customer-request-list-item.type';
import { CustomerRequestMessageType } from 'app/customer-request/types/customer-request-message.type';
import { CustomerRequestType } from 'app/customer-request/types/customer-request.type';
import { CustomerRequestsType } from 'app/customer-request/types/customer-requests.type';
import { ModifiedAuditEvidenceEventPayload } from 'app/customer-request/types/modified-audit-evidence-event-payload.event';
import { NewAuditMesssageEventPayload } from 'app/customer-request/types/new-audit-message-event-payload.type';
import { NewAuditUploadedEvidenceEventPayload } from 'app/customer-request/types/new-audit-uploaded-evidence-event-payload.event';
import { RequestStatusChangeEventPayload } from 'app/customer-request/types/request-status-change-event-payload.type';
import { RequestStatusChangedBulkPayload } from 'app/customer-request/types/request-status-changed-bulk-payload.type';
import {
    SendAuditHubEmailEventParams,
    SendAuditHubEventPayload,
} from 'app/customer-request/types/send-audit-hub-email-event-params.type';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersionRepository } from 'app/users/policies/repositories/policy-version.repository';
import { PolicyVersionForEvent } from 'app/users/policies/types/policy-event.type';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { FrameworkTypeTags } from 'auditors/entities/framework-type-tags.map';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorsCoreService } from 'auditors/services/auditors-core.service';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AuditApiAuditorParamsType } from 'commons/types/audit-api-auditor-params.type';
import { PaginationType } from 'commons/types/pagination.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import { Uploader } from 'dependencies/uploader/uploader';
import { get, head, isEmpty, isNil, orderBy } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { Repository } from 'typeorm';

@Injectable()
export class CustomerRequestAppService extends AppService {
    constructor(
        private readonly auditorFrameworkRepository: AuditorFrameworkRepository,
        private readonly uploader: Uploader,
        private readonly auditorsCoreService: AuditorsCoreService,
        private readonly controlService: ControlService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly usersCoreService: UsersCoreService,
    ) {
        super();
    }

    /**
     * Get customer request details with evidence, messages and files (if any)
     * @param account Account
     * @param user User
     * @param customerRequestId Customer Request ID
     * @returns Customer request details
     */
    async getCustomerRequestDetails(
        account: Account,
        user: User,
        customerRequestId: number,
    ): Promise<CustomerRequestDetailsType> {
        const customerRequest = await this.customerRequestRepository.getRequest(customerRequestId);

        if (isNil(customerRequest)) {
            throw new NotFoundException(
                `Customer request with ID ${customerRequestId} was not found`,
            );
        }

        await this.validateUserAccessForAuditorFrameworkOrFail(
            user,
            customerRequest.auditorFrameworkId,
        );

        const auditorFramework = await this.auditorFrameworkRepository.findOne({
            where: { id: customerRequest.auditorFrameworkId },
        });

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${customerRequest.auditorFrameworkId} was not found`,
            );
        }

        const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

        if (isNil(framework)) {
            throw new NotFoundException(
                `There is no framework related to the auditor framework ${customerRequest.auditorFrameworkId}`,
            );
        }

        const messages: CustomerRequestMessageType[] = !isEmpty(
            customerRequest.customerRequestMessages,
        )
            ? await this.formatCustomerMessages(account, customerRequest.customerRequestMessages)
            : [];

        return {
            customerRequest,
            framework,
            messages: orderBy(messages, message => message.sentAt, 'desc'),
            companyName: account.companyName,
        };
    }

    async getAuditRequestDetails(
        account: Account,
        user: User,
        params: AuditApiAuditorParamsType,
        requestId: number,
    ): Promise<CustomerRequestDetailsType> {
        // validate if auditor has access to the requested audit
        await this.auditorFrameworkRepository.validateAuditByAuditIdAndAuditorEntryId(
            params.auditId,
            params.entryId,
        );

        const request = await this.getCustomerRequestDetails(account, user, requestId);

        if (request.customerRequest.auditorFrameworkId !== params.auditId) {
            throw new ForbiddenException(
                'Request is for a different audit',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }

        return request;
    }

    /**
     * Create customer requests
     * @param account Account
     * @param requestDto Request DTO
     * @returns List of customer requests
     */
    async createCustomerRequests(
        account: Account,
        user: User,
        requestDto: AuditorCreateCustomerRequestsRequestAuditorApiDto,
        auditFrameworkId: string,
    ): Promise<CustomerRequest[]> {
        try {
            const enabledControls = await this.controlService.getControlsWithReady();

            const customerRequests = requestDto.requests.map(request => {
                const customerRequest = new CustomerRequest();
                customerRequest.code = request.code;
                customerRequest.title = request.title;
                customerRequest.description = request.description;
                customerRequest.auditorFrameworkId = auditFrameworkId;

                if (!isEmpty(request.controlIds)) {
                    const controls = enabledControls.filter(control =>
                        request.controlIds.includes(control.id),
                    );
                    customerRequest.controls = controls;
                }
                // If Request has controls then status is IN_REVIEW
                customerRequest.status = isEmpty(customerRequest.controls)
                    ? CustomerRequestStatus.OUTSTANDING
                    : CustomerRequestStatus.IN_REVIEW;
                return customerRequest;
            });

            const savedCustomerRequests =
                await this.customerRequestRepository.save(customerRequests);

            const auditorFramework = await this.getAuditorFrameworkById(
                head(customerRequests)?.auditorFrameworkId,
            );

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);

            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            const isBulkUpload = true;
            const isWizardUpload = false;

            savedCustomerRequests.forEach(customRequest => {
                this._eventBus.publish(
                    new CustomerRequestCreatedEvent(
                        account,
                        user,
                        !isEmpty(customRequest.controls),
                        isBulkUpload,
                        isWizardUpload,
                        customRequest.description,
                        customRequest.id.toString(),
                        framework.name,
                    ),
                );
            });

            return savedCustomerRequests;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequests'),
                error.stack,
            );
            throw error;
        }
    }

    async validateCustomerRequestMessage(clientId: string, account: Account): Promise<void> {
        try {
            const auditorClient =
                await this.auditorsCoreService.getAuditorClientByAccountAndClientId(
                    clientId,
                    account,
                );

            if (isEmpty(auditorClient)) {
                /**
                 * Auditors can have access to multiple audits, and we need to validate
                 * that they are sending the messages to the right audit based on url params and
                 * access token data
                 */
                throw new NotFoundException();
            }
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Trying to send a customer message to wrong auditor client with id ${clientId}`,
                    account,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     * Create customer request message
     * @param account Account
     * @param user User
     * @param customerRequestId Customer request ID
     * @param requestDto Request DTO
     * @param files Files
     * @param sendEmail Flag to indicate if an email should be sent along with the message
     * @returns Customer request message
     */
    async createCustomerRequestMessage(
        params: CreateCustomerRequestMessageParams,
    ): Promise<CustomerRequestMessageType> {
        const {
            account,
            user,
            sentBy,
            customerRequestId,
            requestDto,
            files,
            sendEmail,
            policyVersionsForEvent,
            libraryDocumentVersions,
            externalEvidences,
            referenceDocuments,
        } = params;

        try {
            const customerRequestMessage = new CustomerRequestMessage();
            customerRequestMessage.message = requestDto.message;
            customerRequestMessage.sentBy = sentBy;

            const customerRequest = await this.customerRequestRepository.findOneBy({
                id: customerRequestId,
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(
                    `Customer request with ID ${customerRequestId} was not found`,
                );
            }

            customerRequestMessage.customerRequest = customerRequest;

            let newCustomerRequestMessage =
                await this.customerRequestMessageRepository.save(customerRequestMessage);

            const customerRequestMessageFiles: CustomerRequestMessageFile[] = [];
            if (!isNil(files) && !isEmpty(files)) {
                for (const file of files) {
                    const customerRequestMessageFile =
                        // eslint-disable-next-line no-await-in-loop
                        await this.createCustomerRequestFile(
                            account,
                            newCustomerRequestMessage.id,
                            file,
                        );

                    if (!isNil(customerRequestMessageFile)) {
                        customerRequestMessageFiles.push(customerRequestMessageFile);
                    }
                }
                newCustomerRequestMessage.customerRequestMessageFiles = customerRequestMessageFiles;
            }

            if (!isEmpty(policyVersionsForEvent)) {
                newCustomerRequestMessage = await this.linkPolicyVersions(
                    account,
                    this.customerRequestMessageRepository,
                    newCustomerRequestMessage,
                    policyVersionsForEvent,
                );
            }

            if (!isEmpty(libraryDocumentVersions)) {
                newCustomerRequestMessage.libraryDocumentVersions = libraryDocumentVersions;

                newCustomerRequestMessage =
                    await this.customerRequestMessageRepository.save(newCustomerRequestMessage);
            }

            if (!isEmpty(externalEvidences)) {
                newCustomerRequestMessage.externalEvidences = externalEvidences;
                newCustomerRequestMessage =
                    await this.customerRequestMessageRepository.save(newCustomerRequestMessage);
            }

            // Handle reference documents
            if (referenceDocuments && !isEmpty(referenceDocuments)) {
                const createdReferenceDocuments = await Promise.all(
                    referenceDocuments.map(refDoc => {
                        const referenceDocument = new CustomerRequestMessageReferenceDocument();
                        referenceDocument.documentType = refDoc.documentType;
                        referenceDocument.documentName = refDoc.documentName;
                        referenceDocument.customerRequestMessage = newCustomerRequestMessage;

                        return this.customerRequestMessageReferenceDocumentRepository.save(
                            referenceDocument,
                        );
                    }),
                );
                newCustomerRequestMessage.referenceDocuments = createdReferenceDocuments;
            }

            const messages = await this.formatCustomerMessages(account, [
                newCustomerRequestMessage,
            ]);

            if (sendEmail) {
                const emailType = params.emailType;
                const emailPayload = {
                    ...params?.emailPayload,
                    requestTitle: customerRequest.title,
                    requestCode: customerRequest.code,
                };
                await this.sendNewAuditMessageEmails(
                    user,
                    account,
                    newCustomerRequestMessage,
                    emailType,
                    emailPayload,
                );
            }
            messages.forEach(message => {
                this._eventBus.publish(
                    new AuditHubMessageEvent(account, message, customerRequest.id),
                );
            });

            const auditorFramework = await this.getAuditorFrameworkById(
                customerRequest.auditorFrameworkId,
            );

            const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

            this._eventBus.publish(
                new RequestCommentCreatedEvent(
                    account,
                    user,
                    customerRequest.description ?? '',
                    customerRequestId,
                    customerRequest.title,
                    hasRole(user, [Role.AUDITOR])
                        ? RequestMessageSenderType.AUDITOR
                        : RequestMessageSenderType.CUSTOMER,
                    newCustomerRequestMessage.message,
                    framework.name,
                ),
            );

            return head(messages);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestMessage'),
            );
            throw error;
        }
    }

    /**
     * Create customer request message file
     * @param account Account
     * @param customerRequestMessageId Customer request message ID
     * @param file File
     * @returns Customer request file
     */
    private async createCustomerRequestFile(
        account: Account,
        customerRequestMessageId: number,
        file: UploadedFileType,
    ): Promise<CustomerRequestMessageFile> {
        try {
            if (isNil(file?.buffer)) {
                throw new BadRequestException(`Buffer cannot be ${file?.buffer}`);
            }

            const uploadedFile = await this.uploader.uploadPrivateFile(
                file,
                UploadType.CUSTOMER_REQUEST,
                account.id,
            );

            const customerRequestmessage = await this.customerRequestMessageRepository.findOneBy({
                id: customerRequestMessageId,
            });

            const customerRequestMessageFile = new CustomerRequestMessageFile();
            customerRequestMessageFile.name = uploadedFile.fileName;
            customerRequestMessageFile.file = uploadedFile.key;
            customerRequestMessageFile.customerRequestMessage = customerRequestmessage;

            return await this.customerRequestMessageFileRepository.save(customerRequestMessageFile);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestFile'),
            );
            throw error;
        }
    }

    /**
     * Format list of customer request messages
     * @param account Account
     * @param customerRequestMessages Customer request message
     * @returns Formatted list of messages
     */
    async formatCustomerMessages(
        account: Account,
        customerRequestMessages: CustomerRequestMessage[],
    ): Promise<CustomerRequestMessageType[]> {
        if (isEmpty(customerRequestMessages)) {
            return [];
        }

        try {
            const messages: CustomerRequestMessageType[] = [];
            let usersByEntryId: Map<string, User> = new Map();

            const userEntries = customerRequestMessages
                .map(crm => crm.sentBy)
                .filter(val => !isEmpty(val) && val !== SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE);

            if (!isEmpty(userEntries)) {
                usersByEntryId = new Map(
                    (await this.userRepository.findByEntryIdsOrFail(userEntries)).map(u => [
                        u.entryId,
                        u,
                    ]),
                );
            }

            for (const customerRequestMessage of customerRequestMessages) {
                let message: CustomerRequestMessageType;
                let requestMessageAdapter: CustomerRequestMessageBaseAdapter;
                if (customerRequestMessage.sentBy === SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE) {
                    requestMessageAdapter = new DrataUpdateCustomerRequestMessageAdapter(
                        customerRequestMessage,
                    );
                    message = (
                        requestMessageAdapter as DrataUpdateCustomerRequestMessageAdapter
                    ).adapt();
                } else {
                    const user = usersByEntryId.get(customerRequestMessage.sentBy);

                    if (!user) {
                        continue;
                    }

                    requestMessageAdapter = new UserUpdateCustomerRequestMessageAdapter(
                        user,
                        customerRequestMessage,
                    );
                    message = (
                        requestMessageAdapter as UserUpdateCustomerRequestMessageAdapter
                    ).adapt();
                }

                if (!isNil(message)) {
                    messages.push(message);
                }
            }

            return messages;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('formatCustomerMessages'),
            );
            return [];
        }
    }

    /**
     * Send email indicating a new message has been added to a request
     * @param user User
     * @param account Account
     * @param message Message
     */
    private async sendNewAuditMessageEmails(
        user: User,
        account: Account,
        message: CustomerRequestMessage,
        emailType: AuditHubEmailType,
        emailPayload?: object,
    ): Promise<void> {
        try {
            const auditorFramework = await this.getAuditorFrameworkById(
                message.customerRequest.auditorFrameworkId,
            );

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);

            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            const product = await this.workspacesCoreService.getProductById(
                auditorFramework.productId,
            );

            const productName = get(product, 'name', '');

            const hasMultipleProducts =
                await this.workspacesCoreService.hasMultipleProducts(account);

            if (!isEmpty(auditorFramework.auditorFrameworkAuditors)) {
                if (hasRole(user, [Role.AUDITOR])) {
                    const adminsAndWorkspaceManagersWithFeatureOn =
                        await this.getAdminsAndWorkspaceManagersWithFeatureOn(
                            account,
                            auditHubEmailTypeToFeatureType[emailType],
                        );
                    for (const userWithFeatureOn of adminsAndWorkspaceManagersWithFeatureOn) {
                        const payload = this.emailEventPayloadBuilder({
                            emailType,
                            message,
                            framework,
                            productName,
                            ...emailPayload,
                            isRecieverAuditor: false,
                            clientId: '',
                        });
                        this.sendAuditHubEmailEvent({
                            emailType,
                            account,
                            sender: user,
                            receiver: userWithFeatureOn.user,
                            eventPayload: payload,
                            hasMultipleProducts,
                        });
                    }
                } else {
                    for (const auditorMapItem of auditorFramework.auditorFrameworkAuditors) {
                        const auditorUser =
                            // eslint-disable-next-line no-await-in-loop
                            await this.usersCoreService.getUserByEntryId(
                                auditorMapItem.auditorClient.entry.id,
                            );

                        const payload = this.emailEventPayloadBuilder({
                            emailType,
                            message,
                            framework,
                            productName,
                            ...emailPayload,
                            isRecieverAuditor: true,
                            clientId: auditorMapItem.auditorClient.id,
                            client: auditorMapItem.auditorClient,
                        });
                        this.sendAuditHubEmailEvent({
                            emailType,
                            account,
                            sender: user,
                            receiver: auditorUser,
                            eventPayload: payload,
                            hasMultipleProducts,
                        });
                    }
                }
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('sendNewAuditMessageEmails'),
            );
            throw error;
        }
    }

    private emailEventPayloadBuilder(
        params: Partial<SendAuditHubEventPayload>,
    ):
        | NewAuditMesssageEventPayload
        | RequestStatusChangeEventPayload
        | RequestStatusChangedBulkPayload
        | NewAuditUploadedEvidenceEventPayload
        | ModifiedAuditEvidenceEventPayload
        | undefined {
        const {
            emailType,
            message,
            framework,
            productName,
            statusFrom,
            statusTo,
            totalRequestsUpdateCount,
            auditorFrameworkId,
            isRecieverAuditor,
            clientId,
            client,
            requestTitle,
            requestCode,
        } = params;
        switch (emailType) {
            case AuditHubEmailType.MESSAGE_REQUEST:
                return {
                    message,
                    framework,
                    productName,
                    client,
                } as NewAuditMesssageEventPayload;

            case AuditHubEmailType.STATUS_CHANGE:
                return {
                    statusFrom,
                    statusTo,
                    message,
                    framework,
                    productName,
                    client,
                } as RequestStatusChangeEventPayload;

            case AuditHubEmailType.BULK_STATUS_CHANGE:
                return {
                    isRecieverAuditor,
                    totalRequestsUpdateCount,
                    auditorFrameworkId,
                    clientId,
                } as RequestStatusChangedBulkPayload;

            case AuditHubEmailType.ADDED_EVIDENCE_REQUEST:
                return {
                    message,
                    framework,
                    client,
                    productName,
                    requestTitle,
                    requestCode,
                } as NewAuditUploadedEvidenceEventPayload;

            case AuditHubEmailType.MODIFIED_EVIDENCE_REQUEST:
                return {
                    message,
                    framework,
                    client,
                    productName,
                    requestTitle,
                    requestCode,
                } as ModifiedAuditEvidenceEventPayload;
            default:
                break;
        }
    }

    private sendAuditHubEmailEvent(params: SendAuditHubEmailEventParams): void {
        const { emailType, account, sender, receiver, eventPayload, hasMultipleProducts } = params;
        if (isNil(eventPayload)) {
            this.logger.log(PolloMessage.msg('Event payload is empty'));
            return;
        }

        const { message, framework, productName, client } =
            eventPayload as NewAuditMesssageEventPayload;

        switch (emailType) {
            case AuditHubEmailType.MESSAGE_REQUEST:
                {
                    this._eventBus.publish(
                        new NewAuditMessageEvent(
                            account,
                            sender,
                            receiver,
                            message,
                            framework,
                            productName,
                            hasMultipleProducts,
                            client,
                        ),
                    );
                }
                break;
            case AuditHubEmailType.STATUS_CHANGE:
                {
                    const { statusFrom, statusTo } =
                        eventPayload as RequestStatusChangeEventPayload;

                    const isRecieverAuditor = hasRole(receiver, [Role.AUDITOR]) && !isNil(client);

                    this._eventBus.publish(
                        new RequestStatusChangedEmailEvent(
                            account,
                            sender,
                            receiver,
                            statusFrom,
                            statusTo,
                            framework,
                            productName,
                            hasMultipleProducts,
                            message,
                            { isRecieverAuditor, clientId: client?.id },
                        ),
                    );
                }
                break;
            case AuditHubEmailType.BULK_STATUS_CHANGE:
                {
                    const payload = eventPayload as RequestStatusChangedBulkPayload;

                    this._eventBus.publish(
                        new RequestStatusChangedBulkEmailEvent(
                            account,
                            sender,
                            receiver,
                            payload.totalRequestsUpdateCount,
                            payload.auditorFrameworkId,
                            payload.frameworkName,
                            payload.productName,
                            hasMultipleProducts,
                            {
                                isRecieverAuditor: payload.isRecieverAuditor,
                                clientId: payload.isRecieverAuditor ? payload.clientId : '',
                            },
                        ),
                    );
                }
                break;
            case AuditHubEmailType.ADDED_EVIDENCE_REQUEST:
                const { requestTitle, requestCode } =
                    eventPayload as NewAuditUploadedEvidenceEventPayload;
                this._eventBus.publish(
                    new NewAuditEvidenceMessageEvent(
                        account,
                        sender,
                        receiver,
                        message,
                        client,
                        framework,
                        productName,
                        requestTitle,
                        requestCode,
                    ),
                );
                break;
            case AuditHubEmailType.MODIFIED_EVIDENCE_REQUEST:
                const { requestTitle: modifiedRequestTitle, requestCode: modifiedRequestCode } =
                    eventPayload as ModifiedAuditEvidenceEventPayload;
                this._eventBus.publish(
                    new UpdatedAuditEvidenceMessageEvent(
                        account,
                        sender,
                        receiver,
                        message,
                        client,
                        framework,
                        productName,
                        modifiedRequestTitle,
                        modifiedRequestCode,
                    ),
                );
                break;
            default:
                break;
        }
    }

    /**
     * Get UserFeatures.User with role of admin/workspace manager and has the feature on
     * @param user User
     * @param account Account
     * @param message Message
     */
    private async getAdminsAndWorkspaceManagersWithFeatureOn(
        account: Account,
        featureType: FeatureType,
    ): Promise<UserFeature[]> {
        const users = await this.userRepository.getUsersByRoles([
            Role.ADMIN,
            Role.WORKSPACE_ADMINISTRATOR,
        ]);

        if (users?.length) {
            const usersIds = users.map(u => u.id);
            return this.userFeatureRepository.getUserFeatures(usersIds, featureType);
        } else {
            return [];
        }
    }

    /**
     * Get auditor framework by ID
     * @param auditorFrameworkId Auditor Framework ID
     * @returns Auditor Framework
     */
    private getAuditorFrameworkById(
        // @AUDIT-REFACTOR: TODO rename to auditId
        auditorFrameworkId: string,
    ): Promise<Audit> {
        return this.auditorFrameworkRepository.findOne({
            where: {
                id: auditorFrameworkId,
            },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                    auditorClient: 'auditorFrameworkAuditors.auditorClient',
                    entry: 'auditorClient.entry',
                },
            },
        });
    }

    /**
     * Get framework entity object corresponding to the given auditor framework
     * @param account Account
     * @param auditorFramework Auditor Framework
     * @returns Framework
     */
    private async getFrameworkByAuditorFramework(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to audit
        auditorFramework: Audit,
    ): Promise<Framework> {
        const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);

        return this.frameworksCoreService.getEnabledFrameworkByTag(
            frameworkTag,
            auditorFramework.productId,
            auditorFramework.customFrameworkId,
        );
    }

    /**
     * Link Policy Versions to Customer Request Message
     * @param account Account
     * @param customerRequestMessageRepository Customer Request Message repository
     * @param customerRequestMessage Customer Request Message
     * @param policyVersionsForEvent Policy Versions array
     */
    private async linkPolicyVersions(
        account: Account,
        customerRequestMessageRepository: Repository<CustomerRequestMessage>,
        customerRequestMessage: CustomerRequestMessage,
        policyVersionsForEvent: PolicyVersionForEvent[],
    ): Promise<CustomerRequestMessage> {
        const policyVersions = await this.policyVersionRepository.findByIds(
            policyVersionsForEvent.map(p => p.id),
        );
        customerRequestMessage.policyVersions = policyVersions;

        return customerRequestMessageRepository.save(customerRequestMessage);
    }

    /**
     * get audit request list
     * @param entryId
     * @param accountId
     * @param auditId
     * @param auditFirm
     * @param requestDto
     * @returns
     */
    async getAuditRequestsOrFail(
        params: AuditApiAuditorParamsType,
        account: Account,
        requestDto: PaginationRequestDto,
    ): Promise<PaginationType<CustomerRequestListItemType>> {
        // validate if auditor has access to the requested audit
        const audit = await this.auditorFrameworkRepository.getAuditByIdAndAuditorEntryIdOrFail(
            params.auditId,
            params.entryId,
        );

        const viewDto: CustomerRequestListRequestDto = {
            q: null,
            auditId: params.auditId,
            page: requestDto.page,
            limit: requestDto.limit,
            userIds: [],
        };

        const auditRequestList = await this.getCustomerRequestList(
            account,
            viewDto,
            true,
            true,
            audit,
        );

        return auditRequestList.requests;
    }

    private async getCustomerRequestList(
        account: Account,
        requestDto: CustomerRequestListRequestDto,
        isUserAuditor: boolean,
        isAuditApi: boolean,
        // @AUDIT-REFACTOR: TODO rename to audit
        auditorFramework: Audit,
    ): Promise<CustomerRequestsType> {
        const customerRequests =
            await this.customerRequestRepository.listCustomerRequests(requestDto);

        const totalUnreadMessages = isAuditApi
            ? 0
            : await this.customerRequestListViewRepository.getTotalUnreadMessagesByAudit(
                  isUserAuditor,
                  requestDto.auditId,
              );

        const data: CustomerRequestListItemType[] = [];
        if (!isEmpty(customerRequests?.data)) {
            const enabledControls =
                await this.controlService.getControlsWithReadyForCustomerRequests();

            customerRequests.data.forEach(item => {
                const evidenceControls: CustomerRequestEvidenceType[] = enabledControls
                    .filter(
                        enabledControl =>
                            item.controlCodes.includes(enabledControl.code) &&
                            enabledControl.products[0]?.id === auditorFramework.productId,
                    )
                    .map(filteredControl => {
                        return {
                            id: filteredControl.id,
                            code: filteredControl.code,
                            name: filteredControl.name,
                            description: filteredControl.description,
                            isReady: filteredControl.controlIsReady.isReady,
                        };
                    });

                const customerRequest: CustomerRequestListItemType = {
                    id: item.requestId,
                    code: item.requestCode,
                    title: item.requestTitle,
                    description: item.requestDescription,
                    status: item.requestStatus,
                    unreadMessages: isUserAuditor
                        ? item.auditorUnreadMessages
                        : item.customerUnreadMessages,
                    controls: evidenceControls,
                    auditorFrameworkId: item.auditorFrameworkId,
                };

                data.push(customerRequest);
            });
        }

        return {
            totalUnreadMessages,
            requests: {
                data,
                page: customerRequests.page,
                limit: customerRequests.limit,
                total: customerRequests.total,
            },
        };
    }

    /** Create audit requests
     * @param auditFirm Audit Firm
     * @param entryId Auditor's entry ID
     * @param accountId Tenant's account ID
     * @param auditId Auditor ID
     * @param requestDto Request DTO
     */
    async createAuditRequestsOrFail(
        user: User,
        account: Account,
        params: AuditApiAuditorParamsType,
        requestDto: AuditorCreateCustomerRequestsRequestAuditorApiDto,
    ): Promise<CustomerRequest[]> {
        // validate if auditor has access to the requested audit
        await this.auditorFrameworkRepository.validateAuditByAuditIdAndAuditorEntryId(
            params.auditId,
            params.entryId,
        );

        return this.createCustomerRequests(account, user, requestDto, params.auditId);
    }

    /**
     * @returns Created request message
     */
    async createCustomerRequestMessageByAuditFirmOrFail(
        files: UploadedFileType[],
        account: Account,
        user: User,
        auditId: string,
        requestId: number,
        requestDto: CreateCustomerRequestMessageRequestAuditorApiDto,
    ): Promise<CustomerRequestMessageType> {
        // validate if auditor has access to the requested audit
        await this.auditorFrameworkRepository.validateAuditByAuditIdAndAuditorEntryId(
            auditId,
            user.entryId,
        );

        const auditorRequest = await this.customerRequestRepository.findOneOrFail({
            where: { id: requestId },
        });

        if (auditorRequest.auditorFrameworkId !== auditId) {
            throw new ForbiddenException(
                'Request is for a different audit',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }

        return this.createCustomerRequestMessage({
            account,
            user,
            sentBy: user.entryId,
            customerRequestId: requestId,
            requestDto,
            files,
            sendEmail: true,
            emailType: AuditHubEmailType.MESSAGE_REQUEST,
            referenceDocuments: requestDto.referenceDocuments,
        });
    }

    /**
     * Get paginated list of audit request messages.
     * @param params Request Parameters
     * @returns Paginated list of request messages
     */
    async getCustomerRequestMessageByAuditFirmOrFail(
        user: User,
        account: Account,
        auditId: string,
        requestId: number,
        requestDto: RequestMessagesListRequestAuditorApiDto,
    ): Promise<PaginationType<CustomerRequestMessageType>> {
        // validate if auditor has access to the requested audit
        await this.auditorFrameworkRepository.validateAuditByAuditIdAndAuditorEntryId(
            auditId,
            user.entryId,
        );

        const auditorRequest = await this.customerRequestRepository.findOneOrFail({
            where: { id: requestId },
            withDeleted: true,
        });
        // Check if the request is soft deleted
        if (auditorRequest.deletedAt) {
            throw new NotFoundException(`Customer request with ID ${requestId} has been deleted`);
        }

        if (auditorRequest.auditorFrameworkId !== auditId) {
            throw new ForbiddenException(
                'Request is for a different audit',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }

        const paginatedMessages =
            await this.customerRequestMessageRepository.getCustomerRequestMessagesByRequestId(
                requestId,
                requestDto,
            );

        const formattedMessages = await this.formatCustomerMessages(
            account,
            paginatedMessages.data,
        );

        return {
            data: formattedMessages,
            page: paginatedMessages.page,
            limit: paginatedMessages.limit,
            total: paginatedMessages.total,
        };
    }

    /**
     * Validate if user should be able to get auditor framework data
     * @param user User
     * @param auditorFrameworkId Auditor framework ID
     */
    private async validateUserAccessForAuditorFrameworkOrFail(
        user: User,
        auditorFrameworkId: string,
    ): Promise<void> {
        if (hasRole(user, [Role.AUDITOR])) {
            const auditorFramework = await this.getAuditorFrameworkById(auditorFrameworkId);

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${auditorFrameworkId} was not found`,
                );
            }

            const auditorUsers = auditorFramework.auditorFrameworkAuditors
                .filter(item => !isNil(item.auditorClient))
                .map(item => item.auditorClient.entry.id);

            if (!isEmpty(auditorUsers) && !auditorUsers.includes(user.entryId)) {
                throw new ForbiddenException(
                    `${user.email} is not allowed to get data for auditor framework: ${auditorFrameworkId}`,
                    ErrorCode.AUDITOR_CLIENT_FORBIDDEN,
                );
            }
        }
    }
    /**
     * put audit request status
     * @param account Tenant's account
     * @param user Auditor user
     * @param auditFirm
     * @param params Request Parameters
     * @param requestDto
     * @returns
     */
    async putAuditRequestsStatusOrFail(
        account: Account,
        user: User,
        auditId: string,
        requestId: number,
        requestDto: UpdateCustomerRequestStatusRequestAuditorApiDto,
    ): Promise<CustomerRequestType> {
        // validate if auditor has access to the requested audit
        await this.auditorFrameworkRepository.validateAuditByAuditIdAndAuditorEntryId(
            auditId,
            user.entryId,
        );

        const auditorRequest = await this.customerRequestRepository.findOneOrFail({
            where: { id: requestId },
        });

        if (auditorRequest.auditorFrameworkId !== auditId) {
            throw new ForbiddenException(
                'Request is for a different audit',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }

        auditorRequest.status = requestDto.status;

        const updatedRequest = await this.customerRequestRepository.save(auditorRequest);
        if (!isNil(requestDto.message)) {
            const customerRequestMessage: CreateCustomerRequestMessageParams = {
                account,
                user,
                customerRequestId: requestId,
                requestDto: { message: requestDto.message },
                files: [],
                sendEmail: false,
                sentBy: user.entryId,
                referenceDocuments: undefined,
            };
            await this.createCustomerRequestMessage(customerRequestMessage);
        }
        return {
            id: updatedRequest.id,
            code: updatedRequest.code,
            title: updatedRequest.title,
            description: updatedRequest.description,
            status: requestDto.status,
            auditorFrameworkId: updatedRequest.auditorFrameworkId,
        };
    }

    private get customerRequestRepository(): CustomerRequestRepository {
        return this.getCustomTenantRepository(CustomerRequestRepository);
    }

    private get customerRequestMessageRepository(): CustomerRequestMessageRepository {
        return this.getCustomTenantRepository(CustomerRequestMessageRepository);
    }

    private get customerRequestMessageFileRepository(): Repository<CustomerRequestMessageFile> {
        return this.getTenantRepository(CustomerRequestMessageFile);
    }

    private get customerRequestMessageReferenceDocumentRepository(): Repository<CustomerRequestMessageReferenceDocument> {
        return this.getTenantRepository(CustomerRequestMessageReferenceDocument);
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }

    private get policyVersionRepository(): PolicyVersionRepository {
        return this.getCustomTenantRepository(PolicyVersionRepository);
    }

    private get customerRequestListViewRepository(): CustomerRequestListViewRepository {
        return this.getCustomTenantRepository(CustomerRequestListViewRepository);
    }
}
