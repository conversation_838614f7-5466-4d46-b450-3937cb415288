import { AuditorFrameworkType, RequirementIndexCategoryLabel } from '@drata/enums';
import { CustomerRequestListRequestDto } from 'app/customer-request/dtos/customer-request-list-request.dto';
import { CustomerRequestListView } from 'app/customer-request/entities/customer-request-list-view.entity';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { CustomerRequestStatus } from 'app/customer-request/enums/customer-request-status.enum';
import { CustomerRequestGroupByCategoryType } from 'app/customer-request/types/customer-request-group-by-category.type';
import { Account } from 'auth/entities/account.entity';
import { Role } from 'commons/enums/users/role.enum';
import { fqtn } from 'commons/helpers/database.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { isEmpty, isNil } from 'lodash';

@CustomRepository(CustomerRequest)
export class CustomerRequestRepository extends BaseRepository<CustomerRequest> {
    async getRequest(requestId: number): Promise<CustomerRequest | null> {
        return this.createQueryBuilder('CustomerRequest')
            .leftJoinAndSelect('CustomerRequest.controls', 'Control')
            .leftJoinAndSelect('CustomerRequest.customerRequestMessages', 'CustomerRequestMessage')
            .leftJoin(
                'CustomerRequestMessage.customerRequestMessageFiles',
                'CustomerRequestMessageFile',
            )
            .leftJoin('CustomerRequestMessage.policyVersions', 'PolicyVersion')
            .leftJoin('CustomerRequestMessage.libraryDocumentVersions', 'LibraryDocumentVersion')
            .leftJoin('CustomerRequestMessage.externalEvidences', 'ExternalEvidence')
            .leftJoin('CustomerRequestMessage.referenceDocuments', 'ReferenceDocument')
            .leftJoin(
                'LibraryDocumentVersion.libraryDocumentVersionMetadata',
                'LibraryDocumentVersionMetadata',
            )
            .addSelect([
                'CustomerRequestMessageFile.id',
                'CustomerRequestMessageFile.name',
                'PolicyVersion.id',
                'PolicyVersion.originalFileName',
                'LibraryDocumentVersion.id',
                'LibraryDocumentVersionMetadata.originalFileName',
                'ExternalEvidence.id',
                'ExternalEvidence.name',
                'ExternalEvidence.url',
                'ReferenceDocument.id',
                'ReferenceDocument.documentType',
                'ReferenceDocument.documentName',
            ])
            .where('CustomerRequest.id = :requestId', { requestId })
            .getOne();
    }

    async getRequestWithMessages(requestId: number): Promise<CustomerRequest | null> {
        return this.createQueryBuilder('CustomerRequest')
            .select(['CustomerRequest.id', 'CustomerRequest.auditorFrameworkId'])
            .leftJoinAndSelect('CustomerRequest.customerRequestMessages', 'CustomerRequestMessage')
            .leftJoinAndSelect('CustomerRequestMessage.referenceDocuments', 'ReferenceDocument')
            .leftJoin(
                'CustomerRequestMessage.customerRequestMessageFiles',
                'CustomerRequestMessageFile',
            )
            .leftJoin('CustomerRequestMessage.policyVersions', 'PolicyVersion')
            .leftJoin('CustomerRequestMessage.libraryDocumentVersions', 'LibraryDocumentVersion')
            .leftJoin('CustomerRequestMessage.externalEvidences', 'ExternalEvidence')
            .leftJoin(
                'LibraryDocumentVersion.libraryDocumentVersionMetadata',
                'LibraryDocumentVersionMetadata',
            )
            .leftJoin('LibraryDocumentVersion.libraryDocument', 'LibraryDocument')
            .addSelect([
                'CustomerRequestMessageFile.id',
                'CustomerRequestMessageFile.name',
                'CustomerRequestMessageFile.file',
                'PolicyVersion.id',
                'PolicyVersion.originalFileName',
                'LibraryDocumentVersion.id',
                'LibraryDocumentVersion.type',
                'LibraryDocumentVersion.source',
                'LibraryDocumentVersionMetadata.originalFileName',
                'LibraryDocument.name',
                'ExternalEvidence.id',
                'ExternalEvidence.name',
                'ExternalEvidence.url',
            ])
            .where('CustomerRequest.id = :requestId', { requestId })
            .getOne();
    }

    async getRequestWithControls(requestId: number): Promise<CustomerRequest | null> {
        return this.createQueryBuilder('CustomerRequest')
            .select(['CustomerRequest.id', 'CustomerRequest.auditorFrameworkId'])
            .leftJoinAndSelect(
                'CustomerRequest.controls',
                'Control',
                'Control.enabledAt IS NOT NULL',
            )
            .where('CustomerRequest.id = :requestId', { requestId })
            .getOne();
    }

    async getRequestIdsThatMatchesOwnerIds(ownerIds: number[]): Promise<number[]> {
        if (isEmpty(ownerIds)) {
            return [];
        }
        const requests = await this.createQueryBuilder('CustomerRequest')
            .select(['CustomerRequest.id'])
            .leftJoin('CustomerRequest.owners', 'CustomerRequestOwner')
            .where('CustomerRequestOwner.id  IN(:...ownerIds)', {
                ownerIds,
            })
            .getMany();

        return requests.map(request => request.id);
    }

    async getRequestIdsThatMatchesAuditorId(auditorId: string): Promise<number[]> {
        const requests = await this.createQueryBuilder('CustomerRequest')
            .select(['CustomerRequest.id'])
            .where('CustomerRequest.auditorFrameworkId = :auditorId', {
                auditorId,
            })
            .getMany();

        return requests.map(request => request.id);
    }

    async getRequestIdsThatMatchesRequestStatus(status: CustomerRequestStatus): Promise<number[]> {
        const requests = await this.createQueryBuilder('CustomerRequest')
            .select(['CustomerRequest.id'])
            .where('CustomerRequest.status = :status', {
                status,
            })
            .getMany();

        return requests.map(request => request.id);
    }

    async getRequestOwnersAssigned(isOwned: boolean): Promise<number[]> {
        const query = this.createQueryBuilder('CustomerRequest')
            .leftJoin('CustomerRequest.owners', 'CustomerRequestOwners')
            .select('CustomerRequest.id');
        if (isOwned) {
            query.where('CustomerRequestOwners.id IS NOT NULL');
        } else {
            query.where('CustomerRequestOwners.id IS NULL');
        }
        const requests = await query.getMany();
        return requests.map(request => request.id);
    }

    async getAuditRequestIdsByUserId(userId: number): Promise<CustomerRequest[]> {
        return this.createQueryBuilder('CustomerRequest')
            .innerJoinAndSelect('CustomerRequest.owners', 'owner', 'owner.id = :userId', { userId })
            .select(['CustomerRequest.id'])
            .where('owner.id = :userId', { userId })
            .getMany();
    }

    async getCustomerRequest(requestId: number): Promise<CustomerRequest | null> {
        return this.createQueryBuilder('CustomerRequest')
            .where('CustomerRequest.id = :requestId', { requestId })
            .getOne();
    }

    async getCustomerRequestBasicInfoByAuditorFrameworkId(
        auditorFrameworkId: string,
        excludeIds: number[],
    ): Promise<CustomerRequest[]> {
        const query = this.createQueryBuilder('CustomerRequest')
            .select([
                'CustomerRequest.id',
                'CustomerRequest.code',
                'CustomerRequest.title',
                'CustomerRequest.auditorFrameworkId',
            ])
            .where('CustomerRequest.auditorFrameworkId = :auditorFrameworkId', {
                auditorFrameworkId,
            });

        if (!isNil(excludeIds)) {
            query.andWhere('CustomerRequest.id NOT IN (:...excludeIds)', {
                excludeIds,
            });
        }

        return query.getMany();
    }

    async getRequestsByAuditorFramework(auditorFrameworkId: string): Promise<CustomerRequest[]> {
        return this.find({
            where: {
                auditorFrameworkId,
            },
        });
    }

    async getAuditCustomerRequestByIdWithControlsOrFail(
        customerRequestId: number,
        auditId: string,
    ): Promise<CustomerRequest> {
        return this.findOneOrFail({
            where: {
                id: customerRequestId,
                auditorFrameworkId: auditId,
            },
            relations: {
                controls: true,
            },
        });
    }

    async getCustomerRequestByIdWithControlsAndTestInstancesOrFail(
        customerRequestId: number,
    ): Promise<CustomerRequest> {
        return this.findOneOrFail({
            where: { id: customerRequestId },
            relations: {
                controls: {
                    controlTestInstances: true,
                },
            },
        });
    }

    async getRequestsWithFrameworksAndControlsByAuditId(
        auditId: string,
        frameworkType: AuditorFrameworkType,
        productId: number,
        account: Account,
    ): Promise<CustomerRequestGroupByCategoryType[]> {
        const results = await this.query(
            `SELECT
                cr.id AS cr_id,
                cr.code AS cr_code,
                cr.title AS cr_title,
                cr.description AS cr_description,
                cr.status AS cr_status,
                ri.category AS cr_category,

                c.id AS control_id,
                c.name AS control_name,
                c.description AS control_description,

                vc.is_ready AS control_is_ready,

                cti.id AS test_instance_id

            FROM ${fqtn(account.databaseName, 'customer_request')} cr
            LEFT JOIN ${fqtn(
                account.databaseName,
                'customer_request_evidence_map',
            )} cm ON cm.fk_customer_request_id = cr.id
            LEFT JOIN ${fqtn(account.databaseName, 'control')} c ON c.id = cm.fk_control_id
            LEFT JOIN ${fqtn(account.databaseName, 'vw_control_flags_index')} vc ON vc.control_id = c.id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'controls_control_test_instances_v2_map',
            )} cctim ON cctim.fk_control_id = c.id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'control_test_instance',
            )} cti ON cti.id = cctim.fk_control_test_instance_id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'controls_requirements_map',
            )} crm ON crm.fk_control_id = c.id
            LEFT JOIN ${fqtn(account.databaseName, 'requirement')} r ON r.id = crm.fk_requirement_id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'requirement_index',
            )} ri ON ri.fk_requirement_id = r.id
            LEFT JOIN ${fqtn(account.databaseName, 'framework')} f ON f.id = ri.fk_framework_id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'products_frameworks_map',
            )} pf ON pf.fk_framework_id = f.id
            LEFT JOIN ${fqtn(
                account.databaseName,
                'auditor_framework_type',
            )} a ON a.fk_framework_id = f.id

            WHERE cr.fk_auditor_framework_id = ?
                AND pf.fk_product_id = ?
                AND a.framework_type = ?
                AND cr.code = r.name

            ORDER BY cr.id, c.id, cti.id;
            `,
            [auditId, productId, frameworkType],
        );

        // Transform raw results into CustomerRequestGroupByCategoryType[]
        const categoryMap = new Map<string, CustomerRequestGroupByCategoryType>();

        // Return unsorted results for the service to handle sorting
        results.forEach(row => {
            const category = RequirementIndexCategoryLabel[row.cr_category];

            if (!categoryMap.has(category)) {
                categoryMap.set(category, {
                    category,
                    requests: [],
                });
            }

            const categoryGroup = categoryMap.get(category);

            let request = categoryGroup?.requests.find(r => r.id === row.cr_id);
            if (!request) {
                request = {
                    id: row.cr_id,
                    code: row.cr_code,
                    title: row.cr_title,
                    description: row.cr_description,
                    status: CustomerRequestStatus[row.cr_status],
                    controls: [],
                };
                categoryGroup?.requests.push(request);
            }

            if (!row.control_id) return;

            let control = request.controls.find(c => c.name === row.control_name);
            if (!control) {
                control = {
                    name: row.control_name || '',
                    description: row.control_description || '',
                    automated: false, // Start as false, will be updated if test instances found
                    isReady: false,
                };
                request.controls.push(control);
            }

            // Update automated to true if we find any test instance for this control
            if (row.test_instance_id && !control.automated) {
                control.automated = true;
            }

            if (row.control_is_ready && !control.isReady) {
                control.isReady = true;
            }
        });

        // Convert to array and return
        return Array.from(categoryMap.values());
    }

    async listCustomerRequests(
        dto: CustomerRequestListRequestDto,
    ): Promise<PaginationType<CustomerRequestListView>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            auditId,
        } = dto;

        const queryBuilder = this.createQueryBuilder('CustomerRequest')
            .select([
                'CustomerRequest.id as requestId',
                'CustomerRequest.code as requestCode',
                'CustomerRequest.title as requestTitle',
                'CustomerRequest.description as requestDescription',
                'CustomerRequest.status as requestStatus',
                'CustomerRequest.auditorFrameworkId as auditorFrameworkId',
            ])
            .addSelect(subQuery => {
                return subQuery
                    .select('COUNT(*)')
                    .from('customer_request_message', 'CustomerRequestMessage')
                    .where('CustomerRequestMessage.fk_customer_request_id = CustomerRequest.id')
                    .andWhere('CustomerRequestMessage.read_at IS NULL')
                    .andWhere('CustomerRequestMessage.deleted_at IS NULL')
                    .andWhere(
                        `CustomerRequestMessage.sent_by NOT IN (${this.createAuditorUserSubquery()})`,
                    );
            }, 'auditorUnreadMessages')
            .addSelect(subQuery => {
                return subQuery
                    .select('COUNT(*)')
                    .from('customer_request_message', 'CustomerRequestMessage')
                    .where('CustomerRequestMessage.fk_customer_request_id = CustomerRequest.id')
                    .andWhere('CustomerRequestMessage.read_at IS NULL')
                    .andWhere('CustomerRequestMessage.deleted_at IS NULL')
                    .andWhere(
                        `CustomerRequestMessage.sent_by IN (${this.createAuditorUserSubquery()})`,
                    );
            }, 'customerUnreadMessages')
            .where('CustomerRequest.deleted_at IS NULL')
            .andWhere('CustomerRequest.fk_auditor_framework_id = :auditId', { auditId });

        const countQueryBuilder = this.createQueryBuilder('CustomerRequest')
            .where('CustomerRequest.deleted_at IS NULL')
            .andWhere('CustomerRequest.fk_auditor_framework_id = :auditId', { auditId });

        queryBuilder.orderBy('CustomerRequest.created_at', 'DESC');

        const skip = getSkip(page, limit);
        queryBuilder.offset(skip).limit(limit);

        const [data, total] = await Promise.all([
            queryBuilder.getRawMany(),
            countQueryBuilder.getCount(),
        ]);
        if (data.length === 0) {
            return { data: [], page, limit, total };
        }

        const requestIds = data.map(row => row.requestId);
        const controlCodesMap = await this.getControlCodesBatch(requestIds);

        const enrichedData = data.map(row => {
            const entity = this.mapRawToEntity(row);
            entity.controlCodes = controlCodesMap.get(row.requestId) || [];
            return entity;
        });

        return {
            data: enrichedData,
            page,
            limit,
            total,
        };
    }

    private createAuditorUserSubquery(): string {
        return this.createQueryBuilder()
            .select('DISTINCT User.fk_entry_id')
            .from('user', 'User')
            .innerJoin('user_role', 'UserRole', 'User.id = UserRole.fk_user_id')
            .where(`UserRole.role = ${Role.AUDITOR}`)
            .getQuery();
    }

    private async getControlCodesBatch(requestIds: number[]): Promise<Map<number, string[]>> {
        if (requestIds.length === 0) {
            return new Map();
        }

        const results = await this.createQueryBuilder()
            .select('CustomerRequestEvidenceMap.fk_customer_request_id', 'requestId')
            .addSelect("GROUP_CONCAT(Control.code SEPARATOR ',')", 'codes')
            .from('customer_request_evidence_map', 'CustomerRequestEvidenceMap')
            .leftJoin(
                'control',
                'Control',
                'CustomerRequestEvidenceMap.fk_control_id = Control.id AND Control.enabled_at IS NOT NULL AND Control.deleted_at IS NULL',
            )
            .where('CustomerRequestEvidenceMap.fk_customer_request_id IN (:...requestIds)', {
                requestIds,
            })
            .groupBy('CustomerRequestEvidenceMap.fk_customer_request_id')
            .getRawMany();

        const map = new Map<number, string[]>();
        results.forEach((row: any) => {
            map.set(row.requestId, row.codes ? row.codes.split(',') : []);
        });
        return map;
    }

    private mapRawToEntity(row: any): CustomerRequestListView {
        const entity = new CustomerRequestListView();
        entity.requestId = row.requestId;
        entity.requestCode = row.requestCode;
        entity.requestTitle = row.requestTitle;
        entity.requestDescription = row.requestDescription;
        entity.requestStatus = row.requestStatus;
        entity.auditorFrameworkId = row.auditorFrameworkId;
        entity.controlCodes = row.controlCodes ? row.controlCodes.split(',') : [];
        entity.auditorUnreadMessages = parseInt(row.auditorUnreadMessages) || 0;
        entity.customerUnreadMessages = parseInt(row.customerUnreadMessages) || 0;
        return entity;
    }
}
