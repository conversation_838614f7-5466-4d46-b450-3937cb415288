import {
    ComplianceCheckStatus,
    ComplianceCheckType,
    EmploymentStatus,
    ErrorCode,
    SortDir,
    SortType,
} from '@drata/enums';
import { IdentityServiceUser } from 'app/apis/interfaces/identity-service-user.interface';
import { AuditPersonnelTypeToEmployeeStatus } from 'app/audit-hub/constants/audit.constants';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Device } from 'app/devices/entities/device.entity';
import { PersonnelWithFailedMfaRequestDto } from 'app/users/dtos/personnel-with-failed-mfa-request.dto';
import { UserControlsRequestDto } from 'app/users/dtos/user-controls-request.dto';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { User } from 'app/users/entities/user.entity';
import { GroupPersonnel } from 'app/users/groups/entities/group-personnel.entity';
import { PersonnelBulkActionRequestDto } from 'app/users/personnel/dtos/personnel-bulk-action-request.dto';
import { PersonnelForApTaskRequestDto } from 'app/users/personnel/dtos/personnel-for-ap-task.request.dto';
import { PersonnelRequestDto } from 'app/users/personnel/dtos/personnel-request.dto';
import { PersonnelWithDevicesRequestDto } from 'app/users/personnel/dtos/personnel-with-devices-request.dto';
import { ComplianceCheckTypePropNames } from 'app/users/personnel/entities/compliance-check-type-prop-names.map';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelExpand } from 'app/users/personnel/enums/personnel-expand.enum';
import {
    getAuditPersonnelPeriodWhereConditions,
    isSelectionPresent,
    removeRedundantColumnSelects,
    resolveAllUserSearchQuery,
    resolveFilters,
    SORTING_BY_COMPLIANCE_CHECK_IDENTIFIER,
} from 'app/users/personnel/helpers/query.helper';
import {
    COMPLIANCE_CHECK,
    PERSONNEL,
    USER,
    USER_DOCUMENT,
} from 'app/users/personnel/repositories/personnel-repository.constants';
import { ExtendedPersonnel } from 'app/users/personnel/types/extended-personnel.type';
import { FindPersonnelOptions } from 'app/users/personnel/types/find-personnel-options.type';
import { PersonnelListRequestType } from 'app/users/personnel/types/personnel-list-request.type';
import { PersonnelStatusStats } from 'app/users/personnel/types/personnel-status-stats.type';
import { AuditorPersonnelDateRangeType } from 'auditors/types/auditor-personnel-date-range.type';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { AuditPersonnelType } from 'commons/enums/audit-personnel-type.enum';
import { AuditorPersonnelHandler } from 'commons/enums/auditors/auditor-personnel-handlers.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { BackgroundCheckStatus } from 'commons/enums/background-check-status.enum';
import { BackgroundCheckType } from 'commons/enums/background-check/background-check-type.enum';
import { ComplianceCheckTypeOptions } from 'commons/enums/compliance-check/compliance-check-type-options.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { MobileDeviceManagementSourceType } from 'commons/enums/mdm-source-type.enum';
import { TrainingCampaignType } from 'commons/enums/training-campaign-type.enum';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import {
    applyCursorFilters,
    getCursorPageAutoIncrementId,
    setupCursorQueryAutoIncrementId,
} from 'commons/helpers/cursor-repository.helper';
import { field, like } from 'commons/helpers/database.helper';
import { isTimeToSendEmail } from 'commons/helpers/date.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import {
    currentStatuses as CURRENT_STATUSES,
    strictFormerStatuses,
} from 'commons/helpers/personnel.helper';
import { addProductClause, getProductId } from 'commons/helpers/products.helper';
import { promiseAllInBatches } from 'commons/helpers/promise.helper';
import { promiseEmptyArray } from 'commons/helpers/query.helper';
import { getSha256HashString } from 'commons/helpers/security.helper';
import { getSortDir } from 'commons/helpers/sort.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import { CursorPage } from 'commons/types/cursor-page.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { compact, isEmpty, isNil, map, merge, set } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import {
    Brackets,
    In,
    IsNull,
    Not,
    ObjectLiteral,
    SelectQueryBuilder,
    type FindOptionsWhere,
} from 'typeorm';

@CustomRepository(Personnel)
export class PersonnelRepository extends BaseRepository<Personnel> {
    /**
     *
     * @param id
     */
    async findOneByPersonnelIdOrFail(id: number): Promise<Personnel> {
        // begin building the query
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('Connection.connectionProviderTypes', 'ConnectionProviderType')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('Personnel.id = :id', { id });

        const personnel = await query.getOne();

        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        await this.setPersonnelDeviceWithDocuments(personnel);

        await this.setPersonnelTrainingDocuments([personnel]);

        return personnel;
    }

    /**
     *
     * @param {number} id
     */
    async findOneByPersonnelIdForComplianceTestsOrFail(id: number): Promise<Personnel> {
        // Optimized query that consolidates multiple separate queries into one
        // Using SELECT * for Personnel and User entities for simplicity and maintainability
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            // Consolidate device query to eliminate N+1; do NOT join Device.documents here:
            // - v1 DeviceResponsePublicDto doesn’t serialize documents
            // - internal flows attach documents in service via getDocumentsAndEdrDevicesOptimized()
            .leftJoinAndSelect('Personnel.devices', 'Device', 'Device.deletedAt IS NULL')
            .leftJoinAndSelect('Device.complianceChecks', 'DeviceComplianceChecks')
            .leftJoinAndSelect('Device.identifiers', 'DeviceIdentifiers')
            // Consolidate user identities to eliminate N+1
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            // Keep provider types: used by ConnectionResponsePublicDto.providerTypes and hasIdp via connectionHasProviderType()
            .leftJoinAndSelect('Connection.connectionProviderTypes', 'ConnectionProviderType')
            .where('Personnel.id = :id', { id });

        const personnel = await query.getOne();

        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        // Only load training documents separately as it requires complex subquery
        await this.setPersonnelTrainingDocuments([personnel]);

        return personnel;
    }

    async findOneByPersonnelIdOrFailForReminderEmail(id: number): Promise<Personnel> {
        // begin building the query
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .where('Personnel.id = :id', { id });

        const personnel = await query.getOne();

        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    async findByIdOrFail(id: number, expand: PersonnelExpand[]): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel').where('Personnel.id = :id', { id });
        this.expandSubCollections(query, expand);

        const personnel = await query.getOne();
        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }
        return personnel;
    }

    async findByEmailOrFail(email: string, expand: PersonnelExpand[]): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel').where('User.email = :email', { email });
        this.expandSubCollections(query, [PersonnelExpand.user, ...expand]);

        const personnel = await query.getOne();
        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }
        return personnel;
    }

    findManyByEmploymentStatuses(employmentStatuses: EmploymentStatus[]): Promise<Personnel[]> {
        return this.find({
            where: {
                employmentStatus: In(employmentStatuses),
            },
        });
    }

    /**
     *
     * @param id
     */
    async findOneByUserIdOrFail(id: number): Promise<Personnel> {
        // begin building the query
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('User.id = :id', { id });

        const personnel = await query.getOne();

        if (isEmpty(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    /**
     *
     * @param id
     * :)
     * @returns
     */

    async getPersonnelByIdForPDFDownload(id: number): Promise<Personnel> {
        const personnel = await this.createQueryBuilder('Personnel')
            .where('Personnel.id = :id', { id })
            .getOne();

        if (isEmpty(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        const user = await this.findPersonnelWithUserData(id);

        const [reasonProvider, data, devices, complianceChecks] = await Promise.all([
            this.findPersonnelWithReasonProvider(id),
            this.findPersonnelWithPersonnelData(id),
            this.findPersonnelWithDevices(id),
            this.findPersonnelWithComplianceChecks(id),
        ]);

        const personnelData = {
            user: user.user,
            reasonProvider: reasonProvider.reasonProvider,
            data: data.data,
            devices: devices.devices,
            complianceChecks: complianceChecks.complianceChecks,
        };
        merge(personnel, personnelData);

        if (
            isEmpty(user.user) ||
            isEmpty(data.data) ||
            isEmpty(complianceChecks.complianceChecks)
        ) {
            throw new NotFoundException(ErrorCode.PERSONNEL_PROPERTIES_NOT_FOUND);
        }

        return personnel;
    }

    /**
     *
     * @param email
     */
    async findOneByEmail(email: string, fail: boolean): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('User.email = :email', { email });

        const personnel = await query.getOne();

        if (isEmpty(personnel) && fail) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    async findPersonnelByPersonalEmail(email: string, fail: boolean): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .andWhere('User.personalEmail = :personalEmail', {
                personalEmail: getSha256HashString(email),
            });

        const count = await query.getCount();
        /**
         * We should only match with one user, if multiple users are matched
         * We should return no match
         */
        if (count !== 1) {
            return null;
        }

        const personnel = await query.getOne();

        if (isEmpty(personnel) && fail) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    async findPersonnelByName(
        fullName: { firstName: string; lastName: string },
        fail: boolean,
    ): Promise<Personnel> {
        const { firstName, lastName } = fullName;
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .andWhere('User.firstname = :firstName AND User.lastname = :lastName', {
                firstName,
                lastName,
            });

        const count = await query.getCount();
        /**
         * We should only match with one user, if multiple users are matched
         * We should return no match
         */
        if (count !== 1) {
            return null;
        }

        const personnel = await query.getOne();
        if (isEmpty(personnel) && fail) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    /**
     * Get all personnel records where roles are not deleted.
     * If a personnel does not have any roles, they will still be in the
     * set with no associated roles.
     */
    geAllPersonnelWithOnlyActiveRoles(): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.roles', 'UserRole');

        return query.getMany();
    }

    /**
     * Get one personnel record where roles are not deleted
     */
    async getPersonnelWithOnlyActiveRoles(id: number): Promise<ExtendedPersonnel> {
        // begin building the query
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect(
                'Personnel.complianceChecks',
                'ComplianceCheck',
                'ComplianceCheck.deleted_at IS NULL',
            )
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.identities', 'UserIdentity', 'UserIdentity.deleted_at IS NULL')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .andWhere('Personnel.id = :id', { id });

        const personnel = await query.getOne();

        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    /**
     *
     * @param ids
     * @param page
     * @param limit
     * @returns
     */
    async getAllPersonnelWithAllRoles(
        ids: number[],
        options?: {
            skip?: number;
            limit?: number;
            employmentStatuses?: EmploymentStatus[];
        },
    ): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .withDeleted()
            .innerJoinAndSelect('User.roles', 'UserRole')
            .orderBy('Personnel.id');

        if (!isEmpty(ids)) {
            query.andWhere('Personnel.id IN (:...ids)', {
                ids,
            });
        }
        if (!isEmpty(options?.employmentStatuses)) {
            query.andWhere('Personnel.employmentStatus IN (:...employmentStatuses)', {
                employmentStatuses: options.employmentStatuses,
            });
        }

        if (!isNil(options?.skip) && !isNil(options?.limit)) {
            query.skip(options?.skip);
            query.take(options?.limit);
        }

        const personnel = await query.getMany();

        if (isEmpty(personnel)) {
            return [];
        }

        const complianceChecks = await this.getComplianceChecks(personnel.map(p => p.id));

        if (isEmpty(complianceChecks)) {
            return personnel;
        }

        for (const person of personnel) {
            const complianceCheck = complianceChecks.find(p => p.id === person.id);

            person.complianceChecks = complianceCheck?.complianceChecks ?? [];
        }

        return personnel;
    }

    /**
     *
     * @param ids
     * @returns
     */
    private getComplianceChecks(ids?: number[]): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel').innerJoinAndSelect(
            'Personnel.complianceChecks',
            'ComplianceCheck',
        );

        if (!isEmpty(ids)) {
            query.andWhere('Personnel.id IN (:...ids)', {
                ids,
            });
        }

        return query.getMany();
    }

    /**
     * Get one personnel record with all roles, deleted and not deleted
     *
     * @param {number} id
     * @returns {Promise<ExtendedPersonnel>}
     */
    async getPersonnelWithAllRoles(id: number): Promise<ExtendedPersonnel> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .withDeleted()
            .innerJoinAndSelect('User.roles', 'UserRole')
            .andWhere('Personnel.id = :id', { id: id });

        const personnel = await query.getOne();

        if (isNil(personnel)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return personnel;
    }

    /**
     *
     * @param dto
     * @param includeCsvData
     * @param getComplianceChecks
     * @returns
     */
    async listPersonnel(
        dto: PersonnelRequestDto,
        getComplianceChecks = true,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const {
            page,
            limit,
            q,
            sort,
            sortDir,
            employmentStatus,
            employmentStatuses,
            fullCompliance,
            acceptedPoliciesCompliance,
            identityMfaCompliance,
            bgCheckCompliance,
            agentInstalledCompliance,
            passwordManagerCompliance,
            autoUpdatesCompliance,
            locationServicesCompliance,
            hdEncryptionCompliance,
            antivirusCompliance,
            lockScreenCompliance,
            securityTrainingCompliance,
            hipaaTrainingCompliance,
            nistaiTrainingCompliance,
            deviceCompliance,
            multiSecurityTrainingCompliance,
            offboardingEvidence,
            mdmSourceType,
            inverseMdmSourceTypes,
            groupIds,
            isEdrConnectionActive,
            notFoundInHRISIDP,
            idpConnectionIds,
        } = dto;

        const skip = getSkip(page, limit);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .innerJoin('Personnel.user', 'User');

        query.innerJoinAndSelect('Personnel.data', 'PersonnelData').skip(skip).take(limit);

        this.resolveSearchQ(query, q);

        query = resolveFilters({
            query,
            employmentStatus,
            employmentStatuses,
            fullCompliance,
            acceptedPoliciesCompliance,
            identityMfaCompliance,
            bgCheckCompliance,
            agentInstalledCompliance,
            passwordManagerCompliance,
            autoUpdatesCompliance,
            locationServicesCompliance,
            hdEncryptionCompliance,
            antivirusCompliance,
            lockScreenCompliance,
            securityTrainingCompliance,
            hipaaTrainingCompliance,
            nistaiTrainingCompliance,
            deviceCompliance,
            multiSecurityTrainingCompliance,
            offboardingEvidence,
            mdmSourceType,
            inverseMdmSourceTypes,
            groupIds,
            isEdrConnectionActive,
            excludeIds,
            notFoundInHRISIDP,
            idpConnectionIds,
        });

        this.resolveSort(query, sort, sortDir);

        const [foundPersonnel, total] = await query.getManyAndCount();
        const personnelIds = map(foundPersonnel, 'id');

        let data: Personnel[] = [];

        if (personnelIds.length > 0) {
            query = this.createQueryBuilder('Personnel');

            /**
             * Add the ordering here
             *
             * Note: This is added right after the create
             * due to a TypeORM sorting issue because of
             * the multiple joins on the query
             */

            this.resolveSort(query, sort, sortDir);

            query
                .innerJoinAndSelect('Personnel.user', 'User')
                .leftJoinAndSelect('User.identities', 'UserIdentity')
                .leftJoinAndSelect('UserIdentity.connection', 'Connection')
                .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
                .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
                .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
                .leftJoinAndSelect(
                    'Personnel.securityTrainingComplianceChecks',
                    'TrainingComplianceCheck',
                )
                .innerJoinAndSelect('Personnel.data', 'PersonnelData')
                .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
                .leftJoinAndSelect('User.roles', 'UserRole')
                .loadRelationCountAndMap('Personnel.devicesCount', 'Personnel.devices')
                .where('Personnel.id IN (:...personnelIds)', {
                    personnelIds,
                });

            data = await query.getMany();

            const foundPersonnelIds = data.map(p => p.id);

            await this.setPersonnelDevices(data);

            await this.setPersonnelTrainingDocuments(data);

            await this.setDevicesFailingComplianceCount(data);

            /**
             * Autopilot tests should specify what kind of different
             * compliance checks are required inside dto argument.
             */
            if (foundPersonnelIds.length > 0 && getComplianceChecks) {
                const complianceChecks = await this.getComplianceChecks(foundPersonnelIds);

                for (const person of data) {
                    const complianceCheck = complianceChecks.find(p => p.id === person.id);

                    if (complianceCheck) {
                        person.complianceChecks = complianceCheck.complianceChecks;
                    }
                }
            }
        }

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    async listPersonnelV2(
        dto: PersonnelRequestDto,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            q,
            sort,
            sortDir,
        } = dto;

        const skip = getSkip(page, limit);

        let query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect(
                'Personnel.securityTrainingComplianceChecks',
                'TrainingComplianceCheck',
            )
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceChecks')
            .skip(skip)
            .take(limit);

        this.resolveSearchQ(query, q);

        query = resolveFilters({ query, excludeIds, ...dto });

        this.resolveSort(query, sort, sortDir);

        query = removeRedundantColumnSelects(query);

        const personnelQueryResults = await query.getManyAndCount();

        const [foundPersonnel, total] = personnelQueryResults;

        if (isSelectionPresent(query, SORTING_BY_COMPLIANCE_CHECK_IDENTIFIER)) {
            await this.setComplianceChecks(foundPersonnel);
        }

        return {
            data: await this.attachDocumentsIdentitiesAndDevicesToPersonnel(foundPersonnel),
            page,
            limit,
            total,
        };
    }

    async listPersonnelWithCursor(
        request: PersonnelListRequestType,
    ): Promise<CursorPage<Personnel>> {
        const query = this.createQueryBuilder('Personnel');
        const { cursor, expand, sort, sortDir, size } = request;

        if (expand) {
            this.expandSubCollections(query, expand);
        }

        setupCursorQueryAutoIncrementId(query, sort, sortDir);
        applyCursorFilters(cursor, query, sort, sortDir);

        this.applyFilters(query, request);

        return getCursorPageAutoIncrementId(query, sort, size);
    }

    private async attachDocumentsIdentitiesAndDevicesToPersonnel(
        personnel: Personnel[],
    ): Promise<Personnel[]> {
        const [userDocuments, userIdentities, personnelDevices] = await Promise.all([
            this.getUserDocumentsMap(personnel),
            this.getUserIdentitiesMap(personnel),
            this.getPersonnelDevicesMap(personnel),
        ]);
        return personnel.map(person => {
            const devices = personnelDevices?.get(person.id) ?? [];
            return {
                ...person,
                devices,
                devicesCount: devices.length,
                user: person.user
                    ? {
                          ...person.user,
                          documents: userDocuments?.get(person.user.id) ?? [],
                          identities: userIdentities?.get(person.user.id) ?? [],
                      }
                    : undefined,
            };
        }) as Personnel[];
    }

    private async setComplianceChecks(personnel: Personnel[]) {
        if (isEmpty(personnel)) {
            return;
        }

        const personnelWithComplianceChecks = await this.getComplianceChecks(map(personnel, 'id'));

        const complianceChecksMapping = new Map(
            personnelWithComplianceChecks.map(person => [person.id, person.complianceChecks]),
        );

        personnel.forEach(
            person => (person.complianceChecks = complianceChecksMapping.get(person.id) ?? []),
        );
    }

    getUserIdentitiesByUserIds(userIds: number[]): Promise<UserIdentity[]> {
        if (isNil(userIds) || isEmpty(userIds)) {
            return Promise.resolve([]);
        }
        const userIdentityRepository = this.manager.connection.getRepository(UserIdentity);
        return userIdentityRepository
            .createQueryBuilder('UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('Connection.connectionProviderTypes', 'ConnectionProviderType')
            .where('UserIdentity.fk_user_id IN (:...userIds)', {
                userIds,
            })
            .getMany();
    }

    private convertUserIdentitiesToMapByUser(
        userIdentities: UserIdentity[],
    ): Map<number, UserIdentity[]> {
        if (!userIdentities?.length) {
            return new Map<number, UserIdentity[]>();
        }
        return userIdentities.reduce((userIdentityMap, userIdentity) => {
            if (!userIdentity.userId) {
                return userIdentityMap;
            }

            const existingUserIdentities = userIdentityMap.get(userIdentity.userId) ?? [];
            userIdentityMap.set(userIdentity.userId, [...existingUserIdentities, userIdentity]);

            return userIdentityMap;
        }, new Map<number, UserIdentity[]>());
    }

    private convertUserDocumentsToMapByUser(
        userDocuments: UserDocument[],
    ): Map<number, UserDocument[]> {
        if (!userDocuments?.length) {
            return new Map<number, UserDocument[]>();
        }

        return userDocuments.reduce((userDocumentsMap, userDocument) => {
            if (!userDocument.user) {
                return userDocumentsMap;
            }

            const userId = userDocument.user.id;
            const existingDocuments = userDocumentsMap.get(userId) ?? [];
            userDocumentsMap.set(userId, [...existingDocuments, userDocument]);
            set(userDocument, 'user', undefined);

            return userDocumentsMap;
        }, new Map<number, UserDocument[]>());
    }

    private convertPersonnelDevicesToMapByPerson(
        personnelDevices: Personnel[],
    ): Map<number, Device[]> {
        if (!personnelDevices?.length) {
            return new Map<number, Device[]>();
        }
        return personnelDevices.reduce((personnelDevicesMap, person) => {
            const existingDevices = personnelDevicesMap.get(person.id) ?? [];
            personnelDevicesMap.set(person.id, [...existingDevices, ...(person.devices ?? [])]);

            return personnelDevicesMap;
        }, new Map<number, Device[]>());
    }

    async listPersonnelWithUnacceptedPolicies(
        dto: PersonnelForApTaskRequestDto,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const { page = 1, limit = 100, acceptedPoliciesCompliance, employmentStatuses } = dto;

        const skip = getSkip(page, limit);
        const order = getSortDir(SortDir.ASC);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .innerJoinAndSelect('Personnel.user', 'User')
            .orderBy('User.firstName', order)
            .addOrderBy('User.lastName', order)
            .skip(skip)
            .take(limit);

        query = resolveFilters({
            query,
            excludeIds,
            acceptedPoliciesCompliance,
            employmentStatuses,
        });

        const [foundPersonnel, total] = await query.getManyAndCount();
        const personnelIds = map(foundPersonnel, 'id');

        let data: Personnel[] = [];
        if (!isEmpty(personnelIds)) {
            query = this.createQueryBuilder('Personnel');

            this.resolveSort(query, SortType.NAME, SortDir.ASC);

            query
                .innerJoinAndSelect('Personnel.user', 'User')
                .where('Personnel.id IN (:...personnelIds)', {
                    personnelIds,
                });

            data = await query.getMany();

            const foundPersonnelIds = data.map(personnel => personnel.id);

            /**
             * Autopilot tests should specify what kind of different
             * compliance checks are required inside dto argument.
             */
            if (!isEmpty(foundPersonnelIds)) {
                const complianceChecks = await this.getComplianceChecks(foundPersonnelIds);

                for (const person of data) {
                    const complianceCheck = complianceChecks.find(
                        compliance => compliance.id === person.id,
                    );

                    if (!isNil(complianceCheck)) {
                        person.complianceChecks = complianceCheck.complianceChecks;
                    }
                }
            }
        }

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    async listPersonnelFailedMfa(
        dto: PersonnelWithFailedMfaRequestDto,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const {
            page = 1,
            limit = 100,
            employmentStatuses,
            identityMfaCompliance,
            idpConnectionIds,
        } = dto;

        const skip = getSkip(page, limit);
        const order = getSortDir(SortDir.ASC);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .innerJoinAndSelect('Personnel.user', 'User')
            .orderBy('User.firstName', order)
            .addOrderBy('User.lastName', order)
            .skip(skip)
            .take(limit);

        query = resolveFilters({
            query,
            excludeIds,
            employmentStatuses,
            identityMfaCompliance,
            idpConnectionIds,
        });

        const [foundPersonnel, total] = await query.getManyAndCount();
        const personnelIds = map(foundPersonnel, 'id');

        let data: Personnel[] = [];
        if (!isEmpty(personnelIds)) {
            query = this.createQueryBuilder('Personnel');

            this.resolveSort(query, SortType.NAME, SortDir.ASC);

            query
                .innerJoinAndSelect('Personnel.user', 'User')
                .where('Personnel.id IN (:...personnelIds)', {
                    personnelIds,
                });

            data = await query.getMany();
        }

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    async listPersonnelForSecurityCompliance(
        dto: PersonnelForApTaskRequestDto,
    ): Promise<PaginationType<Personnel>> {
        const {
            page = 1,
            limit = 100,
            employmentStatuses,
            securityTrainingCompliance,
            bgCheckCompliance,
            sortDir,
        } = dto;

        const skip = getSkip(page, limit);
        const order = getSortDir(sortDir);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select(['Personnel.id', 'Personnel.startDate', 'Personnel.separationDate'])
            .innerJoinAndSelect('Personnel.user', 'User')
            .orderBy('User.firstName', order)
            .addOrderBy('User.lastName', order)
            .skip(skip)
            .take(limit);

        query = resolveFilters({
            query,
            employmentStatuses,
            securityTrainingCompliance,
            bgCheckCompliance,
        });

        const [data, total] = await query.getManyAndCount();

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    async listPersonnelWithDevices(
        dto: PersonnelWithDevicesRequestDto,
        getComplianceChecks = true,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const {
            page = 1,
            limit = 100,
            passwordManagerCompliance,
            lockScreenCompliance,
            antivirusCompliance,
            autoUpdatesCompliance,
            employmentStatuses,
            hdEncryptionCompliance,
            isEdrConnectionActive,
            idpConnectionIds,
        } = dto;

        const skip = getSkip(page, limit);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .innerJoin('Personnel.user', 'User');

        query.innerJoinAndSelect('Personnel.data', 'PersonnelData').skip(skip).take(limit);

        query = resolveFilters({
            query,
            excludeIds,
            passwordManagerCompliance,
            lockScreenCompliance,
            antivirusCompliance,
            autoUpdatesCompliance,
            employmentStatuses,
            hdEncryptionCompliance,
            isEdrConnectionActive,
            idpConnectionIds,
        });

        const [foundPersonnel, total] = await query.getManyAndCount();
        const personnelIds = map(foundPersonnel, 'id');

        let data: Personnel[] = [];
        if (!isEmpty(personnelIds)) {
            query = this.createQueryBuilder('Personnel');

            this.resolveSort(query, SortType.NAME, SortDir.ASC);

            query
                .innerJoinAndSelect('Personnel.user', 'User')
                .loadRelationCountAndMap('Personnel.devicesCount', 'Personnel.devices')
                .where('Personnel.id IN (:...personnelIds)', {
                    personnelIds,
                });

            data = await query.getMany();

            const foundPersonnelIds = data.map(personnel => personnel.id);

            await this.setPersonnelDevices(data);

            await this.setDevicesFailingComplianceCount(data);

            /**
             * Autopilot tests should specify what kind of different
             * compliance checks are required inside dto argument.
             */
            if (!isEmpty(foundPersonnelIds) && getComplianceChecks) {
                const complianceChecks = await this.getComplianceChecks(foundPersonnelIds);

                for (const person of data) {
                    const complianceCheck = complianceChecks.find(
                        compliance => compliance.id === person.id,
                    );

                    if (!isNil(complianceCheck)) {
                        person.complianceChecks = complianceCheck.complianceChecks;
                    }
                }
            }
        }

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    async listPlainPersonnel(
        dto: PersonnelForApTaskRequestDto,
    ): Promise<PaginationType<Personnel>> {
        const { page = 1, limit = 100, employmentStatuses, sortDir } = dto;

        const skip = getSkip(page, limit);
        const order = getSortDir(sortDir);

        let query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .innerJoinAndSelect('Personnel.user', 'User')
            .orderBy('User.firstName', order)
            .addOrderBy('User.lastName', order)
            .skip(skip)
            .take(limit);

        query = resolveFilters({
            query,
            employmentStatuses,
        });

        const [data, total] = await query.getManyAndCount();

        const paginationData: PaginationType<Personnel> = {
            data,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    /**
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async setPersonnelDevices(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const personnelIds = personnel.map(p => p.id);
        const queryPersonnelDevices = await this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.devices', 'Devices')
            .leftJoinAndSelect('Devices.identifiers', 'identifiers')
            .leftJoinAndSelect('Devices.complianceChecks', 'DeviceComplianceChecks')
            .leftJoinAndSelect('Devices.asset', 'Asset')
            .leftJoinAndSelect('Asset.assetClassTypes', 'AssetClassType')
            .where('Personnel.id IN (:...ids)', { ids: personnelIds })
            .getMany();

        personnel.forEach(p => {
            const selectedPersonnel = queryPersonnelDevices.find(pd => pd.id === p.id);

            if (selectedPersonnel) {
                p.devices = selectedPersonnel.devices;
            }
        });
    }

    /**
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async setPersonnelTrainingDocuments(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const userIds = compact(personnel.map(p => p.user?.id));
        if (isEmpty(userIds)) {
            return;
        }

        const batchSize = 200;
        const concurrentBatches = 5;
        const userIdBatches: number[][] = [];

        for (let i = 0; i < userIds.length; i += batchSize) {
            userIdBatches.push(userIds.slice(i, i + batchSize));
        }

        const allDocuments = await promiseAllInBatches(
            userIdBatches,
            concurrentBatches,
            (batchUserIds: number[]) => this.getUserDocumentsByUserIds(batchUserIds),
        );

        const flatDocuments = allDocuments.flat();

        const documentsByUserId = flatDocuments.reduce((documentsMap, document) => {
            const userId = document.user.id;

            if (!documentsMap.has(userId)) {
                documentsMap.set(userId, []);
            }

            documentsMap.get(userId)?.push(document);

            return documentsMap;
        }, new Map<number, UserDocument[]>());

        personnel.forEach(person => {
            if (person.user?.id) {
                person.user.documents = documentsByUserId.get(person.user.id) || [];
            }
        });
    }

    private async getUserIdentitiesMap(
        personnel: Personnel | Personnel[],
    ): Promise<Map<number, UserIdentity[]>> {
        if (isNil(personnel) || isEmpty(personnel)) {
            return new Map<number, UserIdentity[]>();
        }
        const personnelArray = Array.isArray(personnel) ? personnel : [personnel];
        const userIds = compact(personnelArray.map(person => person.user?.id));
        const userIdentities = await this.getUserIdentitiesByUserIds(userIds);
        return this.convertUserIdentitiesToMapByUser(userIdentities);
    }

    private async getUserDocumentsMap(
        personnel: Personnel[],
    ): Promise<Map<number, UserDocument[]>> {
        if (isNil(personnel) || isEmpty(personnel)) {
            return new Map<number, UserDocument[]>();
        }

        const userIds = compact(personnel.map(({ user }) => user?.id));
        const userDocuments = await this.getUserDocumentsByUserIds(userIds);
        return this.convertUserDocumentsToMapByUser(userDocuments);
    }

    private async getPersonnelDevicesMap(personnel: Personnel[]): Promise<Map<number, Device[]>> {
        if (isNil(personnel) || isEmpty(personnel)) {
            return new Map<number, Device[]>();
        }

        const personnelIds = personnel.map(({ id }) => id);
        const personnelDevices = await this.getPersonnelDevicesByPersonnelIds(personnelIds);
        return this.convertPersonnelDevicesToMapByPerson(personnelDevices);
    }

    getPersonnelDevicesByPersonnelIds(personnelIds: number[]): Promise<Personnel[]> {
        if (isNil(personnelIds) || isEmpty(personnelIds)) {
            return Promise.resolve([]);
        }
        return this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.devices', 'Devices')
            .leftJoinAndSelect('Devices.identifiers', 'identifiers')
            .leftJoinAndSelect('Devices.complianceChecks', 'DeviceComplianceChecks')
            .leftJoinAndSelect('Devices.asset', 'Asset')
            .leftJoinAndSelect('Asset.assetClassTypes', 'AssetClassType')
            .where('Personnel.id IN (:...ids)', { ids: personnelIds })
            .getMany();
    }

    private getUserDocumentsByUserIds(userIds: number[]): Promise<UserDocument[]> {
        if (isNil(userIds) || isEmpty(userIds)) {
            return Promise.resolve([]);
        }
        const userDocumentRepository = this.manager.connection.getRepository(UserDocument);
        const trainingDocumentTypes: UserDocumentType[] = [
            UserDocumentType.SEC_TRAINING,
            UserDocumentType.HIPAA_TRAINING_EVIDENCE,
            UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
        ];

        return userDocumentRepository
            .createQueryBuilder('UserDocument')
            .innerJoinAndSelect('UserDocument.user', 'User')
            .innerJoin(
                qb =>
                    qb
                        .subQuery()
                        .select([
                            'subDoc.fk_user_id AS fk_user_id',
                            'subDoc.type AS type',
                            'MAX(subDoc.renewal_date) AS maxRenewalDate',
                        ])
                        .from(UserDocument, 'subDoc')
                        .where('subDoc.deletedAt IS NULL')
                        .andWhere('subDoc.fk_user_id IN (:...userIds)', { userIds })
                        .andWhere('subDoc.type IN (:...trainingDocumentTypes)', {
                            trainingDocumentTypes,
                        })
                        .groupBy('subDoc.fk_user_id, subDoc.type'),
                'MaxDates',
                'UserDocument.fk_user_id = MaxDates.fk_user_id AND ' +
                    'UserDocument.type = MaxDates.type AND ' +
                    'UserDocument.renewal_date = MaxDates.maxRenewalDate',
            )
            .where('UserDocument.deletedAt IS NULL')
            .orderBy('UserDocument.createdAt', 'DESC')
            .getMany();
    }

    /**
     * Sets and assigns devices associated with a specific personnel, including
     * their compliance checks, identifiers, AND all device documents.
     *
     * ⚠️ **PERFORMANCE WARNING**: This method has known performance issues and should be refactored.
     *
     * Consider using {@link setPersonnelDevice} instead, which excludes documents
     *
     * @description
     * - Executes a query with LEFT JOINs to load devices with ALL their relations
     * - Includes device compliance checks (compliance status)
     * - Includes device identifiers (serial numbers, MAC addresses, etc.)
     * - ❌ Includes ALL device documents (causes performance issues)
     * - Assigns loaded devices directly to the personnel object
     *
     * @param {Personnel} personnel - The personnel object to assign devices to
     * @returns {Promise<void>} - Returns nothing, modifies the personnel object directly
     *
     * @performance
     * - ❌ **CRITICAL**: Causes timeouts when devices have many documents
     * - ❌ **MEMORY**: Loads all documents into memory at once
     * - ❌ **N+1 PROBLEM**: Creates excessive database queries
     * - ❌ **BLOCKING**: Can hang the application for minutes
     *
     * @issues
     * - Personnel with devices containing many documents cause 504 timeouts
     * - Memory consumption can exceed server limits
     * - Database connection pool exhaustion
     * - Poor user experience (hanging UI)
     *
     * @refactor_needed
     * - Implement pagination for device documents
     * - Add lazy loading for documents
     * - Consider separate endpoint for document loading
     * - Add query limits and performance monitoring
     *
     * @see {@link setPersonnelDevice} - Optimized version without documents
     * @see ENG-70301 - Related ticket for personnel tab performance issues
     *
     * @todo Refactor this method to handle large document sets efficiently
     * @since Legacy method - needs performance optimization
     */
    async setPersonnelDeviceWithDocuments(personnel: Personnel): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const { id } = personnel;

        const devices = await this.createQueryBuilder()
            .select('Device')
            .from(Device, 'Device')
            .leftJoinAndSelect('Device.complianceChecks', 'DeviceComplianceChecks')
            .leftJoinAndSelect('Device.identifiers', 'Identifiers')
            .leftJoinAndSelect('Device.documents', 'Documents')
            .leftJoinAndSelect('Documents.device', 'DocumentDevice')
            .where('Device.fk_personnel_id = :id', { id })
            .getMany();

        personnel.devices = devices;
    }

    /**
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async setPersonnelGroups(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }
        const groupPersonnelRepository = this.manager.connection.getRepository(GroupPersonnel);
        const personnelIds = personnel.map(p => p.id);
        const groups = await groupPersonnelRepository
            .createQueryBuilder('GroupPersonnel')
            .select('GroupPersonnel.personnelId')
            .leftJoinAndSelect('GroupPersonnel.group', 'Group')
            .where('GroupPersonnel.fk_personnel_id IN (:...personnelIds)', {
                personnelIds,
            })
            .getMany();
        personnel.forEach(p => {
            p.groups = groups.filter(g => g.personnelId == p.id);
        });
    }
    /**
     *
     * @param personnelIds
     * @param personnel
     */
    async setDevicesFailingComplianceCount(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }
        const personnelIds: number[] = personnel.map(({ id }) => id);
        let personnelWithFailDevices = await this.createQueryBuilder('personnel')
            .select(['personnel.id', 'device.id'])
            .leftJoin('personnel.devices', 'device')
            .loadRelationCountAndMap(
                'device.failingCompliancesCount',
                'device.complianceChecks',
                'complianceCheck',
                qb =>
                    qb.innerJoin('complianceCheck.device', 'device').where(
                        `device.fk_personnel_id IN (:...personnelIds)
                                    AND complianceCheck.status = :status
                                    AND complianceCheck.type != :agentInstalledCompliance`,
                        {
                            personnelIds,
                            agentInstalledCompliance: ComplianceCheckType.AGENT_INSTALLED,
                            status: ComplianceCheckStatus.FAIL,
                        },
                    ),
            )
            .where('personnel.id IN (:...personnelIds)', {
                personnelIds,
            })
            .getMany();

        personnelWithFailDevices = personnelWithFailDevices.map(
            person =>
                ({
                    ...person,
                    devices: person.devices.filter(device => device.failingCompliancesCount > 0),
                }) as Personnel,
        );

        const failDevicesMapping = new Map(
            personnelWithFailDevices.map(person => [person.id, person.devices]),
        );

        personnel.forEach(person => {
            const failDevices = failDevicesMapping.get(person.id);

            if (failDevices?.length) {
                person.devicesFailingComplianceCount = failDevices.length;
            }
        });
    }

    async getTrainingChecksByPersonnelIds(
        personnelIds: string[] | number[],
        isHipaaFrameworkEnabled = false,
        isNistAiFrameworkEnabled = false,
    ): Promise<Personnel[]> {
        if (isEmpty(personnelIds)) {
            return [];
        }

        const query = this.createQueryBuilder('personnel')
            .select(['personnel.id'])
            .innerJoinAndSelect(
                'personnel.securityTrainingComplianceChecks',
                'securityTrainingComplianceCheck',
            )
            .innerJoin(
                'securityTrainingComplianceCheck.trainingCampaign',
                'SecurityTrainingCampaign',
            )
            .innerJoin('SecurityTrainingCampaign.connection', 'SecurityConnection')
            .where('personnel.id IN (:...personnelIds)', {
                personnelIds,
            })
            .andWhere('SecurityTrainingCampaign.type = :securityType', {
                securityType: TrainingCampaignType.SECURITY_TRAINING,
            });

        if (isHipaaFrameworkEnabled) {
            query
                .innerJoinAndSelect(
                    'personnel.hipaaTrainingComplianceChecks',
                    'hipaaTrainingComplianceCheck',
                )
                .innerJoin('hipaaTrainingComplianceCheck.trainingCampaign', 'HipaaTrainingCampaign')
                .innerJoin('HipaaTrainingCampaign.connection', 'HipaaConnection')
                .andWhere('HipaaTrainingCampaign.type = :hipaaType', {
                    hipaaType: TrainingCampaignType.HIPAA_TRAINING,
                });
        }

        if (isNistAiFrameworkEnabled) {
            query
                .innerJoinAndSelect(
                    'personnel.nistAiTrainingComplianceChecks',
                    'nistAiTrainingComplianceCheck',
                )
                .innerJoin(
                    'nistAiTrainingComplianceCheck.trainingCampaign',
                    'NistAiTrainingCampaign',
                )
                .innerJoin('NistAiTrainingCampaign.connection', 'NistAiConnection')
                .andWhere('NistAiTrainingCampaign.type = :nistAiType', {
                    nistAiType: TrainingCampaignType.NIST_AI_TRAINING,
                });
        }

        return query.getMany();
    }

    async getAllPersonnelForPreAuditPackage(): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .addOrderBy(
                field(
                    [
                        EmploymentStatus.CURRENT_EMPLOYEE,
                        EmploymentStatus.CURRENT_CONTRACTOR,
                        EmploymentStatus.FORMER_EMPLOYEE,
                        EmploymentStatus.SPECIAL_FORMER_EMPLOYEE,
                        EmploymentStatus.FORMER_CONTRACTOR,
                        EmploymentStatus.UNKNOWN,
                        EmploymentStatus.OUT_OF_SCOPE,
                        EmploymentStatus.SERVICE_ACCOUNT,
                    ],
                    'employment_status',
                ),
            )
            .addOrderBy('User.firstname', 'ASC')
            .addOrderBy('User.lastname', 'ASC');
        return query.getMany();
    }

    /**
     *
     * Caution: This query is specialized for the identity sync process.
     *
     * @param {string[]} identityIds
     * @param {ConnectionEntity} connection
     */
    findByIdentityIds(identityIds: string[], connection: ConnectionEntity): Promise<Personnel[]> {
        if (isEmpty(identityIds)) {
            /**
             * No targets passed in there, exit early
             */
            return promiseEmptyArray<Personnel>();
        }

        const query = this.getQueryWithConnectionBase().innerJoin(
            'User.identities',
            'ConnectionFilter',
            `ConnectionFilter.connection = :connectionId
            AND ConnectionFilter.identityId IN (:...identityIds)`,
            {
                connectionId: connection.id,
                identityIds,
            },
        );

        return query.getMany();
    }

    findByConnectionId(id: number): Promise<Personnel[]> {
        const query = this.getQueryWithConnectionBase().innerJoin(
            'User.identities',
            'ConnectionFilter',
            'ConnectionFilter.connection = :connectionId',
            {
                connectionId: id,
            },
        );

        return query.getMany();
    }

    /**
     * Caution: This query is specialized for the identity sync process.
     *
     * @param clientType
     * @returns
     */
    async findByClientType(clientType: ClientType): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .withDeleted()
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .innerJoin(
                // we need to make sure the personnel came from connection
                'UserIdentity.connection',
                'ConnectionFilter',
                `ConnectionFilter.clientType = :clientType AND ConnectionFilter.providerType = :providerType`,
                {
                    clientType,
                    providerType: ProviderType.IDENTITY,
                },
            );
        return query.getMany();
    }

    /**
     * Caution: This query is specialized for the identity sync process.
     *
     * We inner join on user identities to make sure the
     * personnel records came from a user identity source
     *
     * @param options
     * @returns
     */
    findFormerPersonnelByIdentityUser(
        identityUser: IdentityServiceUser,
    ): Promise<Personnel | null> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .withDeleted() // pull in soft deleted records as well
            .innerJoinAndSelect('User.identities', 'UserIdentity')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            // left join since we are applying deletedAt filter
            .leftJoinAndSelect('User.roles', 'UserRole')
            .innerJoin(
                // we need to make sure the personnel came from connection
                'UserIdentity.connection',
                'ConnectionFilter',
                `ConnectionFilter.providerType = :providerType`,
                {
                    providerType: ProviderType.IDENTITY,
                },
            )
            .innerJoin('User.identities', 'UserIdentityFilter')
            .where('Personnel.employment_status IN (:...strictFormerStatuses)', {
                strictFormerStatuses,
            })
            .andWhere(qb => {
                // search by id's for trivial matching
                qb.where('UserIdentityFilter.identityId = :identityId', {
                    identityId: identityUser.getId(),
                });
                const emails = identityUser.getEmails();
                if (!isEmpty(emails)) {
                    // search by email for id shift matching
                    qb.orWhere('UserIdentityFilter.email IN (:...emails)', {
                        emails,
                    });
                }
            });

        return query.getOne();
    }

    findByUserIds(userIds: number[]): Promise<Personnel[]> {
        if (isEmpty(userIds)) {
            return promiseEmptyArray<Personnel>();
        }
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            // left join since we are applying deletedAt filter
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('User.id IN (:...userIds)', {
                userIds,
            });
        return query.getMany();
    }

    /**
     * @param {string[]} usersEmail
     * @returns {Promise<Personnel[]>}
     */
    findByUserEmails(usersEmail: string[]): Promise<Personnel[]> {
        if (isEmpty(usersEmail)) {
            return promiseEmptyArray<Personnel>();
        }
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoin('User.identities', 'UserIdentity')
            .leftJoin('UserIdentity.connection', 'Connection')
            .leftJoin('User.roles', 'UserRole')
            .where('User.email IN (:...usersEmail)', {
                usersEmail,
            });
        return query.getMany();
    }

    /**
     * Pull personnel that need to be separated from the company
     *
     * @param {string[]} ids
     * @param {ConnectionEntity} connection
     * @return {Promise<Personnel[]>}
     */
    findAllExcludeByIdentityIds(ids: string[], connection: ConnectionEntity): Promise<Personnel[]> {
        const formerEmployees = [
            EmploymentStatus.FORMER_CONTRACTOR,
            EmploymentStatus.FORMER_EMPLOYEE,
        ];
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            // left join since we are applying deletedAt filter
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('Personnel.employment_status NOT IN (:...formerEmployees)', {
                formerEmployees,
            })
            .andWhere('Connection.id = :connectionId', {
                connectionId: connection.id,
            });
        // sanity check if there are ids to exclude
        if (!isEmpty(ids)) {
            // filter out the identities with the passed in IDs
            query.andWhere('UserIdentity.identityId NOT IN (:...ids)', { ids });
        }
        // get the personnel records with filters/sort AND the total count
        return query.getMany();
    }

    /**
     * @return {Promise<number>}
     */
    async getCurrentPersonnelCount(): Promise<number> {
        const currentStatuses = [
            EmploymentStatus.CURRENT_EMPLOYEE,
            EmploymentStatus.CURRENT_CONTRACTOR,
        ];

        const { personnelCount } = await this.createQueryBuilder('Personnel')
            .select('COUNT(*)', 'personnelCount')
            .where('Personnel.employmentStatus IN (:...currentStatuses)', {
                currentStatuses,
            })
            .getRawOne();

        return Number(personnelCount);
    }

    async getCurrentPersonnel(): Promise<Personnel[]> {
        const currentStatuses = [
            EmploymentStatus.CURRENT_EMPLOYEE,
            EmploymentStatus.CURRENT_CONTRACTOR,
        ];

        return this.createQueryBuilder('Personnel')
            .where('Personnel.employmentStatus IN (:...currentStatuses)', {
                currentStatuses,
            })
            .leftJoinAndSelect('Personnel.user', 'User')
            .getMany();
    }

    async getCurrentPersonnelFullyCompliance(
        complianceCheckType: ComplianceCheckType,
    ): Promise<Personnel[]> {
        const employmentStatuses = [
            EmploymentStatus.CURRENT_EMPLOYEE,
            EmploymentStatus.CURRENT_CONTRACTOR,
        ];

        return this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.complianceChecks', 'ComplianceChecks')
            .where('Personnel.employmentStatus IN (:...employmentStatuses)', {
                employmentStatuses,
            })
            .andWhere('ComplianceChecks.type = :complianceCheckType', {
                complianceCheckType,
            })
            .andWhere('ComplianceChecks.status = :status', {
                status: ComplianceCheckStatus.PASS,
            })
            .getMany();
    }

    async findByEmploymentStatuses(employmentStatuses: EmploymentStatus[]): Promise<Personnel[]> {
        if (isEmpty(employmentStatuses)) {
            return [];
        }

        const personnel = await this.createQueryBuilder('Personnel')
            .where('Personnel.employmentStatus IN (:...employmentStatuses)', {
                employmentStatuses,
            })
            .leftJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.complianceChecks', 'ComplianceChecks')
            .getMany();
        await this.setPersonnelDevices(personnel);
        await this.setPersonnelGroups(personnel);
        return personnel;
    }

    /**
     *
     * @param {number[]} groupIds
     * @returns {Promise<number[]>}
     */
    async getPersonnelIdsByGroupIds(groupIds: number[]): Promise<number[]> {
        const distinctPersonnel = await this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .leftJoin('Personnel.groups', 'GroupPersonnel')
            .where('GroupPersonnel.fk_group_id IN (:...groupIds)', {
                groupIds,
            })
            .getMany();
        return distinctPersonnel.map(p => p.id);
    }

    /**
     *
     * @param {number[]} personnelIds
     * @param {FindPersonnelOptions} options
     * @returns {Promise<Personnel[]>}
     */
    async findByIdsWithOptions(
        personnelIds: number[],
        options: FindPersonnelOptions = { setDevices: false, setGroups: false },
    ): Promise<Personnel[]> {
        const { setDevices, setGroups } = options;
        if (isEmpty(personnelIds)) {
            return [];
        }
        const personnel = await this.createQueryBuilder('Personnel')
            .where('Personnel.id IN (:...personnelIds)', {
                personnelIds,
            })
            .leftJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('Personnel.complianceChecks', 'ComplianceChecks')
            .getMany();

        if (setDevices) {
            await this.setPersonnelDevices(personnel);
        }
        if (setGroups) {
            await this.setPersonnelGroups(personnel);
        }
        return personnel;
    }

    findByPendingOrMissingHrisBackgroundCheck(
        targetConnection: ConnectionEntity,
        backgroundCheckType: BackgroundCheckType,
    ): Promise<Personnel[]> {
        const targetConnectionClause = { connectionId: targetConnection.id };

        // Note: this query is intently very strict about the data it pulls and targets
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .innerJoinAndSelect(
                // we only want personnel/users who are linked to this connection
                'User.identities',
                'UserIdentity',
                'UserIdentity.fk_connection_id = :connectionId',
                targetConnectionClause,
            )
            .innerJoinAndSelect(
                'UserIdentity.connection',
                'Connection',
                'Connection.id = :connectionId',
                targetConnectionClause,
            )
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .where('BackgroundCheck.status IS NULL')
            .orWhere(
                new Brackets(qb => {
                    qb.where('BackgroundCheck.status IN (:...statuses)', {
                        statuses: [BackgroundCheckStatus.PENDING, BackgroundCheckStatus.ISSUE],
                    }).andWhere('BackgroundCheck.type = :backgroundCheckType', {
                        backgroundCheckType,
                    });
                }),
            );

        return query.getMany();
    }

    findByPendingBackgroundCheck(backgroundCheckType: BackgroundCheckType): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('BackgroundCheck.status IN (:...statuses)', {
                statuses: [BackgroundCheckStatus.PENDING, BackgroundCheckStatus.ISSUE],
            })
            .andWhere('BackgroundCheck.type = :backgroundCheckType', {
                backgroundCheckType,
            });
        return query.getMany();
    }

    /**
     *
     * @param {number[]} ids
     * @returns {Promise<Personnel[]>}
     */
    findAllByIds(ids: number[]): Promise<Personnel[]> {
        if (isEmpty(ids)) {
            return promiseEmptyArray<Personnel>();
        }

        const query = this.createQueryBuilder('personnel')
            .leftJoinAndSelect('personnel.user', 'User')
            .innerJoinAndSelect('personnel.complianceChecks', 'ComplianceCheck')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .where('personnel.id IN (:...ids)', { ids })
            .orderBy(field(ids, 'personnel.id'));

        return query.getMany();
    }

    findAllForAuditValidation(ids: number[]): Promise<Personnel[]> {
        if (isEmpty(ids)) {
            return promiseEmptyArray<Personnel>();
        }

        return this.createQueryBuilder('personnel')
            .select(['personnel.id', 'personnel.startDate', 'personnel.separationDate'])
            .where('personnel.id IN (:...ids)', { ids })
            .getMany();
    }

    /**
     *
     * @param {AuditorPersonnelDateRangeType} dto
     * @param {AuditorPersonnelHandler} handler
     * @returns {Promise<Personnel[]>}
     *
     * @deprecated
     * @TODO-AUDITHUB this should be cleaned up and substituted by getPaginatedPersonnelByTypeWithinRange
     */
    findPersonnelByTimeRange(
        dto: AuditorPersonnelDateRangeType,
        handler: AuditorPersonnelHandler,
        employmentStatuses?: EmploymentStatus[],
    ): Promise<Personnel[]> {
        const { startDate, endDate } = dto;
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .where(`unix_timestamp(${handler}) >= unix_timestamp(:startDate)`, {
                startDate,
            })
            .andWhere(`unix_timestamp(${handler}) <= unix_timestamp(:endDate)`, {
                endDate,
            });
        if (!isEmpty(employmentStatuses)) {
            // apply the filter in the query
            query.andWhere('Personnel.employmentStatus IN (:...employmentStatuses)', {
                employmentStatuses,
            });
        }

        return query.getMany();
    }

    /**
     *
     * @param {AuditorPersonnelDateRangeType} dto
     * @returns {Promise<Personnel[]>}
     *
     * @deprecated
     * @TODO-AUDITHUB this should be cleaned up and substituted by getPaginatedPersonnelByTypeWithinRange
     */
    findPersonnelByTimeRangeForCurrent(
        dto: AuditorPersonnelDateRangeType,
        employmentStatuses?: EmploymentStatus[],
    ): Promise<Personnel[]> {
        const { startDate, endDate } = dto;
        const query = this.createQueryBuilder('Personnel').innerJoinAndSelect(
            'Personnel.user',
            'User',
        );

        if (isNil(endDate)) {
            query
                .where(`unix_timestamp(start_date) <= unix_timestamp(:startDate)`, {
                    startDate,
                })
                .andWhere(
                    '(separation_date IS NULL OR unix_timestamp(separation_date) >= unix_timestamp(:startDate))',
                    {
                        startDate,
                    },
                );
        } else {
            query
                .where(`unix_timestamp(start_date) <= unix_timestamp(:endDate)`, {
                    endDate,
                })
                .andWhere(
                    '((unix_timestamp(separation_date) >= unix_timestamp(:startDate)) OR separation_date IS NULL)',
                    dto,
                );
        }
        if (!isEmpty(employmentStatuses)) {
            // apply the filter in the query
            query.andWhere('Personnel.employmentStatus IN (:...employmentStatuses)', {
                employmentStatuses,
            });
        }

        return query.getMany();
    }

    /**
     *
     * @param {string} firstDate
     * @param {string} secondDate
     * @returns {Promise<Personnel[]>}
     */
    findAllFormerPersonnelByStatusUpdatedAtDateRange(
        firstDate: string,
        secondDate: string,
    ): Promise<Personnel[]> {
        const formerStatuses = [
            EmploymentStatus.FORMER_CONTRACTOR,
            EmploymentStatus.FORMER_EMPLOYEE,
        ];

        const query = this.createQueryBuilder('Personnel');
        query
            .where('Personnel.employment_status IN (:...formerStatuses)', {
                formerStatuses,
            })
            .andWhere('Personnel.separation_date BETWEEN :firstDate AND :secondDate', {
                firstDate,
                secondDate,
            })
            .innerJoinAndSelect('Personnel.user', 'User');
        return query.getMany();
    }

    /**
     * @param {SelectQueryBuilder<Personnel>} query
     * @param {string} q
     */
    private resolveSearchQ(query: SelectQueryBuilder<Personnel>, q: string) {
        // check for the search q
        if (q) {
            // filter to the search term on first name, last name, or email
            query.andWhere(
                `(
                    User.firstName ${like()} :after
                    OR User.lastName ${like()} :after
                    OR User.email ${like()} :after
                    OR User.jobTitle ${like()} :after
                    OR CONCAT(User.firstName, ' ', User.lastName) ${like()} :after
                )`,
                {
                    after: `%${q}%`,
                },
            );
        }
    }

    /**
     *
     * @param account
     * @param dto
     * @returns
     */
    async getControlOwnersByTime(
        account: Account,
        dto: UserControlsRequestDto,
    ): Promise<PaginationType<User>> {
        const { limit, time, getAll } = dto;
        let { page } = dto;

        const query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .leftJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect(
                'User.controls',
                'Control',
                'Control.enabledAt IS NOT NULL AND Control.archivedAt IS NULL AND Control.deletedAt is NULL',
            )
            .leftJoinAndSelect(
                'User.features',
                'UserFeatures',
                'UserFeatures.enabledAt IS NOT NULL',
            )
            .leftJoinAndSelect('UserFeatures.feature', 'Feature')
            .where('Control.id IS NOT NULL')
            .andWhere('Feature.feature_type = :feature', {
                feature: FeatureType.CONTROL_EMAIL_FREQUENCY,
            })
            .andWhere('Personnel.employmentStatus IN (:...validStatus)', {
                validStatus: [
                    EmploymentStatus.CURRENT_CONTRACTOR,
                    EmploymentStatus.CURRENT_EMPLOYEE,
                    EmploymentStatus.UNKNOWN,
                    EmploymentStatus.OUT_OF_SCOPE,
                    EmploymentStatus.SERVICE_ACCOUNT,
                ],
            })
            .orderBy('User.firstName', 'ASC');

        addProductClause(query, 'Control', getProductId(account));

        query.take(limit);

        if (!isNil(page) && !isNil(limit)) {
            query.skip(getSkip(page, limit));
        }

        let [userData, userTotal] = await query.getManyAndCount();

        if (userTotal === 0) {
            throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
        }

        if (getAll) {
            while (userTotal - limit > 0) {
                userTotal -= limit;
                ++page;
                query.take(limit);

                if (!isNil(page) && !isNil(limit)) {
                    query.skip(getSkip(page, limit));
                }

                // eslint-disable-next-line no-await-in-loop
                userData = userData.concat(await query.getMany());
            }
        }

        const data = [];
        let total = 0;

        userData.forEach(row => {
            row.user.features.forEach(userFeature => {
                if (
                    userFeature.feature.featureType === FeatureType.CONTROL_EMAIL_FREQUENCY &&
                    isTimeToSendEmail(userFeature, time, config.get('email.dailyHours'))
                ) {
                    data.push(row.user);
                    ++total;
                }
            });
        });

        return {
            data,
            page,
            limit,
            total,
        };
    }

    async getPersonnelIdsByFilters(
        requestDto: Partial<PersonnelBulkActionRequestDto>,
    ): Promise<number[]> {
        const { employmentStatuses, complianceCheckType, groupIds, q } = requestDto;

        const query = this.createQueryBuilder('Personnel')
            .distinct(true)
            .select('Personnel.id')
            .innerJoin('Personnel.user', 'User')
            .innerJoin('Personnel.complianceChecks', 'ComplianceCheck')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData');

        const complianceWithSearchQuery = resolveAllUserSearchQuery(query, q);
        const filtersToResolve = {
            query: complianceWithSearchQuery,
            employmentStatuses,
            groupIds,
        };

        if (!isNil(complianceCheckType)) {
            const complianceCheckTypeProp = ComplianceCheckTypePropNames.get(complianceCheckType);
            if (complianceCheckType === ComplianceCheckTypeOptions.FULL_COMPLIANCE) {
                filtersToResolve[complianceCheckTypeProp] = true;
            } else {
                filtersToResolve[complianceCheckTypeProp] = false;
            }
        }

        const getQuery = resolveFilters(filtersToResolve);
        const personnelIds = await getQuery.getMany();
        return map(personnelIds, 'id');
    }

    /**
     *
     * @param trainingType
     * @param personnelIds
     * @returns
     */
    getTrainingComplianceChecksPersonnel(
        trainingType: TrainingCampaignType,
        personnelId: number,
        clientType?: ClientType,
    ): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel');
        switch (trainingType) {
            case TrainingCampaignType.SECURITY_TRAINING:
                query.innerJoin(
                    'Personnel.securityTrainingComplianceChecks',
                    'TrainingComplianceCheck',
                );
                break;
            case TrainingCampaignType.HIPAA_TRAINING:
                query.innerJoin(
                    'Personnel.hipaaTrainingComplianceChecks',
                    'TrainingComplianceCheck',
                );
                break;
            case TrainingCampaignType.NIST_AI_TRAINING:
                query.innerJoin(
                    'Personnel.nistAiTrainingComplianceChecks',
                    'TrainingComplianceCheck',
                );
                break;
            default:
        }
        query
            .innerJoin('TrainingComplianceCheck.trainingCampaign', 'TrainingCampaign')
            .innerJoin('TrainingCampaign.connection', 'Connection')
            .addSelect([
                'TrainingComplianceCheck.id',
                'TrainingComplianceCheck.lastCheckedAt',
                'TrainingComplianceCheck.completedAt',
                'TrainingCampaign.trainingId',
                'TrainingCampaign.name',
                'TrainingCampaign.type',
            ])
            .where('TrainingCampaign.type = :trainingType', { trainingType })
            .andWhere('TrainingComplianceCheck.fk_personnel_id = :personnelId', { personnelId });
        if (clientType) {
            query.andWhere('TrainingCampaign.client_type = :clientType', {
                clientType,
            });
        }

        return query.getOne();
    }

    async getCurrentPersonnelFromUserIds(userIds: number[]): Promise<Personnel[]> {
        if (isEmpty(userIds)) {
            return [];
        }
        return this.find({
            where: {
                fkUserId: In(userIds),
                employmentStatus: In(CURRENT_STATUSES),
            },
        });
    }

    private resolveSort(
        query: SelectQueryBuilder<Personnel>,
        sort: SortType,
        sortDir: SortDir,
    ): void {
        // set the order
        const order = getSortDir(sortDir);
        // find the sort field (default is name if nothing was passed in)
        switch (sort) {
            case SortType.FULL_COMPLIANCE:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :fullComplianceJoinType',
                        {
                            fullComplianceJoinType: ComplianceCheckType.FULL_COMPLIANCE,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .orderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.EMPLOYMENT_STATUS:
                query
                    .addSelect('Personnel.employmentStatus')
                    .orderBy('Personnel.employmentStatus', order);
                break;
            case SortType.ACCEPTED_POLICIES:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :acceptedPoliciesType',
                        {
                            acceptedPoliciesType: ComplianceCheckType.ACCEPTED_POLICIES,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.IDENTITY_MFA:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :identityMfaType',
                        {
                            identityMfaType: ComplianceCheckType.IDENTITY_MFA,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.BG_CHECK:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :bgCheckType',
                        {
                            bgCheckType: ComplianceCheckType.BG_CHECK,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.AGENT_INSTALLED:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :agentInstalledType',
                        {
                            agentInstalledType: ComplianceCheckType.AGENT_INSTALLED,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.AUTO_UPDATES:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :autoUpdateType',
                        {
                            autoUpdateType: ComplianceCheckType.AUTO_UPDATES,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.OS_VERSION:
                query.orderBy('PersonnelData.osVersion', order);
                break;
            case SortType.SERIAL_NUM:
                query.orderBy('PersonnelData.serialNumber', order);
                break;
            case SortType.PASSWORD_MANAGER:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :type',
                        {
                            type: ComplianceCheckType.PASSWORD_MANAGER,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            // case SortType.LOCATION_SERVICES:
            //     query
            //         .innerJoin(
            //             'Personnel.complianceChecks',
            //             'orderJoin',
            //             'orderJoin.type = :locationServicesType',
            //             {
            //                 locationServicesType:
            //                     ComplianceCheckType.LOCATION_SERVICES,
            //             },
            //         )
            //         .addSelect('orderJoin.status')
            //         .addOrderBy('orderJoin.status', order)
            //         .addOrderBy('Personnel.id', order);
            //     break;
            case SortType.HDD_ENCRYPTION:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :hdEncryptedType',
                        {
                            hdEncryptedType: ComplianceCheckType.HDD_ENCRYPTION,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.ANTIVIRUS:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :antivirusType',
                        {
                            antivirusType: ComplianceCheckType.ANTIVIRUS,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.START_DATE:
                query.addSelect('Personnel.startDate').orderBy('Personnel.startDate', order);
                break;
            case SortType.SEPARATION_DATE:
                // add the ordering to the query
                query
                    .addSelect('Personnel.separationDate')
                    .orderBy('Personnel.separationDate', order);
                break;
            case SortType.LOCK_SCREEN:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :lockScreenType',
                        {
                            lockScreenType: ComplianceCheckType.LOCK_SCREEN,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.SECURITY_TRAINING:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :securityTrainingType',
                        {
                            securityTrainingType: ComplianceCheckType.SECURITY_TRAINING,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.HIPAA_TRAINING:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :hipaaTrainingType',
                        {
                            hipaaTrainingType: ComplianceCheckType.HIPAA_TRAINING,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.OFFBOARDING:
                query
                    .innerJoin(
                        'Personnel.complianceChecks',
                        'orderJoin',
                        'orderJoin.type = :offoboardingTrainingType',
                        {
                            offoboardingTrainingType: ComplianceCheckType.OFFBOARDING,
                        },
                    )
                    .addSelect('orderJoin.status')
                    .addOrderBy('orderJoin.status', order)
                    .addOrderBy('Personnel.id', order);
                break;
            case SortType.SYNC_STATUS:
                query
                    .addSelect('Personnel.statusUpdatedAt')
                    .orderBy('Personnel.statusUpdatedAt', order);
                break;
            case SortType.NAME:
            default:
                query
                    .addSelect('User.firstName')
                    .addSelect('User.lastName')
                    .orderBy('User.firstName', order)
                    .addOrderBy('User.lastName', order);
                break;
        }
    }

    /**
     * This function returns all active users
     *
     * @param pagination
     * @returns
     *
     */
    public async getAllActiveUsers(
        pagination: PaginationRequestDto,
    ): Promise<PaginationType<User>> {
        const { page, limit } = pagination;
        const skip = getSkip(page, limit);
        const query = this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.user', 'User')
            .where('Personnel.employmentStatus = :currentEmployee OR :currentContractor', {
                currentEmployee: EmploymentStatus.CURRENT_EMPLOYEE,
                currentContractor: EmploymentStatus.CURRENT_CONTRACTOR,
            })
            .skip(skip)
            .take(limit);
        const [personnel, total] = await query.getManyAndCount();
        const users: User[] = [];
        personnel.forEach(pers => {
            users.push(pers.user);
        });
        return {
            data: users,
            page,
            limit,
            total,
        };
    }

    private getQueryWithConnectionBase() {
        return (
            this.createQueryBuilder('Personnel')
                .leftJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
                .innerJoinAndSelect('Personnel.user', 'User')
                .innerJoinAndSelect('Personnel.data', 'PersonnelData')
                .leftJoinAndSelect('User.identities', 'UserIdentity')
                .leftJoinAndSelect('UserIdentity.connection', 'Connection')
                // left join since we are applying deletedAt filter
                .leftJoinAndSelect('User.roles', 'UserRole')
        );
    }

    async getCurrentPersonnelComplianceStats(): Promise<PersonnelStatusStats> {
        const statuses = [EmploymentStatus.CURRENT_EMPLOYEE, EmploymentStatus.CURRENT_CONTRACTOR];

        const { total, failing } = await this.createQueryBuilder('personnel')
            .innerJoin('personnel.complianceChecks', 'complianceCheck')
            .where('personnel.employmentStatus IN (:...statuses)', { statuses })
            .andWhere('complianceCheck.type = :fullCompliance', {
                fullCompliance: ComplianceCheckType.FULL_COMPLIANCE,
            })
            .select([
                'COUNT(DISTINCT personnel.id) AS total',
                `COUNT(DISTINCT CASE WHEN complianceCheck.status = :failStatus THEN personnel.id ELSE NULL END) AS failing`,
            ])
            .setParameters({ failStatus: ComplianceCheckStatus.FAIL })
            .getRawOne();

        return {
            total: Number(total),
            failingPersonnelCompliance: Number(failing),
        };
    }

    async getPolicyAcceptanceReportPersonnelData(
        dto: PersonnelRequestDto,
        page: number,
        limit: number,
        personnelIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const skip = getSkip(page, limit);
        const {
            q,
            employmentStatus,
            employmentStatuses,
            fullCompliance,
            acceptedPoliciesCompliance,
            identityMfaCompliance,
            bgCheckCompliance,
            agentInstalledCompliance,
            passwordManagerCompliance,
            autoUpdatesCompliance,
            locationServicesCompliance,
            hdEncryptionCompliance,
            antivirusCompliance,
            lockScreenCompliance,
            securityTrainingCompliance,
            hipaaTrainingCompliance,
            nistaiTrainingCompliance,
            deviceCompliance,
            multiSecurityTrainingCompliance,
            offboardingEvidence,
            mdmSourceType,
            inverseMdmSourceTypes,
            groupIds,
        } = dto;

        let query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .innerJoinAndSelect(
                'Personnel.complianceChecks',
                'ComplianceChecks',
                'ComplianceChecks.type = :checkType',
                { checkType: ComplianceCheckType.ACCEPTED_POLICIES },
            )
            .innerJoinAndSelect('User.userPolicyVersions', 'UserPolicyVersions')
            .leftJoinAndSelect(
                'UserPolicyVersions.policyVersion',
                'PolicyVersion',
                'PolicyVersion.current = 1 AND PolicyVersion.status = :policyVersionStatus',
                { policyVersionStatus: PolicyVersionStatus.PUBLISHED },
            )
            .skip(skip)
            .take(limit);

        this.resolveSearchQ(query, q);

        query = resolveFilters({
            query,
            employmentStatus,
            employmentStatuses,
            fullCompliance,
            acceptedPoliciesCompliance,
            identityMfaCompliance,
            bgCheckCompliance,
            agentInstalledCompliance,
            passwordManagerCompliance,
            autoUpdatesCompliance,
            locationServicesCompliance,
            hdEncryptionCompliance,
            antivirusCompliance,
            lockScreenCompliance,
            securityTrainingCompliance,
            hipaaTrainingCompliance,
            nistaiTrainingCompliance,
            deviceCompliance,
            multiSecurityTrainingCompliance,
            offboardingEvidence,
            mdmSourceType,
            inverseMdmSourceTypes,
            groupIds,
        });

        if (personnelIds) {
            query.andWhereInIds(personnelIds);
        }

        const [data, total] = await query.getManyAndCount();
        return {
            data,
            total,
            page,
            limit,
        };
    }

    /**
     * Get personnel who have accepted policies assigned to a specific control
     * @param controlId - The control ID to filter policies by
     * @param personnelIds - An array of personnel IDs to filter by
     * @returns Promise<Personnel[]>
     */
    async getPersonnelWithAcceptedControlPolicies(
        controlId: number,
        personnelIds: number[],
    ): Promise<Personnel[]> {
        return this.find({
            where: {
                id: In(personnelIds),
                complianceChecks: {
                    type: ComplianceCheckType.ACCEPTED_POLICIES,
                },
                user: {
                    userPolicyVersions: {
                        acceptedAt: Not(IsNull()),
                        policyVersion: {
                            current: true,
                            policyVersionStatus: PolicyVersionStatus.PUBLISHED,
                        },
                        policy: {
                            controls: {
                                id: controlId,
                            },
                        },
                    },
                },
            },
            relations: {
                user: {
                    userPolicyVersions: {
                        policyVersion: true,
                        policy: {
                            controls: true,
                        },
                    },
                },
                complianceChecks: true,
            },
        });
    }

    async findAllPersonnelWithRelations(
        take: number,
        skip: number,
        relations: string[],
    ): Promise<Personnel[]> {
        return this.find({
            take,
            skip,
            relations,
            loadEagerRelations: false,
        });
    }

    /**
     *
     * @param personnelIds Array of personnel IDs to fetch
     * @returns Promise<Personnel[]> Personnel with user, identities, and connection data
     */
    async findPersonnelWithUserIdentitiesAndConnections(
        personnelIds: number[],
    ): Promise<Personnel[]> {
        return this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .where('Personnel.id IN (:...personnelIds)', {
                personnelIds,
            })
            .getMany();
    }

    async findAll(take: number, skip: number): Promise<Personnel[]> {
        try {
            const [personnel, personnelWithUser, personnelWithComplianeChecks, personnelWithGroup] =
                await Promise.all([
                    this.findAllPersonnelWithRelations(take, skip, []),
                    this.findAllPersonnelWithRelations(take, skip, ['user']),
                    this.findAllPersonnelWithRelations(take, skip, ['complianceChecks']),
                    this.findAllPersonnelWithRelations(take, skip, ['groups', 'groups.group']),
                ]);

            return personnel.map(per => {
                per.user = personnelWithUser.find(p => p.id === per.id)?.user;
                per.complianceChecks =
                    personnelWithComplianeChecks?.find(p => p.id === per.id)?.complianceChecks ??
                    [];
                per.groups = personnelWithGroup?.find(p => p.id === per.id)?.groups ?? [];
                return per;
            });
        } catch (error) {
            PolloLogger.logger(this.constructor.name).error(
                PolloMessage.msg(`findAll: Failed called for take:${take} and skip:${skip}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.findAll.name)
                    .setError(error),
            );
            throw error;
        }
    }

    async resetPersonnelStatusUpdatedAt(personnelIds: number[]): Promise<Personnel[]> {
        await this.createQueryBuilder('Personnel')
            .update(Personnel)
            .set({ statusUpdatedAt: null })
            .where({
                id: In(personnelIds),
                employmentStatus: Not(
                    In([EmploymentStatus.OUT_OF_SCOPE, EmploymentStatus.SERVICE_ACCOUNT]),
                ),
                statusUpdatedAt: Not(IsNull()),
            })
            .execute();

        return this.find({
            where: {
                id: In(personnelIds),
            },
        });
    }

    /**
     *
     * @param auditPersonnelType The type of personnel
     * @param startDate audit start date
     * @param endDate audit end date
     * @param page the page querying for
     * @param limit the number of elements per page
     * @param searchTerms the string used to match the filtered users by name
     *
     * @returns Promise<PaginationType<Personnel>>
     */
    async getPaginatedPersonnelByTypeWithinRange(
        auditPersonnelType: AuditPersonnelType,
        startDate: string,
        endDate: string,
        page: number,
        limit: number,
        searchTerms?: string,
    ): Promise<PaginationType<Personnel>> {
        const skip = getSkip(page, limit);

        const queryBuilder = this.createQueryBuilder('personnel')
            .select([
                'personnel.id',
                'personnel.employmentStatus',
                'personnel.separationDate',
                'personnel.createdAt',
                'personnel.updatedAt',
                'personnel.statusUpdatedAt',
                'user.firstName',
                'user.lastName',
                'user.email',
                'user.avatar',
            ])
            .innerJoin('personnel.user', 'user')
            .andWhere('personnel.employmentStatus IN (:...statuses)', {
                statuses: AuditPersonnelTypeToEmployeeStatus[auditPersonnelType],
            })
            .orderBy('user.lastName', 'ASC')
            .addOrderBy('user.firstName', 'ASC')
            .take(limit)
            .skip(skip);

        switch (auditPersonnelType) {
            case AuditPersonnelType.HIRED_WITHIN_DATE_RANGE:
                queryBuilder.andWhere('personnel.startDate BETWEEN :startDate AND :endDate', {
                    startDate,
                    endDate,
                });
                break;
            case AuditPersonnelType.FIRED_WITHIN_DATE_RANGE:
                queryBuilder.andWhere('personnel.separationDate BETWEEN :startDate AND :endDate', {
                    startDate,
                    endDate,
                });
                break;
            case AuditPersonnelType.CURRENT_WITHIN_DATE_RANGE:
                queryBuilder
                    .andWhere('personnel.startDate < :startDate', { startDate })
                    .andWhere(
                        '(personnel.separationDate > :endDate OR personnel.separationDate IS NULL)',
                        {
                            endDate,
                        },
                    );
                break;
        }

        if (searchTerms) {
            const searchPattern = `%${searchTerms}%`;
            queryBuilder.andWhere(
                '(user.firstName LIKE :search OR user.lastName LIKE :search OR user.email LIKE :search)',
                { search: searchPattern },
            );
        }

        const [personnel, total] = await queryBuilder.getManyAndCount();

        return {
            data: personnel,
            total,
            page,
            limit,
        };
    }

    async getPaginatedPersonnelForAuditSample({
        audit,
        auditSampleId,
        page,
        limit,
        auditPersonnelType,
        searchTerms,
    }: {
        audit: Audit;
        auditSampleId: number;
        page: number;
        limit: number;
        auditPersonnelType?: AuditPersonnelType;
        searchTerms?: string;
    }): Promise<PaginationType<Personnel>> {
        const personnelQueryConditions = getAuditPersonnelPeriodWhereConditions({
            startDate: audit.startDate,
            endDate: audit.endDate,
            auditPersonnelType,
            searchTerms,
        });

        const employmentStatusFilter: FindOptionsWhere<Personnel> = auditPersonnelType
            ? {
                  employmentStatus: In(AuditPersonnelTypeToEmployeeStatus[auditPersonnelType]),
              }
            : {};

        const skip = getSkip(page, limit);

        const [personnel, total] = await this.findAndCount({
            where: {
                ...personnelQueryConditions,
                ...employmentStatusFilter,
                auditorFrameworkSamplePersonnelMap: {
                    auditorFrameworkSample: {
                        id: auditSampleId,
                        auditorFrameworkId: audit.id,
                    },
                },
            },
            relations: {
                user: true,
            },
            take: limit,
            skip,
            order: {
                user: {
                    lastName: 'ASC',
                    firstName: 'ASC',
                },
            },
        });

        return {
            data: personnel,
            total,
            page,
            limit,
        };
    }

    public async getActivePersonnelByComplianceCheckType(
        pagination: PaginationRequestDto,
        complianceCheckType: ComplianceCheckType,
    ): Promise<PaginationType<Personnel>> {
        const { page, limit } = pagination;
        const skip = getSkip(page, limit);

        const query = this.createQueryBuilder(PERSONNEL)
            .select([
                `${PERSONNEL}.id`,
                `${COMPLIANCE_CHECK}.id`,
                `${COMPLIANCE_CHECK}.completionDate`,
                `${COMPLIANCE_CHECK}.type`,
                `${USER}.id`,
                `${USER_DOCUMENT}.id`,
                `${USER_DOCUMENT}.renewalDate`,
                `${USER_DOCUMENT}.type`,
            ])
            .innerJoin(`${PERSONNEL}.complianceChecks`, COMPLIANCE_CHECK)
            .leftJoin(`${PERSONNEL}.user`, USER)
            .leftJoin(`${USER}.documents`, USER_DOCUMENT)
            .where(`${PERSONNEL}.employmentStatus IN(:...employmentStatuses)`, {
                employmentStatuses: [
                    EmploymentStatus.CURRENT_EMPLOYEE,
                    EmploymentStatus.CURRENT_CONTRACTOR,
                ],
            })
            .andWhere(`${COMPLIANCE_CHECK}.type = :complianceCheckType`, {
                complianceCheckType,
            })
            .andWhere(`${COMPLIANCE_CHECK}.status = :complianceCheckStatus`, {
                complianceCheckStatus: ComplianceCheckStatus.PASS,
            })
            .andWhere(`${COMPLIANCE_CHECK}.completionDate IS NOT NULL`)
            .skip(skip)
            .take(limit);

        const [personnel, total] = await query.getManyAndCount();

        return {
            data: personnel,
            page,
            limit,
            total,
        };
    }

    async countSampledHiredPersonnelInDateRange(
        personnelIds: number[],
        firstDate: string,
        secondDate: string,
    ): Promise<number> {
        if (isEmpty(personnelIds)) {
            return 0;
        }
        return this.createQueryBuilder('Personnel')
            .where('Personnel.start_date BETWEEN :firstDate AND :secondDate', {
                firstDate,
                secondDate,
            })
            .andWhere('id IN (:...personnelIds)', { personnelIds })
            .getCount();
    }

    async countSampledFormerPersonnelInDateRange(
        personnelIds: number[],
        firstDate: string,
        secondDate: string,
    ): Promise<number> {
        if (isEmpty(personnelIds)) {
            return 0;
        }
        return this.createQueryBuilder('Personnel')
            .where('Personnel.separation_date' + ' BETWEEN :firstDate AND :secondDate', {
                firstDate,
                secondDate,
            })
            .andWhere(`id IN (:...personnelIds)`, { personnelIds })
            .getCount();
    }

    async countSampledCurrentPersonnelInDateRange(
        personnelIds: number[],
        firstDate: string,
        secondDate: string,
    ): Promise<number> {
        if (isEmpty(personnelIds)) {
            return 0;
        }
        return this.createQueryBuilder('Personnel')
            .where('Personnel.start_date ' + '<= :firstDate', {
                firstDate,
            })
            .andWhere(
                new Brackets(qb => {
                    qb.where('Personnel.separation_date IS NULL').orWhere(
                        new Brackets(qb2 => {
                            qb2.where('Personnel.separation_date ' + '>= :secondDate', {
                                secondDate,
                            });
                        }),
                    );
                }),
            )
            .andWhere(`id IN (:...personnelIds)`, { personnelIds })
            .getCount();
    }

    private async findPersonnelWithUserData(id: number): Promise<Personnel> {
        const query = this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.user', 'User')
            .leftJoinAndSelect('User.identities', 'UserIdentity')
            .leftJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('Connection.connectionProviderTypes', 'ConnectionProviderType')
            .leftJoinAndSelect('User.backgroundChecks', 'BackgroundCheck')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .where('Personnel.id = :id', { id });

        const personnel: Personnel = await query.getOne();

        await this.setPersonnelTrainingDocuments([personnel]);

        return personnel;
    }

    private findPersonnelWithReasonProvider(id: number): Promise<Personnel> {
        return this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider')
            .leftJoinAndSelect('ReasonProvider.roles', 'ReasonProviderRoles')
            .where('Personnel.id = :id', { id })
            .getOne();
    }

    private findPersonnelWithPersonnelData(id: number): Promise<Personnel> {
        return this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.data', 'PersonnelData')
            .where('Personnel.id = :id', { id })
            .getOne();
    }

    private findPersonnelWithComplianceChecks(id: number): Promise<Personnel> {
        return this.createQueryBuilder('Personnel')
            .innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck')
            .where('Personnel.id = :id', { id })
            .getOne();
    }

    private findPersonnelWithDevices(id: number): Promise<Personnel> {
        return this.createQueryBuilder('Personnel')
            .leftJoinAndSelect('Personnel.devices', 'Devices')
            .leftJoinAndSelect('Devices.complianceChecks', 'DeviceComplianceChecks')
            .leftJoinAndSelect('Devices.identifiers', 'Identifiers')
            .leftJoinAndSelect('Devices.documents', 'Documents')
            .leftJoinAndSelect('Documents.device', 'DocumentDevice')
            .where('Personnel.id = :id', { id })
            .getOne();
    }

    async getPersonnelNamesByIds(personnelIds: number[]): Promise<Personnel[]> {
        return this.createQueryBuilder('Personnel')
            .select(['Personnel.id', 'User.firstName', 'User.lastName'])
            .innerJoin('Personnel.user', 'User')
            .where('Personnel.id IN (:...personnelIds)', { personnelIds })
            .getMany();
    }

    async getCurrentNonCompliantPersonnel(staleHoursThreshold?: number): Promise<Personnel[]> {
        const query = this.createQueryBuilder('Personnel')
            .select(['Personnel.id'])
            .innerJoin(
                'Personnel.complianceChecks',
                'fullComplianceFilter',
                'fullComplianceFilter.type = :fullComplianceFilterType',
                {
                    fullComplianceFilterType: ComplianceCheckType.FULL_COMPLIANCE,
                },
            )
            .where('Personnel.employmentStatus IN(:...employmentStatuses)', {
                employmentStatuses: [
                    EmploymentStatus.CURRENT_EMPLOYEE,
                    EmploymentStatus.CURRENT_CONTRACTOR,
                ],
            });

        const isValidHours = staleHoursThreshold !== undefined && staleHoursThreshold > 0;
        if (isValidHours) {
            const staleThresholdDate = new Date();
            staleThresholdDate.setHours(staleThresholdDate.getHours() - staleHoursThreshold);

            query
                .leftJoin(
                    'Personnel.devices',
                    'device',
                    'device.sourceType = :agentSourceType AND device.lastCheckedAt < :staleThresholdDate AND device.deletedAt IS NULL',
                    {
                        agentSourceType: MobileDeviceManagementSourceType.AGENT,
                        staleThresholdDate,
                    },
                )
                .andWhere('(fullComplianceFilter.status = :status OR device.id IS NOT NULL)', {
                    status: ComplianceCheckStatus.FAIL,
                });
        } else {
            query.andWhere('fullComplianceFilter.status = :status', {
                status: ComplianceCheckStatus.FAIL,
            });
        }

        return query.getMany();
    }

    getPersonnelForNotifications(
        personnel: Personnel[],
        loadDevicesCheckedAt: boolean = false,
    ): Promise<Personnel[]> {
        return this.find({
            select: {
                id: true,
                employmentStatus: true,
                user: { id: true, firstName: true, lastName: true, email: true },
                complianceChecks: { id: true, type: true, status: true },
                ...(loadDevicesCheckedAt && {
                    devices: { id: true, lastCheckedAt: true },
                }),
            },
            where: { id: In(personnel.map(({ id }) => id)) },
            relations: {
                user: { roles: false, userRoles: false },
                complianceChecks: true,
                data: false,
                ...(loadDevicesCheckedAt && {
                    devices: true,
                }),
            },
            loadEagerRelations: false,
        });
    }

    private expandSubCollections(
        query: SelectQueryBuilder<Personnel>,
        expand: PersonnelExpand[],
    ): void {
        const expandMapping = {
            [PersonnelExpand.complianceChecks]: () =>
                query.innerJoinAndSelect('Personnel.complianceChecks', 'ComplianceCheck'),
            [PersonnelExpand.reasonProvider]: () =>
                query.leftJoinAndSelect('Personnel.reasonProvider', 'ReasonProvider'),
            [PersonnelExpand.user]: () => query.innerJoinAndSelect('Personnel.user', 'User'),
        };
        [...new Set(expand)].forEach(prop => expandMapping[prop]?.());
    }

    private applyFilters(query: SelectQueryBuilder<Personnel>, request: PersonnelListRequestType) {
        const filters: { where: string; params: ObjectLiteral }[] = [];

        if (!isEmpty(request.employmentStatus)) {
            filters.push({
                where: 'Personnel.employmentStatus IN (:...employmentStatus)',
                params: { employmentStatus: request.employmentStatus },
            });
        }

        for (const filter of filters) {
            query.andWhere(filter.where, filter.params);
        }
    }

    async countByPersonnelRequestDto(dto: PersonnelRequestDto): Promise<number> {
        const query = this.createQueryBuilder('Personnel').innerJoin('Personnel.user', 'User');
        this.resolveSearchQ(query, dto.q);
        return resolveFilters({
            query,
            ...dto,
        }).getCount();
    }
}
