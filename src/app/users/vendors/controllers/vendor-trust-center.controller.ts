import { MongoAbility } from '@casl/ability';
import { Controller, Get, Param, ParseIntPipe } from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags,
} from '@nestjs/swagger';
import { AppController } from 'app/app.controller';
import { User } from 'app/users/entities/user.entity';
import { VendorTrustCenterAccessRequestRequirementsResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-requirements-response.dto';
import { VendorTrustCenterAccessRequestResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-response.dto';
import { VendorTrustCenterCertificationsResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-certifications-response.dto';
import { VendorTrustCenterDocumentDownloadResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-document-download-response.dto';
import { VendorTrustCenterDocumentsResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-documents-response.dto';
import { VendorTrustCenterItemsByCategoryListResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-items-by-category-list-response.dto';
import { VendorTrustCenterSubProcessorsResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-legal-response.dto';
import { VendorTrustCenterOverviewResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-overview-response.dto';
import { VendorTrustCenterResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-response.dto';
import { VendorTrustCenterAccessRequest } from 'app/users/vendors/entities/vendor-trust-center-access-request.entity';
import { rolesToApiDoc, VRM_ROLES } from 'app/users/vendors/helpers/vendors-roleset';
import { VendorTrustCenterCertification } from 'app/users/vendors/types/vendor-trust-center-certification.type';
import { VendorTrustCenterDocument } from 'app/users/vendors/types/vendor-trust-center-document.type';
import { VendorTrustCenterItemsByCategory } from 'app/users/vendors/types/vendor-trust-center-items-by-category.type';
import { VendorTrustCenterOverview } from 'app/users/vendors/types/vendor-trust-center-overview.type';
import { VendorTrustCenterSubProcessor } from 'app/users/vendors/types/vendor-trust-center-subprocessor.type';
import { VendorTrustCenter } from 'app/users/vendors/types/vendor-trust-center.type';
import { VendorsTrustCenterService } from 'app/users/vendors/vendors-trust-center.service';
import { VendorsRoute } from 'app/users/vendors/vendors.routes';
import { Account } from 'auth/entities/account.entity';
import { CheckAbilities } from 'casl/ability.handlers';
import { Action } from 'casl/actions';
import { Dto } from 'commons/decorators/dto.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import {
    SBAccessRequestRequirements,
    SBTrustCenterDocumentDownload,
} from 'dependencies/safebase/safebase-vendor-trust-center.types';

@ApiTags('Vendor Trust Center')
@Controller()
@ProductArea(Area.VENDOR_PROFILE)
export class VendorTrustCenterController extends AppController {
    constructor(private readonly vendorsTrustCenterService: VendorsTrustCenterService) {
        super();
    }

    @ApiOperation({
        description: `Get vendor trust center
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER)
    getVendorTrustCenter(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenter> {
        return this.vendorsTrustCenterService.getVendorTrustCenter(account, vendorId);
    }

    @ApiOperation({
        description: `Get vendor trust center overview
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterOverviewResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterOverviewResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_OVERVIEW)
    getVendorTrustCenterOverview(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterOverview> {
        return this.vendorsTrustCenterService.getVendorTrustCenterOverview(account, vendorId);
    }

    @ApiOperation({
        description: `Get vendor trust center certifications
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterCertificationsResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterCertificationsResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_CERTIFICATIONS)
    getVendorTrustCenterCertifications(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterCertification[]> {
        return this.vendorsTrustCenterService.getVendorTrustCenterCertifications(
            account,
            vendorId,
            user,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center sub-processors
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterSubProcessorsResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterSubProcessorsResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_SUB_PROCESSORS)
    getVendorTrustCenterSubProcessors(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterSubProcessor[]> {
        return this.vendorsTrustCenterService.getVendorTrustCenterSubProcessors(
            account,
            vendorId,
            user,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center documents
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterDocumentsResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterDocumentsResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_DOCUMENTS)
    getVendorTrustCenterDocuments(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterDocument[]> {
        return this.vendorsTrustCenterService.getVendorTrustCenterDocuments(
            account,
            vendorId,
            user,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center document by id
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterDocumentDownloadResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterDocumentDownloadResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_DOCUMENT_DOWNLOAD)
    getVendorTrustCenterDocumentById(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
        @Param('documentId') documentId: string,
    ): Promise<SBTrustCenterDocumentDownload> {
        return this.vendorsTrustCenterService.getVendorTrustCenterDocumentsById(
            account,
            vendorId,
            documentId,
            user,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center items
        <br><br>
        Allowed Roles: [${rolesToApiDoc(...VRM_ROLES.READ_ONLY)}]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterItemsByCategoryListResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(...VRM_ROLES.READ_ONLY)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterItemsByCategoryListResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_ITEMS)
    getVendorTrustCenterItemsByCategory(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterItemsByCategory[]> {
        return this.vendorsTrustCenterService.getVendorTrustCenterItemsByCategory(
            account,
            vendorId,
            user,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center access request information
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.RISK_MANAGER]}
        ]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterAccessRequestResponseDto || ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.ACT_AS_READ_ONLY,
        Role.RISK_MANAGER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterAccessRequestResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_ACCESS_REQUEST)
    getVendorTrustCenterAccessRequest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<VendorTrustCenterAccessRequest> {
        return this.vendorsTrustCenterService.getVendorTrustCenterAccessRequest(
            account,
            user,
            vendorId,
        );
    }

    @ApiOperation({
        description: `Get vendor trust center access request requirements
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.RISK_MANAGER]}
        ]`,
    })
    @ApiOkResponse({
        type: VendorTrustCenterAccessRequestRequirementsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.ACT_AS_READ_ONLY,
        Role.RISK_MANAGER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    @Dto(VendorTrustCenterAccessRequestRequirementsResponseDto)
    @Get(VendorsRoute.GET_VENDOR_TRUST_CENTER_ACCESS_REQUEST_REQUIREMENTS)
    getVendorTrustCenterAccessRequestRequirements(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', ParseIntPipe) vendorId: number,
    ): Promise<SBAccessRequestRequirements> {
        return this.vendorsTrustCenterService.getVendorTrustCenterAccessRequestRequirements(
            account,
            user,
            vendorId,
        );
    }
}
