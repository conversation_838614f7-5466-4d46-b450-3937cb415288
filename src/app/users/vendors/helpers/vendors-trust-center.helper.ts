import { VendorTrustCenterAccessRequestStatus } from 'app/users/vendors/enums/vendor-trust-center-access-request-status.enum';

/**
 * Check if the access token is expired based on multiple criteria
 */
export const isTokenExpired = (accessRequest: { expiresAt: Date; status: string }): boolean => {
    if (accessRequest.status === VendorTrustCenterAccessRequestStatus.EXPIRED) {
        return true;
    }

    const expiresAt = accessRequest.expiresAt ? new Date(accessRequest.expiresAt) : null;
    if (!expiresAt || isNaN(expiresAt.getTime())) {
        return true;
    }

    const now = new Date();
    return expiresAt <= now;
};
