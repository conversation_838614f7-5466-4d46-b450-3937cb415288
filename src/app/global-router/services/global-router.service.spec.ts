import { Test, TestingModule } from '@nestjs/testing';
import {
    EntrySyncOptions,
    GlobalRouterService,
} from 'app/global-router/services/global-router.service';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloLogger } from 'pollo-logger/pollo.logger';

jest.mock('pollo-logger/pollo.logger');

describe('GlobalRouterService', () => {
    let service: GlobalRouterService;
    let entryRepository: jest.Mocked<EntryRepository>;
    let entryCoreService: jest.Mocked<EntryCoreService>;
    let mockLogger: jest.Mocked<any>;
    let mockQueryBuilder: any;

    const mockEntries = [
        { id: '1', domain: 'test1.com', createdAt: new Date(), deletedAt: null },
        { id: '2', domain: 'test2.com', createdAt: new Date(), deletedAt: null },
        { id: '3', domain: 'test1.com', createdAt: new Date(), deletedAt: new Date() },
    ];

    beforeEach(async () => {
        mockLogger = {
            log: jest.fn(),
            warn: jest.fn(),
        };

        (PolloLogger.logger as jest.Mock) = jest.fn().mockReturnValue(mockLogger);

        mockQueryBuilder = {
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            withDeleted: jest.fn().mockReturnThis(),
            getMany: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                GlobalRouterService,
                {
                    provide: EntryRepository,
                    useValue: {
                        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
                    },
                },
                {
                    provide: EntryCoreService,
                    useValue: {
                        publishEntrySyncReadEvent: jest.fn(),
                        publishEntrySyncReadSoftDeletedEvent: jest.fn(),
                    },
                },
            ],
        }).compile();

        service = module.get<GlobalRouterService>(GlobalRouterService);
        entryCoreService = module.get<EntryCoreService>(EntryCoreService);
        entryRepository = module.get(EntryRepository);

        jest.spyOn(global, 'setTimeout').mockImplementation((fn: any) => {
            fn();
            return {} as any;
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('syncEntryTable', () => {
        const defaultOptions: EntrySyncOptions = {
            batchSize: 2,
            dryRun: false,
        };

        it('should call repository methods for basic sync', async () => {
            mockQueryBuilder.getMany
                .mockResolvedValueOnce(mockEntries.slice(0, 2))
                .mockResolvedValueOnce([]);

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock).mockResolvedValue({
                success: true,
            });

            await service.syncEntryTable(defaultOptions);

            expect(entryRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
            expect(mockQueryBuilder.skip).toHaveBeenCalledTimes(2);
            expect(mockQueryBuilder.take).toHaveBeenCalledTimes(2);
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledTimes(2);
            expect(mockQueryBuilder.getMany).toHaveBeenCalledTimes(2);
            expect(entryCoreService.publishEntrySyncReadEvent).toHaveBeenCalledTimes(2);
        });

        it('should not call publishEntryEvent in dry run mode', async () => {
            mockQueryBuilder.getMany
                .mockResolvedValueOnce(mockEntries.slice(0, 2))
                .mockResolvedValueOnce([]);

            await service.syncEntryTable({
                ...defaultOptions,
                dryRun: true,
            });

            expect(mockQueryBuilder.getMany).toHaveBeenCalledTimes(2);
            expect(entryCoreService.publishEntrySyncReadEvent).not.toHaveBeenCalled();
        });

        it('should call where method when domain is specified', async () => {
            mockQueryBuilder.getMany.mockResolvedValueOnce([]);

            await service.syncEntryTable({
                ...defaultOptions,
                domain: 'test.com',
            });

            expect(mockQueryBuilder.where).toHaveBeenCalledTimes(1);
        });

        it('should call withDeleted when includeDeleted is true', async () => {
            mockQueryBuilder.getMany.mockResolvedValueOnce([]);

            await service.syncEntryTable({
                ...defaultOptions,
                includeDeleted: true,
            });

            expect(mockQueryBuilder.withDeleted).toHaveBeenCalledTimes(1);
        });

        it('should stop querying when maxEntries is reached', async () => {
            mockQueryBuilder.getMany
                .mockResolvedValueOnce(mockEntries.slice(0, 2))
                .mockResolvedValueOnce(mockEntries.slice(2, 3));

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock).mockResolvedValue({
                success: true,
            });

            await service.syncEntryTable({
                ...defaultOptions,
                maxEntries: 2,
            });

            expect(mockQueryBuilder.getMany).toHaveBeenCalledTimes(1);
            expect(mockLogger.log).toHaveBeenCalled();
        });

        it('should stop processing when failure threshold is exceeded', async () => {
            mockQueryBuilder.getMany.mockResolvedValueOnce(mockEntries.slice(0, 2));

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock).mockResolvedValue({
                success: false,
                error: new Error('Test error'),
            });

            const result = await service.syncEntryTable({
                ...defaultOptions,
                failureThreshold: 1,
            });

            expect(entryCoreService.publishEntrySyncReadEvent).toHaveBeenCalledTimes(2);
            expect(result.aborted).toBe(true);
            expect(mockLogger.log).toHaveBeenCalledTimes(3);
        });

        it('should call publish for each entry in batch', async () => {
            mockQueryBuilder.getMany.mockResolvedValueOnce(mockEntries).mockResolvedValueOnce([]);

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock).mockResolvedValue({
                success: true,
            });
            (entryCoreService.publishEntrySyncReadSoftDeletedEvent as jest.Mock).mockResolvedValue({
                success: true,
            });

            await service.syncEntryTable({
                ...defaultOptions,
                batchSize: 5,
            });

            expect(entryCoreService.publishEntrySyncReadEvent).toHaveBeenCalledTimes(2);
            expect(entryCoreService.publishEntrySyncReadSoftDeletedEvent).toHaveBeenCalledTimes(1);
        });

        it('should continue processing multiple batches', async () => {
            mockQueryBuilder.getMany
                .mockResolvedValueOnce(mockEntries.slice(0, 2))
                .mockResolvedValueOnce(mockEntries.slice(2, 3))
                .mockResolvedValueOnce([]);

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock).mockResolvedValue({
                success: true,
            });
            (entryCoreService.publishEntrySyncReadSoftDeletedEvent as jest.Mock).mockResolvedValue({
                success: true,
            });

            await service.syncEntryTable(defaultOptions);

            expect(mockQueryBuilder.getMany).toHaveBeenCalledTimes(3);
            expect(entryCoreService.publishEntrySyncReadEvent).toHaveBeenCalledTimes(2);
            expect(entryCoreService.publishEntrySyncReadSoftDeletedEvent).toHaveBeenCalledTimes(1);
        });

        it('should log warnings for failed entries but continue', async () => {
            mockQueryBuilder.getMany
                .mockResolvedValueOnce(mockEntries.slice(0, 2))
                .mockResolvedValueOnce([]);

            (entryCoreService.publishEntrySyncReadEvent as jest.Mock)
                .mockResolvedValueOnce({ success: false })
                .mockResolvedValueOnce({ success: true });

            await service.syncEntryTable({
                ...defaultOptions,
                failureThreshold: 60,
            });

            expect(entryCoreService.publishEntrySyncReadEvent).toHaveBeenCalledTimes(2);
            expect(mockLogger.warn).toHaveBeenCalledTimes(1);
        });
    });
});
