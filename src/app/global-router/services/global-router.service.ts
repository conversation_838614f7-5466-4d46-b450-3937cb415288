import { Injectable } from '@nestjs/common';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { EntryCoreService } from 'auth/services/entry-core.service';
import PQueue from 'p-queue';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export interface EntrySyncOptions {
    batchSize: number;
    dryRun: boolean;
    domain?: string;
    includeDeleted?: boolean;
    failureThreshold?: number;
    maxEntries?: number;
    batchDelayMs?: number;
}

export interface EntrySyncResponseDto {
    message: string;
    processedCount: number;
    accountsProcessed?: number;
    failedCount?: number;
    aborted?: boolean;
}

@Injectable()
export class GlobalRouterService {
    private readonly logger = PolloLogger.logger();

    constructor(
        private readonly entryRepository: EntryRepository,
        private readonly entryCoreService: EntryCoreService,
    ) {}

    async syncEntryTable(options: EntrySyncOptions): Promise<EntrySyncResponseDto> {
        const {
            batchSize,
            dryRun,
            domain,
            includeDeleted,
            failureThreshold = 10,
            maxEntries,
            batchDelayMs,
        } = options;

        this.logger.log(
            PolloMessage.msg('Starting entry global sync')
                .setContext('GlobalRouterService')
                .setMetadata(options as any),
        );

        let offset = 0;
        let processedCount = 0;
        let failedCount = 0;
        const accountsProcessed = new Set<string>();

        const queue = new PQueue({ concurrency: 10 });

        while (true) {
            if (maxEntries && maxEntries > 0 && processedCount >= maxEntries) {
                this.logger.log(
                    PolloMessage.msg(`Reached maximum entries limit: ${maxEntries}`).setContext(
                        'GlobalRouterService',
                    ),
                );
                break;
            }

            const queryBuilder = this.entryRepository
                .createQueryBuilder('entry')
                .skip(offset)
                .take(batchSize)
                .orderBy('entry.createdAt', 'ASC');

            if (domain) {
                queryBuilder.where('entry.domain = :domain', { domain: domain });
            }

            if (includeDeleted) {
                queryBuilder.withDeleted();
            }

            /* eslint-disable no-await-in-loop */
            const entries = await queryBuilder.getMany();

            if (entries.length === 0) {
                break;
            }

            const batchFailures: string[] = [];

            if (!dryRun) {
                const promiseResults = await Promise.allSettled(
                    entries.map(entry =>
                        queue.add(async () => {
                            accountsProcessed.add(entry.domain);
                            if (entry.deletedAt) {
                                return {
                                    entry,
                                    publishResult:
                                        await this.entryCoreService.publishEntrySyncReadSoftDeletedEvent(
                                            entry,
                                        ),
                                };
                            } else {
                                return {
                                    entry,
                                    publishResult:
                                        await this.entryCoreService.publishEntrySyncReadEvent(
                                            entry,
                                        ),
                                };
                            }
                        }),
                    ),
                );

                for (const promiseResult of promiseResults) {
                    if (promiseResult.status === 'fulfilled') {
                        const { entry, publishResult } = promiseResult.value;
                        if (!publishResult.success) {
                            failedCount++;
                            batchFailures.push(entry.id);
                        }
                    } else {
                        failedCount++;
                    }
                }
            } else {
                for (const entry of entries) {
                    accountsProcessed.add(entry.domain);
                }
            }

            processedCount += entries.length;
            offset += batchSize;

            if (batchDelayMs && batchDelayMs > 0) {
                await new Promise(resolve => setTimeout(resolve, batchDelayMs));
            }

            this.logger.log(
                PolloMessage.msg(`Processed batch: ${entries.length} entries`)
                    .setContext('GlobalRouterService')
                    .setMetadata({
                        offset,
                        processedCount,
                        failedCount,
                        batchSize: entries.length,
                        batchFailures: !dryRun ? batchFailures?.length || 0 : 0,
                        batchFailureRate:
                            !dryRun && entries.length > 0
                                ? (((batchFailures?.length || 0) / entries.length) * 100).toFixed(1)
                                : 0,
                        overallFailureRate:
                            processedCount > 0
                                ? ((failedCount / processedCount) * 100).toFixed(1)
                                : 0,
                        failureThreshold,
                        accountsProcessedCount: accountsProcessed.size,
                        isDryRun: dryRun,
                    }),
            );

            const batchFailureRate = (batchFailures.length / entries.length) * 100;
            const overallFailureRate = (failedCount / processedCount) * 100;

            if (batchFailureRate >= failureThreshold || overallFailureRate >= failureThreshold) {
                const errorMsg =
                    `Entry sync aborted: ${batchFailures.length}/${entries.length} failures in current batch ` +
                    `(${batchFailureRate.toFixed(1)}%), ${failedCount}/${processedCount} overall (${overallFailureRate.toFixed(1)}%)`;
                this.logger.log(
                    PolloMessage.msg(errorMsg).setContext('GlobalRouterService').setMetadata({
                        batchFailures,
                        batchFailureRate,
                        overallFailureRate,
                        failureThreshold,
                        processedCount,
                        failedCount,
                    }),
                );
                return {
                    message: errorMsg,
                    processedCount,
                    accountsProcessed: accountsProcessed.size,
                    failedCount,
                    aborted: true,
                };
            }

            if (batchFailures.length > 0) {
                this.logger.warn(
                    PolloMessage.msg(`Batch had ${batchFailures.length} failures but continuing`)
                        .setContext('GlobalRouterService')
                        .setMetadata({
                            batchFailures,
                            batchFailureRate: batchFailureRate.toFixed(1),
                        }),
                );
            }
        }

        const failedDetail = failedCount > 0 ? ` (${failedCount} failures)` : '';
        const message = dryRun
            ? `Dry run completed. Would process ${processedCount} entries across ${accountsProcessed.size} accounts`
            : `Successfully processed ${processedCount} entries across ${accountsProcessed.size} accounts${failedDetail}`;

        const result = {
            message,
            processedCount,
            accountsProcessed: accountsProcessed.size,
            failedCount,
            aborted: false,
        };

        this.logger.log(
            PolloMessage.msg('Completed entry global sync')
                .setContext('GlobalRouterService')
                .setMetadata(result),
        );

        return result;
    }
}
