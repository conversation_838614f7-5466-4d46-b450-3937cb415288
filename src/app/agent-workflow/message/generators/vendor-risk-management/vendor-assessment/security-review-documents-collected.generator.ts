import { Injectable } from '@nestjs/common';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import { AgentWorkflowStepMessageActionType } from 'app/agent-workflow/enums/agent-workflow-step-message-action-type.enum';
import { AgentWorkflowStepMessageAction } from 'app/agent-workflow/enums/agent-workflow-step-message-action.enum';
import { AgentWorkflowStepMessageCaller } from 'app/agent-workflow/enums/agent-workflow-step-message-caller.enum';
import { VendorAssessmentMessageGeneratorBase } from 'app/agent-workflow/message/generators/vendor-assessment-message-generator-base';
import { AgentWorkflowStepMessageDataType } from 'app/agent-workflow/types';
import { VendorsSecurityRiskCoreService } from 'app/users/vendors/services/vendors-security-risk-core.service';
import { Account } from 'auth/entities/account.entity';

/**
 * Message generator for the SECURITY_REVIEW_DOCUMENTS_COLLECTED step in vendor assessment process.
 * Generates appropriate message data for when vendor documents have been collected and the user
 * can start the assessment.
 */
@Injectable()
export class SecurityReviewDocumentsCollectedMessageGenerator extends VendorAssessmentMessageGeneratorBase {
    constructor(vendorsSecurityRiskCoreService: VendorsSecurityRiskCoreService) {
        super(vendorsSecurityRiskCoreService);
    }
    /**
     * Generates message data for the SECURITY_REVIEW_DOCUMENTS_COLLECTED step.
     * @param account - The account context for the workflow
     * @param agentWorkflow - The agent workflow containing context and state
     * @returns Promise<Array of message data objects for the step>
     */
    async generate(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepMessageDataType[]> {
        this.logGenerate(account, agentWorkflow);
        const securityReview = await this.getSecurityReview(agentWorkflow);
        const vendorName = securityReview.vendor?.name || 'Unknown Vendor';

        return [
            {
                caller: AgentWorkflowStepMessageCaller.AGENT,
                title: [],
                body: [
                    {
                        text: `I can help you conduct a detailed assessment on ${vendorName} and provide a report of their security posture.`,
                    },
                ],
                actions: [],
            },
            {
                caller: AgentWorkflowStepMessageCaller.USER,
                title: [],
                body: [],
                actions: [
                    {
                        type: AgentWorkflowStepMessageActionType.BUTTON,
                        text: 'Start assessment',
                        action: AgentWorkflowStepMessageAction.SEND_SECURITY_REVIEW_DOCUMENTS,
                    },
                ],
            },
        ];
    }
}
