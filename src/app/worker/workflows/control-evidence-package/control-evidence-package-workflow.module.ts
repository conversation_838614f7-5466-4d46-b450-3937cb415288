import { Module } from '@nestjs/common';
import { CollectCustomerRequestControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-customer-request-controls-evidence.service';
import { CollectSelectControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-select-controls-evidence.service';
import { AuditHubAuditPackagesOrchestrationModule } from 'app/audit-hub/services/audithub-audit-packages/audit-hub-audit-packages-orchestration.module';
import { CompanyOrchestrationModule } from 'app/companies/orchestration/company-orchestration.module';
import { GrcEvidenceDownloadOrchestrationModule } from 'app/grc/orchestration/grc-evidence-download-orchestration.module';
import { PersonnelOrchestrationModule } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.module';
import { ControlEvidencePackageActivityService } from 'app/worker/workflows/control-evidence-package/activities/control-evidence-package-activity.service';
import { ControlEvidenceJsonUploaderService } from 'app/worker/workflows/control-evidence-package/services/control-evidence-json-uploader.service';
import { AuditorsCoreModule } from 'auditors/auditors-core.module';
import { AuditorsModule } from 'auditors/auditors.module';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';
import { ReleaseWindowsFilenameAbbreviation } from 'feature-flags/implementations/release-windows-filename-abbreviation-feature-flag';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [
        AuditHubAuditPackagesOrchestrationModule,
        TypeOrmExtensionsModule.forGlobalCustomRepository([
            AuditorFrameworkRepository,
            AuditorRepository,
        ]),
        AuditorsCoreModule,
        CompanyOrchestrationModule,
        GrcEvidenceDownloadOrchestrationModule,
        PersonnelOrchestrationModule,
        AuditorsModule,
    ],
    providers: [
        CollectSelectControlsEvidenceService,
        CollectCustomerRequestControlsEvidenceService,
        ControlEvidencePackageActivityService,
        ControlEvidenceJsonUploaderService,
        ReleaseWindowsFilenameAbbreviation,
    ],
    exports: [ControlEvidencePackageActivityService],
})
export class ControlEvidencePackageWorkflowModule {}
