import { Flatfile } from '@flatfile/api';
import { JobStatus } from '@flatfile/api/api';
import { Injectable } from '@nestjs/common';
import { FlatFileSdk } from 'app/flatfile-bulk-import/services/flat-file.sdk';
import { PolloLogger } from 'pollo-logger/pollo.logger';

// Extended JobConfig type with initialMessage property
interface ExtendedJobConfig extends Flatfile.JobConfig {
    initialMessage?: string;
}

// Job progress constants
const JOB_PROGRESS = {
    STARTED: 10,
    PROCESSING: 25,
    FINALIZING: 90,
} as const;

@Injectable()
export class FlatfileJobManagerService {
    private readonly flatFileSdk = new FlatFileSdk();

    constructor(private logger?: PolloLogger<any>) {}

    /**
     * Execute a Flatfile job
     *
     * @param config Job configuration including type, operation, source, and optional initial message
     * @param processor Function containing the business logic to execute
     * @param outcome Optional outcome configuration for job completion
     * @returns Promise resolving to the processor result
     * @throws Re-throws any error from the processor after failing the job
     */
    async executeJob<T>(
        config: ExtendedJobConfig,
        processor: (jobId: string) => Promise<T>,
        outcome?: Flatfile.JobOutcome,
    ): Promise<T> {
        const { initialMessage = 'Processing...', ...jobConfig } = config;

        // Create job
        const { data: job } = await this.flatFileSdk.createJob({
            type: jobConfig.type,
            operation: jobConfig.operation,
            source: jobConfig.source,
            trigger: 'immediate',
            mode: 'toolbarBlocking',
        });

        this.logger?.log(`Flatfile job created: ${job.id} (${jobConfig.operation})`);

        try {
            // Acknowledge job start
            await this.flatFileSdk.ackJob(job.id, {
                info: initialMessage,
                progress: JOB_PROGRESS.STARTED,
            });

            // Update progress: Processing started
            await this.updateJobProgress(job.id, JOB_PROGRESS.PROCESSING, 'Processing...');

            // Execute business logic
            const result = await processor(job.id);

            // Update progress: Processing completed
            await this.updateJobProgress(job.id, JOB_PROGRESS.FINALIZING, 'Finalizing...');

            // Only complete job if it hasn't been manually completed or failed
            const isTerminal = await this.isJobInTerminalState(job.id);
            if (!isTerminal) {
                await this.flatFileSdk.completeJob(job.id, outcome ? { outcome } : undefined);
                this.logger?.log(`Flatfile job completed: ${job.id}`);
            } else {
                const currentStatus = await this.getJobStatus(job.id);
                this.logger?.log(`Flatfile job already ${currentStatus}: ${job.id}`);
            }

            return result;
        } catch (error) {
            // Only fail job if it hasn't been manually completed or failed
            const isTerminal = await this.isJobInTerminalState(job.id);
            if (!isTerminal) {
                const failureOutcome: Flatfile.JobOutcome = {
                    acknowledge: true,
                    message: `Job failed: There was an error processing your request. Please try again.`,
                    heading: 'Processing Error',
                };

                await this.flatFileSdk.failJob(job.id, { outcome: failureOutcome });
                this.logger?.error(`Flatfile job failed: ${job.id} - ${(error as Error).message}`);
            } else {
                const currentStatus = await this.getJobStatus(job.id);
                this.logger?.error(
                    `Flatfile job error (already ${currentStatus}): ${job.id} - ${(error as Error).message}`,
                );
            }

            throw error;
        }
    }

    /**
     * Private helper method for updating job progress
     *
     * @param jobId The job ID to update
     * @param progress The progress percentage (0-100)
     * @param message The progress message to display
     */
    private async updateJobProgress(
        jobId: string,
        progress: number,
        message: string,
    ): Promise<void> {
        await this.flatFileSdk.updateJob(jobId, {
            info: message,
            progress,
        });

        this.logger?.log(`Job progress updated: ${jobId} - ${progress}% - ${message}`);
    }

    /**
     * Get the current status of a Flatfile job
     *
     * @param jobId The job ID to check
     * @returns The job status from Flatfile API
     */
    private async getJobStatus(jobId: string): Promise<JobStatus | undefined> {
        try {
            const { data: job } = await this.flatFileSdk.getJob(jobId);
            return job.status;
        } catch (error) {
            this.logger?.error(
                `Failed to get job status for ${jobId}: ${(error as Error).message}`,
            );
            return undefined;
        }
    }

    /**
     * Check if a job is in a terminal state (completed or failed)
     *
     * @param jobId The job ID to check
     * @returns True if job is completed or failed, false if still running
     */
    private async isJobInTerminalState(jobId: string): Promise<boolean> {
        const status = await this.getJobStatus(jobId);
        return (
            status === JobStatus.Complete ||
            status === JobStatus.Failed ||
            status === JobStatus.Canceled
        );
    }

    /**
     * Helper method for failing a job with a custom outcome
     *
     * @param jobId The job ID to fail
     * @param outcome The failure outcome to display
     */
    async failJob(jobId: string, outcome: Flatfile.JobOutcome): Promise<void> {
        await this.flatFileSdk.failJob(jobId, { outcome });
        this.logger?.error(`Job failed manually: ${jobId} - ${outcome.message}`);
    }

    /**
     * Helper method for completing a job
     *
     * @param jobId The job ID to complete
     * @param outcome Optional completion outcome
     */
    async completeJob(jobId: string, outcome?: Flatfile.JobOutcome): Promise<void> {
        await this.flatFileSdk.completeJob(jobId, outcome ? { outcome } : undefined);
        this.logger?.log(`Job completed manually: ${jobId}`);
    }
}
