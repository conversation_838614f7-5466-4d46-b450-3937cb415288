import { File_, RecordWithLinks, Sheet, Space, Workbook } from '@flatfile/api/api';
import { FlatfileEvent } from '@flatfile/listener';
import { Injectable } from '@nestjs/common';
import { DocumentService } from 'app/documents/services/document.service';
import {
    allowedFileTypes,
    DEFAULT_FILE_SIZE,
    DEFAULT_ZIP_FILE_SIZE,
    FILES_ATTACHMENT_SHEET_SLUG,
} from 'app/flatfile-bulk-import/constants/flatfile.constants';
import { FlatfileApprovedFileEvent } from 'app/flatfile-bulk-import/observables/events/flatfile-approved-file.event';
import { FlatfileRejectedFileEvent } from 'app/flatfile-bulk-import/observables/events/flatfile-rejected-file.event';
import { FlatFileSdk } from 'app/flatfile-bulk-import/services/flat-file.sdk';
import { scannable } from 'app/scanner-uploader/helpers/scannable-file-validator.helper';
import { ScannerUploaderCoreService } from 'app/scanner-uploader/services/scanner-uploader-core.service';
import { FlatfileJobManagerService } from 'app/worker/workflows/bulk-import/activities/flatfile-job-manager.service';
import {
    FileMetadata,
    ScanningResult,
} from 'app/worker/workflows/bulk-import/types/flatfile-processing-workflow.types';
import { Account } from 'auth/entities/account.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { DocumentReferenceType } from 'commons/enums/document-reference-type.enum';
import { ScannerUploaderReferenceType } from 'commons/enums/scanner-uploader/scanner-uploader-reference-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import type { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { UploaderPayloadType } from 'dependencies/uploader/types/uploader-payload.type';
import { isEmpty } from 'lodash';
import Stream from 'node:stream';
import { buffer } from 'node:stream/consumers';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

// Bring everything from src/app/flatfile-bulk-import/listeners/risk-files-listener.ts and transform it into a service

// temporal solution until Stephen merge his cache PR
const TEN_MINUTES = 1000 * 60 * 10; // 10 minutes ttl

type LocalCache<T> = {
    lastUpdated: number;
    value: T;
};

function getCache<T>(cache: Map<string, LocalCache<T>>, key: string, ttl: number): T | undefined {
    const cachedValue = cache.get(key);
    if (cachedValue && cachedValue.lastUpdated > Date.now() - ttl) {
        return cachedValue.value;
    }
    return undefined;
}

function setCache<T>(cache: Map<string, LocalCache<T>>, key: string, value: T) {
    cache.set(key, { lastUpdated: Date.now(), value });
}

@Injectable()
export class FlatfileRiskFilesService {
    private UNCONFIGURED_BUCKET_NAME = 'CHANGEME'; // Default value from default.yml
    private zipFileExtension = 'zip';
    private readonly FILE_VALIDATION_HEADING = 'File Validation';
    private logger = PolloLogger.logger(FlatfileRiskFilesService.name);
    private flatFileSdk = new FlatFileSdk();
    // temporal solution until Stephen merge his cache PR
    private spaceCache = new Map<string, LocalCache<Space>>();
    private workbooksCache = new Map<string, LocalCache<Workbook[]>>();

    constructor(
        private readonly scannerUploaderCoreService: ScannerUploaderCoreService,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly documentService: DocumentService,
        private readonly flatfileJobManagerService: FlatfileJobManagerService,
    ) {}

    public cleanUpLocalCache() {
        this.spaceCache.clear();
        this.workbooksCache.clear();
    }

    public async onFileUploaded(event: FlatfileEvent | FlatfileEvent[]): Promise<ScanningResult> {
        // Skip if scanning bucket is not configured
        if (!this.isScanningBucketConfigured()) {
            return { success: false, error: 'scanning disabled' };
        }

        const events = Array.isArray(event) ? event : [event];
        const isBulk = Array.isArray(event);

        // Get context from first event (all should have same spaceId/account)
        const { account, spaceId } = await this.getFlatfileContext(events[0]);
        const principalWorkbook = await this.getSpacePrincipalWorkbook(spaceId);

        const fileCount = events.length;
        const initialMessage = isBulk
            ? `Analyzing ${fileCount} files...`
            : 'Analyzing file size...';

        try {
            return await this.flatfileJobManagerService.executeJob(
                {
                    type: 'workbook',
                    operation: 'validation:file-intake',
                    source: principalWorkbook.id,
                    initialMessage,
                },
                async jobId => {
                    const validationErrors: Array<{ filename: string; error: string }> = [];
                    const validFiles: Array<{ file: File_; fileEvent: FlatfileEvent }> = [];
                    const filesSheet = this.getFilesSheet(principalWorkbook);

                    // Process each file for validation
                    for (const fileEvent of events) {
                        // eslint-disable-next-line no-await-in-loop
                        const { file } = await this.getFlatfileContext(fileEvent);

                        // File size validation
                        if (
                            file.ext === this.zipFileExtension
                                ? this.isZipAboveFileSizeLimit(file)
                                : this.isFileAboveFileSizeLimit(file)
                        ) {
                            const fileSizeLimit = this.getFileSizeLimit(file);
                            const currentFileSize = this.formatFileSize(file.size);
                            const maxFileSize = this.formatFileSize(fileSizeLimit);
                            const error = `File size exceeds the maximum allowed size of ${maxFileSize}`;

                            validationErrors.push({
                                filename: `${file.name} (${currentFileSize})`,
                                error,
                            });
                            // eslint-disable-next-line no-await-in-loop
                            await this.flatFileSdk.deleteFile(file.id);
                            continue;
                        }

                        // File type validation
                        const fileExtension = `.${file.ext}`;
                        if (!allowedFileTypes.includes(fileExtension)) {
                            const allowedTypesString = allowedFileTypes.join(', ');
                            const error = `File has an unsupported file type. Allowed file types: ${allowedTypesString}`;

                            validationErrors.push({ filename: file.name, error });
                            // eslint-disable-next-line no-await-in-loop
                            await this.flatFileSdk.deleteFile(file.id);
                            // eslint-disable-next-line no-await-in-loop
                            await this.removeFileFromSheet(filesSheet.id, file.name);
                            continue;
                        }

                        // File is valid
                        validFiles.push({ file, fileEvent });
                    }

                    // If there are validation errors, fail the job with consolidated message
                    if (validationErrors.length > 0) {
                        const errorMessage = isBulk
                            ? this.buildBulkValidationErrorMessage(validationErrors)
                            : this.buildSingleFileValidationErrorMessage(validationErrors[0]);

                        // Use jobManager to fail the job with proper outcome
                        await this.flatfileJobManagerService.failJob(jobId, {
                            acknowledge: true,
                            message: errorMessage,
                            heading: this.FILE_VALIDATION_HEADING,
                        });

                        return { success: false, error: 'validation failed' };
                    }

                    // Process valid files
                    if (validFiles.length === 0) {
                        return { success: true, message: 'no files to process' };
                    }

                    // For single file, use original logic
                    if (!isBulk) {
                        const { file } = validFiles[0];
                        const uploadedDummyFile: UploadedFileType =
                            await this.createUploadableFile(file);

                        if (scannable(uploadedDummyFile)) {
                            const scanResult = await this.uploadFileForScanning(
                                {
                                    id: file.id,
                                    name: file.name,
                                    mimetype: file.mimetype,
                                    size: file.size,
                                },
                                account,
                                spaceId,
                                filesSheet.id,
                                jobId,
                                false,
                            );

                            if (scanResult.success) {
                                return { success: true, message: 'file uploaded' };
                            } else {
                                // Error already handled in uploadFileForScanning for single files
                                return { success: false, error: scanResult.error };
                            }
                        } else {
                            const result = await this.uploadRegularFile(
                                {
                                    id: file.id,
                                    name: file.name,
                                    mimetype: file.mimetype,
                                    size: file.size,
                                },
                                account,
                                filesSheet.id,
                            );
                            await this.flatfileJobManagerService.completeJob(jobId);
                            return result;
                        }
                    }

                    // For bulk files, process all valid files
                    const scannableFiles: File_[] = [];
                    const regularFiles: File_[] = [];

                    for (const { file } of validFiles) {
                        const uploadedDummyFile: UploadedFileType =
                            // eslint-disable-next-line no-await-in-loop
                            await this.createUploadableFile(file);
                        if (scannable(uploadedDummyFile)) {
                            scannableFiles.push(file);
                        } else {
                            regularFiles.push(file);
                        }
                    }

                    // Track scanning failures
                    const scanningFailures: string[] = [];

                    // Upload scannable files for scanning
                    for (const file of scannableFiles) {
                        // eslint-disable-next-line no-await-in-loop
                        const scanResult = await this.uploadFileForScanning(
                            {
                                id: file.id,
                                name: file.name,
                                mimetype: file.mimetype,
                                size: file.size,
                            },
                            account,
                            spaceId,
                            filesSheet.id,
                            jobId,
                            true, // isBulkOperation = true
                        );

                        if (!scanResult.success) {
                            scanningFailures.push(file.name);
                        }
                    }

                    // Upload regular files directly
                    for (const file of regularFiles) {
                        // eslint-disable-next-line no-await-in-loop
                        await this.uploadRegularFile(
                            {
                                id: file.id,
                                name: file.name,
                                mimetype: file.mimetype,
                                size: file.size,
                            },
                            account,
                            filesSheet.id,
                        );
                    }

                    if (scanningFailures.length > 0) {
                        const failureMessage =
                            this.buildBulkScanningFailureMessage(scanningFailures);

                        // Use jobManager to fail the job with proper outcome
                        await this.flatfileJobManagerService.failJob(jobId, {
                            acknowledge: true,
                            message: failureMessage,
                            heading: this.FILE_VALIDATION_HEADING,
                        });

                        return { success: false, error: 'scanning failures detected' };
                    }

                    const successfulFiles = validFiles.length;
                    return {
                        success: true,
                        message: `${successfulFiles} files processed successfully`,
                    };
                },
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Error in onFileUploaded')
                    .setError(error as Error)
                    .setIdentifier({
                        fileCount: events.length,
                        spaceId,
                        accountId: account.id,
                    }),
            );
            return { success: false, error: (error as Error).message };
        }
    }

    public async onFileDeleted(event: FlatfileEvent): Promise<void> {
        const { spaceId, fileId } = event.context;
        const [mainWorkbook] = await this.getSpaceWorkbooks(spaceId);
        const fileSheet = this.getFilesSheet(mainWorkbook);
        const records = await this.getSheetRecordsByQuery(fileSheet.id, `fileId eq '${fileId}'`);

        if (isEmpty(records)) {
            this.logger.warn(
                PolloMessage.msg('File not found in sheet, was already deleted').setIdentifier({
                    fileId,
                }),
            );
            return;
        }

        const fileName = String(records[0].values.filename.value);

        await this.removeFileFromSheet(fileSheet.id, fileName);
    }

    public async onFileApproved(event: FlatfileApprovedFileEvent): Promise<ScanningResult> {
        this.logger.log(
            PolloMessage.msg('Handling FlatfileScanningApprovedEvent')
                .setContext(this.constructor.name)
                .setSubContext('handleEvent')
                .setMetadata({
                    accountId: event.account.id,
                }),
        );
        const NO_ENTITY_REFERENCE = 1;
        const referenceFile = await this.documentService.saveDocument(
            event.uploadedFile as UploaderPayloadType,
            DocumentReferenceType.RISK,
            NO_ENTITY_REFERENCE,
        );

        const { fileMetadata: file } = event;

        const recordData = {
            filename: { value: file.name },
            filetype: { value: file.mimetype },
            filesize: { value: file.size },
            fileId: { value: file.id },
            drataDocumentId: { value: referenceFile.id.toString() },
        };

        this.logger.log(
            PolloMessage.msg('Inserting record into file attachment sheet').setIdentifier({
                recordData,
            }),
        );

        const insertResult = await this.flatFileSdk.insertRecords(event.fileAttachmentSheetId, [
            recordData,
        ]);

        this.logger.log(
            PolloMessage.msg('File attachment record inserted').setIdentifier({
                fileId: event.flatfileFileId,
                drataDocumentId: referenceFile.id,
            }),
        );

        return { success: true, message: 'file approved', output: { recordData, insertResult } };
    }

    public async onFileRejected(
        event: FlatfileRejectedFileEvent | FlatfileRejectedFileEvent[],
    ): Promise<ScanningResult> {
        const events = Array.isArray(event) ? event : [event];
        const isBulk = Array.isArray(event);

        this.logger.log(
            PolloMessage.msg(`Handling ${isBulk ? 'bulk' : 'single'} file rejection`)
                .setContext(this.constructor.name)
                .setSubContext('handleEvent')
                .setMetadata({
                    fileCount: events.length,
                    accountId: events[0].account.id,
                }),
        );

        try {
            // Pre-job setup: Delete all rejected files
            await Promise.all(events.map(e => this.flatFileSdk.deleteFile(e.flatfileFileId)));

            const workbook = await this.getSpacePrincipalWorkbook(events[0].spaceId);
            const [, fileAttachmentSheet] = workbook?.sheets || [];

            const fileCount = events.length;
            const fileWord = fileCount > 1 ? 'files' : 'file';
            const initialMessage = isBulk ? `Deleting ${fileCount} files...` : 'Deleting file...';

            return await this.flatfileJobManagerService.executeJob(
                {
                    type: 'workbook',
                    operation: 'validation:file-scanning-rejected-intake',
                    source: workbook.id,
                    initialMessage,
                },
                async () => {
                    // Validate prerequisites
                    if (!fileAttachmentSheet) {
                        throw new Error('file attachment sheet not found');
                    }

                    // Remove all files from sheet
                    for (const e of events) {
                        try {
                            // eslint-disable-next-line no-await-in-loop
                            await this.removeFileFromSheet(fileAttachmentSheet.id, e.filename);
                        } catch (error) {
                            // Log the error but don't break the flow - the file was already deleted from Flatfile
                            this.logger.warn(
                                PolloMessage.msg(
                                    'Could not remove file from sheet, but continuing with job completion',
                                )
                                    .setError(error as Error)
                                    .setIdentifier({
                                        filename: e.filename,
                                        sheetId: fileAttachmentSheet.id,
                                        spaceId: e.spaceId,
                                    }),
                            );
                        }
                    }

                    return { success: false, error: `${fileCount} ${fileWord} rejected` };
                },
                {
                    acknowledge: true,
                    message: isBulk
                        ? this.buildBulkRejectionMessage(events.map(e => e.filename))
                        : `File "${events[0].filename}" was deleted as it may contain malicious content.`,
                    heading: this.FILE_VALIDATION_HEADING,
                },
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Error in onFileRejected')
                    .setError(error as Error)
                    .setIdentifier({
                        fileCount: events.length,
                        spaceId: events[0].spaceId,
                        accountId: events[0].account.id,
                    }),
            );
            return { success: false, error: (error as Error).message };
        }
    }

    private async uploadFileForScanning(
        file: FileMetadata,
        account: Account,
        spaceId: string,
        sheetId: string,
        jobId: string,
        isBulkOperation = false,
    ): Promise<{ success: boolean; error?: string }> {
        // Convert Flatfile file to UploadedFileType format
        const downloadedFile = await this.flatFileSdk.downloadFile(file.id);
        const uploadedFile: UploadedFileType = {
            buffer: await buffer(downloadedFile),
            originalname: file.name,
            mimetype: file.mimetype,
            size: file.size,
            fieldname: 'file',
            encoding: '7bit',
        };
        try {
            // Upload for antivirus scanning with tenant context
            const scanResult = await this.scannerUploaderCoreService.uploadFileForScanningOrFail(
                account,
                uploadedFile,
                UploadType.RISK,
                ScannerUploaderReferenceType.FLATFILE_RISK,
                {
                    spaceId,
                    fileMetadata: file,
                    fileAttachmentSheetId: sheetId,
                },
                new FlatfileRejectedFileEvent(account, file.id, spaceId, file.name),
                file.id,
            );

            this.logger.log(
                PolloMessage.msg('File sent for antivirus scanning').setIdentifier({
                    fileId: file.id,
                    scanResult,
                }),
            );

            return { success: true };
        } catch (error) {
            const errorMessage = `Error scanning file`;
            this.logger.error(
                PolloMessage.msg(errorMessage).setIdentifier({
                    fileId: file.id,
                    error,
                }),
            );

            if (!isBulkOperation) {
                await this.flatfileJobManagerService.failJob(jobId, {
                    acknowledge: true,
                    message: `${errorMessage} ${file.name}.`,
                    heading: this.FILE_VALIDATION_HEADING,
                });
            }

            // Clean up the failed file
            await this.flatFileSdk.deleteFile(file.id);
            await this.removeFileFromSheet(sheetId, file.name);

            return { success: false, error: errorMessage };
        }
    }

    private async getFlatfileContext(event: FlatfileEvent): Promise<{
        account: Account;
        file: File_;
        spaceId: string;
        spaceMetadata: { scannedFiles?: string[] };
    }> {
        const { spaceId } = event.context;
        const space = await this.getSpace(spaceId);
        const accountId = space.metadata.accountId;
        const account = await this.accountsCoreService.getAccountById(accountId);

        const file = await this.getFile(spaceId, event.context.fileId);
        return {
            account,
            file,
            spaceId,
            spaceMetadata: space.metadata,
        };
    }

    private async createUploadableFile(
        file: FileMetadata,
        data = Stream.Readable.from([]),
    ): Promise<UploadedFileType> {
        return {
            buffer: await buffer(data),
            originalname: file.name,
            mimetype: file.mimetype,
            size: file.size,
            fieldname: 'file',
            encoding: '7bit',
        };
    }

    private getFilesSheet(workbook: Workbook): Sheet {
        const filesSheet = workbook.sheets?.find(
            sheet => sheet.slug === FILES_ATTACHMENT_SHEET_SLUG,
        );

        if (!filesSheet) {
            this.logger.error(
                PolloMessage.msg('Error: "File Attachments" sheet not found in the space.'),
            );
            throw new Error('Error: "File Attachments" sheet not found in the space.');
        }

        return filesSheet;
    }

    private async getSpacePrincipalWorkbook(spaceId: string) {
        const [targetWorkbook] = await this.getSpaceWorkbooks(spaceId);
        if (!targetWorkbook) {
            this.logger.error(
                PolloMessage.msg('Error: Workbook not found in the space.').setIdentifier({
                    spaceId,
                }),
            );
            throw new Error('Error: Workbook not found in the space.');
        }
        return targetWorkbook;
    }

    private isScanningBucketConfigured(): boolean {
        const scanningBucket = config.get('scannerUploader.s3.scanningBucket');
        return !isEmpty(scanningBucket) && scanningBucket !== this.UNCONFIGURED_BUCKET_NAME;
    }

    private async addFileToSheet(sheet: Sheet | undefined, file: File_, documentId?: string) {
        // Prepare the record data to be inserted into the sheet.
        // The structure { value: ... } is used for each field.
        if (!sheet) {
            return;
        }
        const recordData = {
            filename: { value: file.name },
            filetype: { value: file.mimetype },
            filesize: { value: file.size },
            fileId: { value: file.id },
        };

        if (documentId) {
            recordData['drataDocumentId'] = { value: documentId };
        }

        await this.flatFileSdk.insertRecords(sheet.id, [recordData]);
        return recordData;
    }

    private async removeFileFromSheet(sheetId: string, fileName: string) {
        // TODO: apply pagination ENG-71311
        const [record] = await this.getSheetRecordsByQuery(sheetId, `filename eq '${fileName}'`);

        if (!record) {
            return;
        }

        const fileId = record.id;
        await this.flatFileSdk.deleteSheetRecord(sheetId, fileId);
    }

    private isFileAboveFileSizeLimit(file: File_): boolean {
        return file.size > DEFAULT_FILE_SIZE;
    }

    private isZipAboveFileSizeLimit(file: File_): boolean {
        return file.size > DEFAULT_ZIP_FILE_SIZE;
    }

    private formatFileSize(bytes: number): string {
        const mb = bytes / (1000 * 1000);
        return `${mb.toFixed(1)}MB`;
    }

    private getFileSizeLimit(file: File_): number {
        return file.ext === this.zipFileExtension ? DEFAULT_ZIP_FILE_SIZE : DEFAULT_FILE_SIZE;
    }

    private buildBulkRejectionMessage(rejectedFiles: string[]): string {
        const fileCount = rejectedFiles.length;
        const fileWord = fileCount > 1 ? 'files' : 'file';
        const verbWord = fileCount > 1 ? 'were' : 'was';
        const pronounWord = fileCount > 1 ? 'they' : 'it';

        const header = `${fileCount} ${fileWord} ${verbWord} deleted as ${pronounWord} may contain malicious content:\n`;
        const fileList = rejectedFiles.map((file, index) => `${index + 1}. ${file}`).join('\n');
        return `${header} ${fileList}`;
    }

    private buildBulkValidationErrorMessage(
        validationErrors: Array<{ filename: string; error: string }>,
    ): string {
        const errorCount = validationErrors.length;
        const fileWord = errorCount > 1 ? 'files' : 'file';
        const verbWord = errorCount > 1 ? 'were' : 'was';

        // Group errors by error message
        const errorGroups = new Map<string, string[]>();

        for (const { filename, error } of validationErrors) {
            if (!errorGroups.has(error)) {
                errorGroups.set(error, []);
            }
            const filenames = errorGroups.get(error);
            if (filenames) {
                filenames.push(filename);
            }
        }

        const header = `${errorCount} ${fileWord} ${verbWord} rejected due to validation errors:\n\n`;

        const errorSections = Array.from(errorGroups.entries()).map(([errorMessage, filenames]) => {
            const fileList = filenames
                .map((filename, index) => `${index + 1}. ${filename}`)
                .join('\n');
            return `${errorMessage}\n\n${fileList}`;
        });

        return `${header}${errorSections.join('\n\n')}`;
    }

    private buildSingleFileValidationErrorMessage(validationError: {
        filename: string;
        error: string;
    }): string {
        const { filename, error } = validationError;
        return `File "${filename}" was rejected: ${error}`;
    }

    private buildBulkScanningFailureMessage(failedFiles: string[]): string {
        const fileCount = failedFiles.length;
        const fileWord = fileCount > 1 ? 'files' : 'file';
        const verbWord = fileCount > 1 ? 'were' : 'was';

        const header = `${fileCount} ${fileWord} ${verbWord} not uploaded due to scanning failures:\n\n`;
        const fileList = failedFiles.map((file, index) => `${index + 1}. ${file}`).join('\n');

        return `${header}${fileList}`;
    }

    private async uploadRegularFile(
        file: FileMetadata,
        account: Account,
        sheetId: string,
    ): Promise<ScanningResult> {
        try {
            const downloadedFile = await this.flatFileSdk.downloadFile(file.id);
            const uploadableFile = await this.createUploadableFile(file, downloadedFile);
            const NO_ENTITY_REF = 1;
            const document = await this.documentService.uploadFile(
                account,
                uploadableFile,
                UploadType.RISK,
                DocumentReferenceType.RISK,
                NO_ENTITY_REF,
            );

            // insert
            const insertedData = await this.addFileToSheet(
                { id: sheetId } as Sheet,
                { ...file } as File_,
                document.id.toString(),
            );

            this.logger.log(
                PolloMessage.msg('File attachment record updated').setIdentifier({
                    fileId: file.id,
                    drataDocumentId: document.id,
                }),
            );
            return {
                success: true,
                message: 'file uploaded',
                output: { insertedData },
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Error downloading file').setIdentifier({
                    fileId: file.id,
                    error,
                }),
            );
            await this.removeFileFromSheet(sheetId, file.name);
            return { success: false, error: 'error downloading file' };
        }
    }

    private async getSpace(spaceId: string) {
        const cachedSpace = getCache(this.spaceCache, spaceId, TEN_MINUTES);
        if (cachedSpace) {
            return cachedSpace;
        }
        const space = await this.flatFileSdk.getSpace(spaceId);
        setCache(this.spaceCache, spaceId, space.data);
        return space.data;
    }

    private async getSpaceWorkbooks(spaceId: string) {
        const cacheWorkbooksKey = `${spaceId}-workbooks`;
        const cachedWorkbooks = getCache(this.workbooksCache, cacheWorkbooksKey, TEN_MINUTES);
        if (cachedWorkbooks) {
            return cachedWorkbooks;
        }
        const workbooks = await this.flatFileSdk.getSpaceWorkbooks(spaceId);
        setCache(this.workbooksCache, cacheWorkbooksKey, workbooks);
        return workbooks;
    }

    private async getFile(spaceId: string, fileId: string) {
        const { data: flatfileFile } = await this.flatFileSdk.getFile(fileId);
        return flatfileFile;
    }

    private async getFiles(spaceId: string) {
        const allFiles: File_[] = [];

        await forEachTokenPage(
            async (_nextPageToken, page = 1) => {
                // Get records from FlatFile
                const response = await this.flatFileSdk.getFiles(spaceId, page);

                // Transform the response to match expected format
                return {
                    data: {
                        page: response || [],
                        // If we have more records than current page size, set nextPageToken to next page number
                        nextPageToken: response.length > 0 ? String(page + 1) : null,
                    },
                };
            },
            async (files: File_[]) => {
                if (isEmpty(files)) {
                    return;
                }
                allFiles.push(...files);
            },
        );
        return allFiles;
    }

    private async getSheetRecordsByQuery(sheetId: string, query: string) {
        const sheetRecords: RecordWithLinks[] = [];
        await forEachTokenPage(
            async (_nextPageToken, page = 1) => {
                // Get records from FlatFile
                const response = await this.flatFileSdk.getSheetRecordsByQuery(
                    sheetId,
                    query,
                    page,
                );

                // Transform the response to match expected format
                return {
                    data: {
                        page: response.data.records || [],
                        // If we have more records than current page size, set nextPageToken to next page number
                        nextPageToken: response.data.records?.length > 0 ? String(page + 1) : null,
                    },
                };
            },
            async (records: RecordWithLinks[]) => {
                if (isEmpty(records)) {
                    return;
                }
                sheetRecords.push(...records);
            },
        );
        return sheetRecords;
    }
}
