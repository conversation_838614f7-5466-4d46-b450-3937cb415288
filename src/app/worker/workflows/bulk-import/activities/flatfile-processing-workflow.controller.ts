import { FlatfileEvent } from '@flatfile/listener';
import { Injectable } from '@nestjs/common';
import { BulkImportCommerceService } from 'app/bulk-import/bulk-import-commerce/bulk-import-commerce.service';
import { FlatfileApprovedFileEvent } from 'app/flatfile-bulk-import/observables/events/flatfile-approved-file.event';
import { FlatfileRejectedFileEvent } from 'app/flatfile-bulk-import/observables/events/flatfile-rejected-file.event';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { FlatfileRiskFilesService } from 'app/worker/workflows/bulk-import/activities/flatfile-risk-files.service';
import { FlatfileRiskValidationsService } from 'app/worker/workflows/bulk-import/activities/flatfile-risk-validations.service';
import { FlatfileListFieldsProcessor } from 'app/worker/workflows/bulk-import/processors/flatfile-list-fields.processor';
import {
    EntityValidationInput,
    EntityValidationResult,
    EventSubmitInput,
    FlatfileTemporalEventInput,
    RiskValidationResult,
    ScanningResult,
} from 'app/worker/workflows/bulk-import/types/flatfile-processing-workflow.types';
import {
    FlatfileScanningResultEvents,
    WorkbookMapJobCompletedEvent,
} from 'app/worker/workflows/bulk-import/types/flatfile-space-monitor.types';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import { has } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class FlatfileProcessingWorkflowController extends WorkflowActivityController {
    constructor(
        private readonly flatfileRiskFilesService: FlatfileRiskFilesService,
        private readonly flatfileRiskValidationsService: FlatfileRiskValidationsService,
        private readonly flatfileListFieldsProcessor: FlatfileListFieldsProcessor,
        private readonly bulkImportCommerceService: BulkImportCommerceService,
    ) {
        super();
    }

    @ActivityNs()
    async uploadFileForScanning(
        spaceId: string,
        fileId: string,
        accountId: string,
    ): Promise<ScanningResult> {
        try {
            this.logger.log(
                PolloMessage.msg('Uploading file for scanning').setIdentifier({
                    spaceId,
                    fileId,
                    accountId,
                }),
            );

            return await this.flatfileRiskFilesService.onFileUploaded({
                context: {
                    spaceId,
                    fileId,
                },
            } as FlatfileEvent);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to upload file for scanning')
                    .setError(error)
                    .setIdentifier({
                        spaceId,
                        fileId,
                        accountId,
                    }),
            );

            throw error;
        }
    }

    @ActivityNs()
    async uploadFilesForBulkScanning(
        fileEvents: Array<{ spaceId: string; fileId: string; accountId: string }>,
    ): Promise<ScanningResult> {
        try {
            this.logger.log(
                PolloMessage.msg('Uploading files for bulk scanning').setIdentifier({
                    fileCount: fileEvents.length,
                    fileIds: fileEvents.map(e => e.fileId),
                }),
            );

            // Convert to FlatfileEvent format and use existing method
            const flatfileEvents = fileEvents.map(e => ({
                context: { spaceId: e.spaceId, fileId: e.fileId },
            })) as FlatfileEvent[];

            return await this.flatfileRiskFilesService.onFileUploaded(flatfileEvents);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to upload files for bulk scanning')
                    .setError(error)
                    .setIdentifier({
                        fileCount: fileEvents.length,
                    }),
            );

            throw error;
        }
    }

    @ActivityNs()
    async deleteFile(spaceId: string, fileId: string, accountId: string): Promise<void> {
        try {
            this.logger.log(
                PolloMessage.msg('Will delete file from Flatfile').setIdentifier({
                    spaceId,
                    fileId,
                    accountId,
                }),
            );

            await this.flatfileRiskFilesService.onFileDeleted({
                context: {
                    spaceId,
                    fileId,
                },
            } as FlatfileEvent);

            this.logger.log(
                PolloMessage.msg('File deleted successfully').setIdentifier({
                    spaceId,
                    fileId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to delete file').setError(error).setIdentifier({
                    spaceId,
                    fileId,
                    accountId,
                }),
            );
        }
    }

    @ActivityNs()
    async validateRisks(event: FlatfileTemporalEventInput): Promise<RiskValidationResult> {
        try {
            return await this.flatfileRiskValidationsService.onValidation(event);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Risk validation failed').setError(error).setIdentifier(event),
            );

            return {
                success: false,
                validRisks: [],
                invalidRisks: [],
                errors: [error.message],
            };
        }
    }

    @ActivityNs()
    async validateRecords(event: EntityValidationInput): Promise<EntityValidationResult> {
        const { entityType, accountId } = event;
        try {
            if (!entityType) {
                this.logger.error(
                    PolloMessage.msg('Entity type is required for record validation').setIdentifier(
                        event,
                    ),
                );
                return {
                    entityType,
                    success: false,
                    validEntities: [],
                    invalidEntities: [],
                    errors: ['Entity type is required for record validation'],
                };
            }

            const eventAdapter = {
                entityType,
                accountId,
                context: {
                    ...event.context,
                    sheetId: event.sheetId,
                    spaceId: event.spaceId,
                    accountId: event.accountId,
                },
            };

            await this.bulkImportCommerceService.validate(eventAdapter);

            return {
                entityType,
                success: true,
                validEntities: [],
                invalidEntities: [],
                errors: [],
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Bulk import validation records failed.')
                    .setError(error)
                    .setIdentifier(event),
            );

            return {
                entityType,
                success: false,
                validEntities: [],
                invalidEntities: [],
                errors: [error.message],
            };
        }
    }

    @ActivityNs()
    async saveRecords(event: EventSubmitInput): Promise<EntityValidationResult> {
        const { entityType, accountId, sheetId, spaceId, userId } = event;
        try {
            if (!entityType) {
                this.logger.error(
                    PolloMessage.msg('Entity type is required for record validation').setIdentifier(
                        event,
                    ),
                );
                return {
                    entityType,
                    success: false,
                    validEntities: [],
                    invalidEntities: [],
                    errors: ['Entity type is required for record validation'],
                };
            }

            await this.bulkImportCommerceService.save(
                entityType,
                spaceId,
                sheetId,
                accountId,
                userId,
            );

            return {
                entityType,
                success: true,
                validEntities: [],
                invalidEntities: [],
                errors: [],
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Risk validation failed').setError(error).setIdentifier(event),
            );

            return {
                entityType,
                success: false,
                validEntities: [],
                invalidEntities: [],
                errors: [error.message],
            };
        }
    }
    @ActivityNs()
    async handleWorkbookMappingJobCompletion(event: WorkbookMapJobCompletedEvent): Promise<void> {
        try {
            this.logger.log(
                PolloMessage.msg('Handling mapping job completion').setIdentifier({
                    jobId: event.jobId,
                }),
            );

            await this.flatfileListFieldsProcessor.processListFields(event);
            await this.flatfileListFieldsProcessor.transformCategories(event);

            this.logger.log(
                PolloMessage.msg('Mapping job completion handled successfully').setIdentifier({
                    jobId: event.jobId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to handle mapping job completion')
                    .setError(error)
                    .setIdentifier({
                        jobId: event.jobId,
                    }),
            );
            throw error;
        }
    }

    @ActivityNs()
    async handleFileScanningResult(event: FlatfileScanningResultEvents): Promise<ScanningResult> {
        try {
            this.logger.log(
                PolloMessage.msg('Handling file scanning result').setIdentifier({
                    fileId: event.flatfileFileId,
                }),
            );

            if (has(event, 'uploadedFile')) {
                return await this.flatfileRiskFilesService.onFileApproved(
                    event as FlatfileApprovedFileEvent,
                );
            } else {
                return await this.flatfileRiskFilesService.onFileRejected(
                    event as FlatfileRejectedFileEvent,
                );
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to handle file scanning result')
                    .setError(error)
                    .setIdentifier({
                        fileId: event.flatfileFileId,
                    }),
            );
            throw error;
        }
    }

    @ActivityNs()
    async handleBulkFileScanningResults(
        events: FlatfileScanningResultEvents[],
    ): Promise<ScanningResult> {
        try {
            this.logger.log(
                PolloMessage.msg('Handling bulk file scanning results').setIdentifier({
                    eventCount: events.length,
                    fileIds: events.map(e => e.flatfileFileId),
                }),
            );

            // Separate approved and rejected files
            const approvedEvents: FlatfileApprovedFileEvent[] = [];
            const rejectedEvents: FlatfileRejectedFileEvent[] = [];

            for (const event of events) {
                if (has(event, 'uploadedFile')) {
                    approvedEvents.push(event as FlatfileApprovedFileEvent);
                } else {
                    rejectedEvents.push(event as FlatfileRejectedFileEvent);
                }
            }

            // Process approved files individually (existing logic)
            for (const approvedEvent of approvedEvents) {
                // eslint-disable-next-line no-await-in-loop
                await this.flatfileRiskFilesService.onFileApproved(approvedEvent);
            }

            // Process rejected files using existing method (handles both single and bulk)
            if (rejectedEvents.length > 0) {
                return await this.flatfileRiskFilesService.onFileRejected(rejectedEvents);
            }

            return { success: true, message: 'bulk scanning completed' };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to handle bulk file scanning results')
                    .setError(error)
                    .setIdentifier({
                        eventCount: events.length,
                    }),
            );
            throw error;
        }
    }

    @ActivityNs()
    async cleanUpLocalCache(): Promise<void> {
        this.flatfileRiskFilesService.cleanUpLocalCache();
    }
}
