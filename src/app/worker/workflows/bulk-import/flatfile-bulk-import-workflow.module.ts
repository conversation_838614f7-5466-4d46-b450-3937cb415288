import { Module } from '@nestjs/common';
import { BulkImportCommerceModule } from 'app/bulk-import/bulk-import-commerce/bulk-import-commerce.module';
import { CustomFieldsCoreModule } from 'app/custom-fields/custom-fields-core.module';
import { DocumentsModule } from 'app/documents/documents.module';
import { CategoryCoreModule } from 'app/risk-management/category-core.module';
import { RiskCoreModule } from 'app/risk-management/risk-core.module';
import { RiskOrchestrationModule } from 'app/risk-management/risk-orchestration.module';
import { ScannerUploaderCoreModule } from 'app/scanner-uploader/scanner-uploader-core.module';
import { UsersCoreModule } from 'app/users/users-core.module';
import { VendorsCoreModule } from 'app/users/vendors-core.module';
import { FlatfileJobManagerService } from 'app/worker/workflows/bulk-import/activities/flatfile-job-manager.service';
import { FlatfileProcessingWorkflowController } from 'app/worker/workflows/bulk-import/activities/flatfile-processing-workflow.controller';
import { FlatfileRiskFilesService } from 'app/worker/workflows/bulk-import/activities/flatfile-risk-files.service';
import { FlatfileRiskValidationsService } from 'app/worker/workflows/bulk-import/activities/flatfile-risk-validations.service';
import { FlatfileListFieldsProcessor } from 'app/worker/workflows/bulk-import/processors/flatfile-list-fields.processor';
import { AccountsCoreModule } from 'auth/accounts-core-module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

/**
 * Workflow module for Flatfile bulk import Temporal activities.
 * This module is imported by the main worker module to expose activities for Temporal workflows.
 *
 * Activities provided:
 * - FlatfileProcessingActivityService: Handles file processing, validation, and scanning
 *
 * This module imports all necessary dependencies to ensure the activity service can be instantiated.
 */
@ModuleType(ModuleTypes.CONTROL_PLANE)
@Module({
    imports: [
        DocumentsModule,
        ScannerUploaderCoreModule,
        AccountsCoreModule,
        UsersCoreModule,
        VendorsCoreModule,
        RiskCoreModule,
        CategoryCoreModule,
        CustomFieldsCoreModule,
        BulkImportCommerceModule,
        RiskOrchestrationModule,
    ],
    providers: [
        FlatfileProcessingWorkflowController,
        FlatfileJobManagerService,
        FlatfileRiskFilesService,
        FlatfileRiskValidationsService,
        FlatfileListFieldsProcessor,
    ],
    exports: [FlatfileProcessingWorkflowController],
})
export class FlatfileBulkImportWorkflowModule {}
