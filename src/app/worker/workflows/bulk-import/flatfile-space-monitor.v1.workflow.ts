import {
    condition,
    defineSignal,
    log,
    setHandler,
    SignalDefinition,
    sleep,
    workflowInfo,
} from '@temporalio/workflow';
import { BulkImportEntityEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import { proxyActivitiesForService } from 'app/worker/helpers/worker.helper';
import { FlatfileProcessingWorkflowController } from 'app/worker/workflows/bulk-import/activities/flatfile-processing-workflow.controller';
import {
    EntityValidationInput,
    FlatfileEventInput,
    FlatfileTemporalEventInput,
} from 'app/worker/workflows/bulk-import/types/flatfile-processing-workflow.types';
import {
    FileCreatedEvent,
    FlatfileScanningResultEvents,
    SheetSubmitEvent,
    WorkbookMapJobCompletedEvent,
} from 'app/worker/workflows/bulk-import/types/flatfile-space-monitor.types';
import { toAccountIdType } from 'commons/types/account-id.type';

export type SignalDefinitionType = SignalDefinition<
    [FileCreatedEvent | FlatfileEventInput | WorkbookMapJobCompletedEvent | SheetSubmitEvent],
    string
>;

// Define signals for different Flatfile event types
export const fileCreatedSignal = defineSignal<[FileCreatedEvent]>('fileCreated');
export const sheetValidatedSignal = defineSignal<[FlatfileEventInput]>('sheetValidated');
export const sheetSubmittedSignal = defineSignal<[SheetSubmitEvent]>('sheetSubmitted');
export const spaceClosedSignal = defineSignal('spaceClosed');
export const workbookMapJobCompletedSignal =
    defineSignal<[WorkbookMapJobCompletedEvent]>('jobCompleted');
export const fileScanningResultSignal =
    defineSignal<[FlatfileScanningResultEvents]>('fileScanningResult');

const {
    FlatfileProcessingWorkflowController_uploadFileForScanning,
    FlatfileProcessingWorkflowController_uploadFilesForBulkScanning,
    //FlatfileProcessingWorkflowController_deleteFile,
    FlatfileProcessingWorkflowController_validateRisks,
    FlatfileProcessingWorkflowController_validateRecords,
    FlatfileProcessingWorkflowController_handleWorkbookMappingJobCompletion,
    FlatfileProcessingWorkflowController_handleFileScanningResult,
    FlatfileProcessingWorkflowController_handleBulkFileScanningResults,
    FlatfileProcessingWorkflowController_cleanUpLocalCache,
} = proxyActivitiesForService<typeof FlatfileProcessingWorkflowController>({
    startToCloseTimeout: '20 minutes',
    retry: {
        maximumAttempts: 3,
    },
    heartbeatTimeout: '3 minutes',
});

/**
 * Long-running workflow that monitors a Flatfile space for events.
 * This workflow survives server restarts and continues processing events.
 *
 * Key features:
 * - Receives signals for file:created and sheet:validated events
 * - Processes events individually as they come in (no batching)
 * - Implements debouncing to prevent duplicate processing within 1 second
 * - Prevents duplicate processing using Set-based deduplication
 * - Processes events directly (no child workflows)
 * - Runs until space is explicitly closed
 */
export async function flatfileSpaceMonitorWorkflow(
    spaceId: string,
    accountId: string,
): Promise<void> {
    let isSpaceActive = true;
    const processedFiles = new Set<string>();
    const processedSpaceEvents = new Set<string>();
    const processedWorkbookMapJobEvents = new Set<string>();

    // Internal event queues for individual processing
    const pendingFileEvents: FileCreatedEvent[] = [];
    const pendingSheetEvents: FlatfileEventInput[] = [];
    const pendingWorkbookMapJobEvents: WorkbookMapJobCompletedEvent[] = [];
    const pendingScanningResultEvents: FlatfileScanningResultEvents[] = [];

    let hasNewEvents = false;

    const workflowLogger = {
        info: (message: string, context?: any) => {
            log.info(`[SpaceMonitor-${spaceId}] ${message}`, context || '');
        },
        error: (message: string, error?: any) => {
            log.error(`[SpaceMonitor-${spaceId}] ${message}`, error || '');
        },
    };

    const entityType = workflowInfo().memo?.entityType as BulkImportEntityEnum;
    const workspaceId = workflowInfo().memo?.workspaceId;
    workflowLogger.info('Space monitor workflow started', {
        spaceId,
        accountId,
        entityType,
        workspaceId,
    });

    // Handle file creation events - Add to queue for individual processing
    setHandler(fileCreatedSignal, async (event: FileCreatedEvent) => {
        const fileKey = `${event.spaceId}-${event.fileId}`;

        workflowLogger.info('Received file created signal', {
            fileId: event.fileId,
            fileName: event.fileName,
            fileKey,
            entityType,
            workspaceId,
        });

        if (!processedFiles.has(fileKey)) {
            // Add to internal queue for immediate individual processing
            pendingFileEvents.push(event);
            hasNewEvents = true;
            workflowLogger.info('Added file event to processing queue', { fileKey });
        } else {
            workflowLogger.info('File already processed, skipping', { fileKey });
        }
    });

    // Handle sheet validation events - Add to queue for individual processing
    setHandler(sheetValidatedSignal, async (event: FlatfileEventInput) => {
        const spaceEventKey = `${event.context.spaceId}-${event.eventId}`;

        workflowLogger.info('Received sheet validated signal', {
            sheetId: event.sheetId,
            sheetName: event.context.sheetSlug,
            spaceEventKey,
            entityType,
            workspaceId,
        });

        if (!processedSpaceEvents.has(spaceEventKey)) {
            // Add to internal queue for immediate individual processing
            processedSpaceEvents.add(spaceEventKey);
            pendingSheetEvents.push(event);
            hasNewEvents = true;
            workflowLogger.info('Added sheet event to processing queue', { spaceEventKey });
        } else {
            workflowLogger.info('Sheet already processed, skipping', { spaceEventKey });
        }
    });

    // Handle space closure
    setHandler(spaceClosedSignal, () => {
        workflowLogger.info('Received space closed signal, stopping monitor');
        isSpaceActive = false;
    });

    // Handle job completion (for ref list parsing)
    setHandler(workbookMapJobCompletedSignal, async (event: WorkbookMapJobCompletedEvent) => {
        const spaceEventKey = `${event.spaceId}-${event.eventId}`;

        workflowLogger.info('Received job completed signal', {
            jobId: event.jobId,
            spaceEventKey,
        });

        if (!processedWorkbookMapJobEvents.has(spaceEventKey)) {
            processedWorkbookMapJobEvents.add(spaceEventKey);
            pendingWorkbookMapJobEvents.push(event);
            hasNewEvents = true;
            workflowLogger.info('Added workbook map job event to processing queue', {
                spaceEventKey,
            });
        } else {
            workflowLogger.info('Job already processed, skipping', { spaceEventKey });
        }
    });

    // ADD: File processing function (replaces child workflow)
    setHandler(fileScanningResultSignal, (event: FlatfileScanningResultEvents) => {
        workflowLogger.info('Received file scanning result signal');
        hasNewEvents = true;
        const scanningResultEventKey = `results-${event.spaceId}-${event.flatfileFileId}`;
        workflowLogger.info('Added scanning result event to processing queue', {
            scanningResultEventKey,
        });
        pendingScanningResultEvents.push(event);
    });

    // File processing function for individual event processing
    async function processFileEvent(event: FileCreatedEvent, logger: any): Promise<void> {
        logger.info('Processing file event', { fileId: event.fileId });
        await FlatfileProcessingWorkflowController_uploadFileForScanning(
            event.spaceId,
            event.fileId,
            event.accountId,
        );
    }

    // Sheet processing function for individual event processing
    async function processSheetEvent(event: FlatfileEventInput, logger: any): Promise<void> {
        logger.info('Processing sheet event', { sheetId: event.sheetId });

        const memoEntityType = workflowInfo().memo?.entityType as BulkImportEntityEnum;
        event.context.workspaceId = workspaceId;
        const flatfileTemporalEvent: FlatfileTemporalEventInput = {
            ...event,
            workflowId: workflowInfo().workflowId,
            accountId: toAccountIdType(workflowInfo().memo?.accountId as string),
            entityType: memoEntityType,
        };

        if (memoEntityType === BulkImportEntityEnum.RISK) {
            await FlatfileProcessingWorkflowController_validateRisks(flatfileTemporalEvent);
        } else {
            const validateRecordsEvent: EntityValidationInput = {
                ...flatfileTemporalEvent,
                entityType: workflowInfo().memo?.entityType as BulkImportEntityEnum,
                accountId: toAccountIdType(workflowInfo().memo?.accountId as string),
                spaceId: event.context?.spaceId,
                sheetName: event.context?.sheetSlug,
            };
            await FlatfileProcessingWorkflowController_validateRecords(validateRecordsEvent);
        }
    }

    async function processWorkbookMapJobEvent(
        event: WorkbookMapJobCompletedEvent,
        logger: any,
    ): Promise<void> {
        logger.info('Processing job event', { jobId: event.jobId });
        await FlatfileProcessingWorkflowController_handleWorkbookMappingJobCompletion(event);
    }

    async function processScanningResultEvent(
        event: FlatfileScanningResultEvents,
        logger: any,
    ): Promise<void> {
        logger.info('Processing scanning result event', { fileId: event.flatfileFileId });
        await FlatfileProcessingWorkflowController_handleFileScanningResult(event);
    }

    async function processBulkScanningResultEvents(
        events: FlatfileScanningResultEvents[],
        logger: any,
    ): Promise<void> {
        logger.info('Processing bulk scanning result events', {
            eventCount: events.length,
            fileIds: events.map(e => e.flatfileFileId),
        });
        await FlatfileProcessingWorkflowController_handleBulkFileScanningResults(events);
    }

    // New function to handle scanning results with batching window
    async function processScanningResultsWithBatching(
        pendingEvents: FlatfileScanningResultEvents[],
        logger: any,
        BATCHING_WINDOW_MS: number,
    ): Promise<void> {
        // Wait a bit to allow more events to accumulate (batching window)

        logger.info(`Waiting ${BATCHING_WINDOW_MS}ms for additional scanning results`, {
            currentEventCount: pendingEvents.length,
        });

        await sleep(BATCHING_WINDOW_MS);

        // After waiting, check how many events we have
        const eventCount = pendingEvents.length;
        logger.info('Processing scanning results after batching window', {
            finalEventCount: eventCount,
        });

        if (eventCount > 1) {
            // Multiple events - use bulk processing
            logger.info('Using bulk processing for multiple scanning results');
            await processBulkScanningResultEvents([...pendingEvents], logger);
        } else if (eventCount === 1) {
            // Single event - use individual processing
            logger.info('Using individual processing for single scanning result');
            await processScanningResultEvent(pendingEvents[0], logger);
        }

        // Clear the processed events
        pendingEvents.length = 0;
    }

    // New function to handle file uploads with batching window
    async function processFileEventsWithBatching(
        pendingEvents: FileCreatedEvent[],
        logger: any,
        BATCHING_WINDOW_MS: number,
        recentlyProcessedFiles: Map<string, number>,
    ): Promise<void> {
        const currentTime = Date.now();
        const initialCount = pendingEvents.length;
        await sleep(BATCHING_WINDOW_MS / 2);

        if (pendingEvents.length === initialCount) {
            logger.info('No new events, processing early');
        } else {
            await sleep(BATCHING_WINDOW_MS / 2);
        }
        const DEBOUNCE_TIMEOUT = 500;
        const eventsToProcess: FileCreatedEvent[] = [];
        const seenInBatch = new Set<string>();

        while (pendingEvents.length > 0) {
            const event = pendingEvents.shift();
            if (!event) continue;

            const eventKey = `${event.spaceId}-${event.fileId}`;
            const lastProcessedTime = recentlyProcessedFiles.get(eventKey) || 0;

            if (currentTime - lastProcessedTime >= DEBOUNCE_TIMEOUT) {
                if (!seenInBatch.has(eventKey)) {
                    eventsToProcess.push(event);
                    seenInBatch.add(eventKey);
                    logger.info('Added file event to processing batch', { eventKey });
                } else {
                    logger.info('File event already in current batch, skipping duplicate', {
                        eventKey,
                    });
                }
            } else {
                logger.info('File event debounced, skipping', {
                    eventKey,
                    timeSinceLastProcess: currentTime - lastProcessedTime,
                    debounceTimeout: DEBOUNCE_TIMEOUT,
                });
            }
        }

        const eventCount = eventsToProcess.length;
        logger.info('Processing file uploads after batching window', {
            finalEventCount: eventCount,
            originalEventCount: pendingEvents.length + eventsToProcess.length,
        });

        if (eventCount > 1) {
            // Multiple events - use bulk processing
            logger.info('Using bulk processing for multiple file uploads');
            const fileEvents = eventsToProcess.map(event => ({
                spaceId: event.spaceId,
                fileId: event.fileId,
                accountId: event.accountId,
            }));

            await FlatfileProcessingWorkflowController_uploadFilesForBulkScanning(fileEvents);

            // Mark all files as processed
            for (const event of eventsToProcess) {
                recentlyProcessedFiles.set(`${event.spaceId}-${event.fileId}`, currentTime);
            }
        } else if (eventCount === 1) {
            // Single event - use individual processing
            logger.info('Using individual processing for single file upload');
            const event = eventsToProcess[0];
            await processFileEvent(event, logger);
            recentlyProcessedFiles.set(`${event.spaceId}-${event.fileId}`, currentTime);
        }

        pendingEvents.length = 0;
    }

    // Generic function to process events with debouncing
    async function processEventsWithDebouncing<T>(
        eventQueue: T[],
        getEventKey: (event: T) => string,
        processEvent: (event: T, logger: any) => Promise<void>,
        eventType: string,
        currentTime: number,
        logger: any,
        DEBOUNCE_TIMEOUT_IN_MS: number,
        recentlyProcessedEvents: Map<string, number>,
    ): Promise<void> {
        while (eventQueue.length > 0) {
            const event = eventQueue.shift();
            if (!event) continue; // Safety check, though this shouldn't happen

            const eventKey = getEventKey(event);
            const lastProcessedTime = recentlyProcessedEvents.get(eventKey) || 0;

            // Check if enough time has passed since last processing (debounce)
            if (currentTime - lastProcessedTime >= DEBOUNCE_TIMEOUT_IN_MS) {
                logger.info(`Processing ${eventType} event individually`, {
                    eventKey,
                    event,
                });

                try {
                    // eslint-disable-next-line no-await-in-loop
                    await processEvent(event, logger);
                    recentlyProcessedEvents.set(eventKey, currentTime);

                    logger.info(`Successfully processed ${eventType} event`, { eventKey });
                } catch (error) {
                    logger.error(`Failed to process ${eventType} event`, { eventKey, error });
                    // Re-add to queue for retry (optional)
                    // eventQueue.unshift(event);
                }
            } else {
                logger.info(`${eventType} event debounced, skipping`, {
                    eventKey,
                    timeSinceLastProcess: currentTime - lastProcessedTime,
                    debounceTimeout: DEBOUNCE_TIMEOUT_IN_MS,
                });
            }
        }
    }

    // Main processing loop - Process events individually with debouncing
    const DEBOUNCE_TIMEOUT = 500; // half a second debounce
    const BATCHING_WINDOW_MS = 3000; // 3 seconds batching window
    const recentlyProcessed = new Map<string, number>(); // key -> timestamp

    while (isSpaceActive) {
        // Wait for new events or space closure
        // eslint-disable-next-line no-await-in-loop
        await condition(() => {
            return hasNewEvents || !isSpaceActive;
        });

        if (!isSpaceActive) break;

        if (hasNewEvents) {
            const currentTime = Date.now();

            // Process file events with batching logic
            if (pendingFileEvents.length > 0) {
                // eslint-disable-next-line no-await-in-loop
                await processFileEventsWithBatching(
                    pendingFileEvents,
                    workflowLogger,
                    BATCHING_WINDOW_MS,
                    recentlyProcessed,
                );
            }

            // Process sheet events individually
            // eslint-disable-next-line no-await-in-loop
            await processEventsWithDebouncing(
                pendingSheetEvents,
                (event: FlatfileEventInput) =>
                    `${event.context.spaceId}-${event.sheetId}-${event.eventId}`,
                async (event: FlatfileEventInput, logger: any) => {
                    await processSheetEvent(event, logger);
                },
                'sheet',
                currentTime,
                workflowLogger,
                DEBOUNCE_TIMEOUT,
                recentlyProcessed,
            );

            // eslint-disable-next-line no-await-in-loop
            await processEventsWithDebouncing(
                pendingWorkbookMapJobEvents,
                (event: WorkbookMapJobCompletedEvent) => `${event.spaceId}-${event.jobId}`,
                async (event: WorkbookMapJobCompletedEvent, logger: any) => {
                    await processWorkbookMapJobEvent(event, logger);
                },
                'job',
                currentTime,
                workflowLogger,
                DEBOUNCE_TIMEOUT,
                recentlyProcessed,
            );

            // Process scanning results with batching logic
            if (pendingScanningResultEvents.length > 0) {
                // eslint-disable-next-line no-await-in-loop
                await processScanningResultsWithBatching(
                    pendingScanningResultEvents,
                    workflowLogger,
                    BATCHING_WINDOW_MS,
                );
            }

            hasNewEvents = false;

            // Clean up old entries from debounce map to prevent memory leaks
            const cutoffTime = currentTime - DEBOUNCE_TIMEOUT * 10; // Keep entries for 10x debounce time
            for (const [key, timestamp] of recentlyProcessed.entries()) {
                if (timestamp < cutoffTime) {
                    recentlyProcessed.delete(key);
                }
            }
        }
    }

    await FlatfileProcessingWorkflowController_cleanUpLocalCache();

    workflowLogger.info('Space monitor workflow completed', {
        processedFilesCount: processedFiles.size,
        processedSpaceEventsCount: processedSpaceEvents.size,
    });
}
