import { FrameworkTag } from '@drata/enums';
import { ProductFramework } from 'app/companies/products/entities/product-framework.entity';
import { CustomCategory } from 'app/custom-frameworks/entities/custom-category.entity';
import { AuditorFrameworkType } from 'app/frameworks/entities/auditor-framework-type.entity';
import { FrameworkIsReadyIndexView } from 'app/frameworks/entities/framework-is-ready-index-view.entity';
import { ProfileRequirement } from 'app/frameworks/entities/profile-requirement.entity';
import { ProfileSelection } from 'app/frameworks/entities/profile-selection.entity';
import { RequirementIndex } from 'app/frameworks/entities/requirement-index.entity';
import { User } from 'app/users/entities/user.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { Type } from 'class-transformer';
import {
    IsBoolean,
    IsDate,
    IsIn,
    IsNotEmpty,
    IsOptional,
    IsUUID,
    MaxLength,
} from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import { getValues } from 'commons/helpers/enum.helper';
import { VirtualRelation } from 'commons/repositories/query.builder';
import config from 'config';
import {
    AfterLoad,
    BeforeInsert,
    BeforeUpdate,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    Relation,
    UpdateDateColumn,
} from 'typeorm';

@Index('enabled', ['deletedAt', 'enabledAt'])
@Entity()
export class Framework extends BaseEntity {
    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        length: config.get('db.varcharLength'),
        type: BaseEntity.varchar(),
        name: 'external_id',
        nullable: true,
    })
    externalId?: string | null;

    @Index('IDX_framework_name')
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ length: config.get('db.varcharLength') })
    name: string;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxLongText'))
    @Column({ type: 'text' })
    description: string;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxLongText'))
    @Column({ type: 'text', name: 'long_description' })
    longDescription: string;

    @Index()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ length: config.get('db.varcharLength') })
    slug: string;

    @Index()
    @IsNotEmpty()
    @IsIn(getValues(FrameworkTag))
    @Column({ type: BaseEntity.smallInt() })
    tag: FrameworkTag;

    @Index('IDX_framework_pill')
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ length: config.get('db.varcharLength') })
    pill: string;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    @Column({
        name: 'enabled_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        nullable: true,
    })
    enabledAt?: Date | null;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    @Column({
        name: 'controls_enabled_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        nullable: true,
    })
    controlsEnabledAt?: Date | null;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'level_label',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
        default: null,
    })
    levelLabel: string | null;

    @IsOptional()
    @IsBoolean()
    @Column({
        name: 'has_level',
        nullable: false,
        width: config.get('db.booleanLength'),
        default: false,
    })
    hasLevel: boolean;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'selected_level',
        type: BaseEntity.varchar(),
        nullable: true,
        default: null,
        length: config.get('db.varcharLength'),
    })
    selectedLevel: string | null;

    @IsOptional()
    @IsBoolean()
    @Column({
        name: 'privacy',
        type: 'boolean',
        nullable: true,
        default: null,
        width: config.get('db.booleanLength'),
    })
    privacy: boolean | null;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxColorHexCodeLength'))
    @Column({
        length: config.get('db.colorHexCodeLength'),
    })
    color: string;

    @IsNotEmpty()
    @MaxLength(config.get('validation.maxColorHexCodeLength'))
    @Column({
        name: 'bg_color',
        length: config.get('db.colorHexCodeLength'),
    })
    bgColor: string;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'active_logo',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLongLength'),
        nullable: true,
    })
    activeLogo?: string | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'inactive_logo',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLongLength'),
        nullable: true,
    })
    inactiveLogo?: string | null;

    @Index()
    @DeleteDateColumn({ type: BaseEntity.timestamp(), name: 'deleted_at' })
    deletedAt: Date | null;

    @CreateDateColumn({ type: BaseEntity.timestamp(), name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: BaseEntity.timestamp(), name: 'updated_at' })
    updatedAt: Date;

    @OneToMany(() => RequirementIndex, requirementIndex => requirementIndex.framework, {
        eager: false,
    })
    requirementIndexes: RequirementIndex[];

    @ManyToMany(() => User, user => user.frameworks, {
        eager: false,
        onDelete: 'NO ACTION',
        onUpdate: 'NO ACTION',
    })
    @JoinTable({
        name: 'frameworks_owners_map',
        joinColumn: {
            name: 'fk_framework_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'fk_user_id',
            referencedColumnName: 'id',
        },
    })
    owners: Relation<User>[];

    @ManyToMany(() => Policy, policy => policy.frameworks, {
        eager: false,
    })
    policies: Policy[];

    @OneToMany(
        () => AuditorFrameworkType,
        auditorFrameworkType => auditorFrameworkType.relatedFramework,
        {
            eager: false,
            nullable: true,
        },
    )
    auditorFrameworkTypes: AuditorFrameworkType[];

    @IsOptional()
    @IsUUID()
    @Column({
        name: 'custom_framework_id',
        type: BaseEntity.varchar(),
        length: config.get('db.uuidLength'),
        nullable: true,
    })
    customFrameworkId?: string | null;

    @OneToMany(() => CustomCategory, customCategory => customCategory.framework, {
        eager: false,
        nullable: true,
    })
    customCategories: CustomCategory[] | null;

    @OneToMany(() => ProductFramework, productFramework => productFramework.framework, {
        eager: false,
        onDelete: 'NO ACTION',
    })
    products: ProductFramework[];

    @OneToMany(() => ProfileRequirement, profileRequirement => profileRequirement.catalog, {
        eager: false,
        onDelete: 'NO ACTION',
        onUpdate: 'NO ACTION',
    })
    profileRequirements: ProfileRequirement[];

    @OneToMany(() => ProfileSelection, profileSelection => profileSelection.framework, {
        eager: false,
        onDelete: 'NO ACTION',
        onUpdate: 'NO ACTION',
    })
    profileSelections: ProfileSelection[];

    @VirtualRelation({
        entity: FrameworkIsReadyIndexView,
        property: 'frameworkId',
    })
    frameworkIsReady: FrameworkIsReadyIndexView;

    @BeforeInsert()
    @BeforeUpdate()
    public beforeChange(): void {
        this.color = this.color.replace('#', '');
        this.bgColor = this.bgColor.replace('#', '');
    }

    @AfterLoad()
    public afterLoad(): void {
        this.color = `#${this.color}`;
        this.bgColor = `#${this.bgColor}`;
        // In case this property is mapped it might not trigger its after load method
        // So we force call it here.
        this.frameworkIsReady?.afterLoad();
    }
}
