import { Injectable } from '@nestjs/common';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { PolloLogger } from 'pollo-logger/pollo.logger';

@Injectable()
export class FlatfileBulkImportFeatureFlagService {
    private readonly logger = PolloLogger.logger(this.constructor.name);
    private readonly bulkImportFlag = {
        category: FeatureFlagCategory.NONE,
        defaultValue: false,
        name: FeatureFlag.RELEASE_BULK_IMPORT,
    };

    private readonly bulkImportRisksFlag = {
        category: FeatureFlagCategory.NONE,
        defaultValue: false,
        name: FeatureFlag.RELEASE_BULK_IMPORT_RISKS,
    };

    private readonly bulkImportControlsFlag = {
        category: FeatureFlagCategory.NONE,
        defaultValue: false,
        name: FeatureFlag.RELEASE_BULK_IMPORT_CONTROLS,
    };

    private readonly bulkImportTrainingsFlag = {
        category: FeatureFlagCategory.NONE,
        defaultValue: false,
        name: FeatureFlag.RELEASE_BULK_IMPORT_TRAININGS,
    };

    constructor(private readonly featureFlagService: FeatureFlagService) {}

    /**
     * Check if bulk import feature is enabled for an account
     * @param {Account} account - The account to check
     * @returns {Promise<boolean>}
     */
    isBulkImportFeatureEnabled(account: Account): Promise<boolean> {
        return this.featureFlagService.evaluateAs(this.bulkImportFlag, account);
    }

    /**
     * Check if bulk import controls feature is enabled for an account
     * @param {Account} account - The account to check
     * @returns {Promise<boolean>}
     */
    isBulkImportControlsFeatureEnabled(user: User, account: Account): Promise<boolean> {
        return this.featureFlagService.evaluate(this.bulkImportControlsFlag, user, account);
    }

    /**
     * Check if bulk import risks feature is enabled for an account
     * @param {Account} account - The account to check
     * @returns {Promise<boolean>}
     */
    isBulkImportRisksFeatureEnabled(user: User, account: Account): Promise<boolean> {
        return this.featureFlagService.evaluate(this.bulkImportRisksFlag, user, account);
    }

    /* Check if bulk import feature is enabled globally
     * @param {Account} account - The account to check
     * @returns {Promise<boolean>}
     */
    isBulkImportFeatureEnabledGlobally(): Promise<boolean> {
        return this.featureFlagService.evaluateAsGlobal(this.bulkImportFlag);
    }

    /**
     * Check if bulk import trainings feature is enabled for an account
     * @param {Account} account - The account to check
     * @returns {Promise<boolean>}
     */
    isBulkImportTrainingsFeatureEnabled(user: User, account: Account): Promise<boolean> {
        return this.featureFlagService.evaluate(this.bulkImportTrainingsFlag, user, account);
    }
}
