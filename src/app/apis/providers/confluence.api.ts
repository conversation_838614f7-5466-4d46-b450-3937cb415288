import { ErrorCode } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { EventBus } from '@nestjs/cqrs';
import { IExternalPoliciesServices } from 'app/apis/interfaces/external-policies.interface';
import { IServiceRunner } from 'app/apis/interfaces/service-runner.interface';
import { OAuth2Api } from 'app/apis/providers/oauth2.api';
import { ConfluenceSdk } from 'app/apis/services/confluence/confluence.sdk';
import { ExternalFileLatestVersionType } from 'app/apis/types/policies/external-file-latest-version.type';
import { ExternalFileWithUrl } from 'app/apis/types/policies/external-file-with-url.type';
import { ExternalFileType } from 'app/apis/types/policies/external-file.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { SynchronConfluenceTemporalEvent } from 'app/users/observables/events/synchron-confluence-temporal.event';
import { ListExternalDocumentsDto } from 'app/users/policies/dtos/list-external-documents.dto';
import { UserPolicyVersion } from 'app/users/policies/entities/user-policy-version.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { asyncForEach } from 'commons/helpers/array.helper';
import { lcIncludes } from 'commons/helpers/string.helper';
import { PaginationType } from 'commons/types/pagination.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { DocraptorHtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/docraptor/docraptor-html-to-pdf-converter';
import { cloneDeep, find, get, isEmpty, isNil, last, map, toString } from 'lodash';
import { OAuth2Adapter } from 'oauth2/adapters/oauth2.adapter';
import { OAuth2AccessTokenType } from 'oauth2/types/oauth2-access-token.type';

export class ConfluenceApi extends OAuth2Api implements IExternalPoliciesServices {
    private confluenceSdk: ConfluenceSdk;

    constructor(
        oauth2: OAuth2Adapter,
        connection: ConnectionEntity,
        httpService: HttpService,
        cacheService: CacheService,
        loggingContext: LoggingContext,
        eventBus: EventBus,
        protected readonly account: Account,
    ) {
        super(oauth2, connection, httpService, cacheService, loggingContext, eventBus);
    }

    get sdk(): ConfluenceSdk {
        if (isNil(this.confluenceSdk)) {
            this.confluenceSdk = new ConfluenceSdk(
                this.httpService,
                this.metadata.key,
                this.account,
            );
        }
        return this.confluenceSdk;
    }

    ping(): Promise<boolean> {
        return this.sdk.ping(this.connection);
    }

    getPingService(): IServiceRunner {
        throw new Error('Method not implemented.');
    }

    getDataSource(): ApiDataSource {
        return ApiDataSource.JIRA;
    }

    getClientType(): ClientType {
        return ClientType.CONFLUENCE;
    }

    async getClientId(): Promise<string> {
        const metadata = this.connection.getMetadata();

        return metadata.cloudId;
    }

    getMetadataForDisplay(): Map<string, any> {
        return new Map();
    }

    async getClient(metadata: ConnectionMetadata): Promise<OAuth2Api> {
        let accessToken: OAuth2AccessTokenType;
        if (isNil(metadata.expiresAt)) {
            accessToken = await this.oauth2.getAccessToken(
                this.getClientType(),
                metadata.key,
                metadata,
            );

            this.setMetadata(accessToken);
        } else {
            const isExpired = this.oauth2.isTokenExpired(
                this.getClientType(),
                String(metadata.expiresAt),
            );

            if (isExpired) {
                accessToken = await this.oauth2.refreshToken(
                    this.getClientType(),
                    metadata.refreshToken,
                    metadata,
                );

                this.setMetadata(accessToken);
            }
        }

        return this;
    }

    private setMetadata(accessToken: OAuth2AccessTokenType): void {
        this.metadata = {
            ...this.metadata,
            key: accessToken.accessToken,
            expiresAt: accessToken.expiresAt,
            scope: accessToken.scope,
            refreshToken: accessToken.refreshToken,
        };

        const cloneMeta = cloneDeep(this.metadata);
        this.connection.setMetadata(cloneMeta);
    }

    async listExternalFiles(
        listExternalDocumentsDto: ListExternalDocumentsDto,
    ): Promise<PaginationType<ExternalFileType>> {
        const clientId = await this.getClientId();
        const { nameSearch } = listExternalDocumentsDto;
        if (!isNil(nameSearch) && !isEmpty(nameSearch)) {
            const searchedFiles = await this.searchByName(listExternalDocumentsDto, clientId);
            return this.mapPaginationResponse(
                listExternalDocumentsDto,
                searchedFiles.externalFiles,
                searchedFiles.nextPageUri,
            );
        }

        const paginateFiles = await this.getPaginatedExternalFiles(
            listExternalDocumentsDto,
            clientId,
        );
        return this.mapPaginationResponse(
            listExternalDocumentsDto,
            paginateFiles.externalFiles,
            paginateFiles.nextPageUri,
        );
    }

    private async getPaginatedExternalFiles(
        listExternalDocumentsDto: ListExternalDocumentsDto,
        clientId: string,
    ): Promise<{ externalFiles: ExternalFileType[]; nextPageUri: string }> {
        const externalFiles: ExternalFileType[] = [];
        let nextPageUri: string;
        let limitNotReached: boolean;

        do {
            // eslint-disable-next-line no-await-in-loop
            const response = await this.sdk.listContents(
                clientId,
                listExternalDocumentsDto.limit,
                !isEmpty(nextPageUri) ? nextPageUri : listExternalDocumentsDto.nextPage,
            );
            nextPageUri = response.nextPageUri ?? null;

            const parentIds = response.data.map(pt => pt.parentId).filter(data => data);

            if (!isNil(parentIds)) {
                // eslint-disable-next-line no-await-in-loop
                const parentPages = await this.sdk.getParentPages(clientId, parentIds);

                response.data.forEach(page => {
                    const parent = find(parentPages.data, pt => pt.id === page.parentId);
                    if (parent) {
                        page.parent = parent;
                    }
                });
            }
            // eslint-disable-next-line no-await-in-loop
            const filteredExternalfiles =
                // eslint-disable-next-line no-await-in-loop
                await this.mapContentsResponseToExternalFileType(response.data, clientId);
            externalFiles.push(...filteredExternalfiles);

            limitNotReached = externalFiles.length < listExternalDocumentsDto.limit;
        } while (limitNotReached && !isEmpty(nextPageUri));
        const nextPageUriEncoded = this.getCustomCursor(nextPageUri, last(externalFiles));

        return {
            externalFiles,
            nextPageUri: nextPageUriEncoded,
        };
    }

    private getCustomCursor(nextPageUri: string, lastFile: ExternalFileType): string | null {
        if (!isEmpty(nextPageUri) && !isEmpty(lastFile)) {
            const cursor = `{"id":"${lastFile.id}","contentOrder":"id","contentOrderValue":${lastFile.id}}`;
            const cursorEncrypted = Buffer.from(cursor).toString('base64');
            return nextPageUri.replace(/(cursor=).*?(&)/g, `$1${cursorEncrypted}$2`);
        }
        return null;
    }

    private async searchByName(
        listExternalDocumentsDto: ListExternalDocumentsDto,
        clientId: string,
    ): Promise<{ externalFiles: ExternalFileType[]; nextPageUri: string }> {
        const { data, nextPageUri } = await this.sdk.searchContents(
            clientId,
            listExternalDocumentsDto,
        );

        const externalFiles = await this.mapSearchResult(clientId, data);

        if (
            !isEmpty(externalFiles) &&
            config.get('confluence.api.searchUriNonPageIssuesTenants').includes(this.account.domain)
        ) {
            externalFiles.forEach(e => {
                this.eventBus.publish(new SynchronConfluenceTemporalEvent(this.account, e.id));
            });
        }

        return { externalFiles, nextPageUri };
    }

    private async mapSearchResult(
        clientId: string,
        searchResults: any,
    ): Promise<ExternalFileType[]> {
        const externalPolicyFiles: ExternalFileType[] = [];

        const spacesIds: string[] = map(searchResults, r =>
            get(r, 'content.space.id', null),
        ).filter(e => e);
        const spaces = await this.sdk.getSpacesByIds(clientId, spacesIds);
        const hasSpaces = !isEmpty(spaces);

        await asyncForEach(searchResults, async ({ content: page }) => {
            externalPolicyFiles.push({
                id: page?.id,
                name: page?.title,
                path: this.getPathName(page),
                originalName: `${page?.title}.pdf`,
                createdAt: page?.history.createdDate,
                version: toString(page?.version.number),
                spaceName: hasSpaces
                    ? get(
                          get(spaces, 'response.results', []).find(
                              s => s.id === get(page, 'space.id', null) && s.type === 'global',
                          ),
                          'name',
                          '',
                      )
                    : '',
                html: get(page, 'body.styled_view.value', null),
            });
        });

        return externalPolicyFiles;
    }

    private getPathName(page: any): string {
        if (isEmpty(page)) {
            return '';
        }
        if (!isEmpty(page.ancestors)) {
            return page.ancestors.slice(-1)[0]?.title ?? '';
        }

        if (!isNil(page.parent)) {
            return get(page, 'parent.title', '');
        }

        if (!isNil(page.space)) {
            return get(page, 'space.name', '');
        }

        return '';
    }

    async getExternalFile(fileId: string): Promise<ExternalFileType> {
        const clientId = await this.getClientId();
        const { response: page } = await this.sdk.getPage(clientId, fileId);

        if (
            !isEmpty(page) &&
            config.get('confluence.api.searchUriNonPageIssuesTenants').includes(this.account.domain)
        ) {
            this.logger.log(
                PolloAdapter.cxt('Confluence temporal not getting on search', this.loggingContext)
                    .setResult({
                        id: page.id,
                        name: page.title,
                    })
                    .setDomain(this.loggingContext.domain),
            );
        }

        const version =
            !isNil(page.version) && typeof page.version === 'number'
                ? page.version
                : get(page, 'version.number', null);

        const html = get(page, 'body.styled_view.value', '');

        return {
            id: page.id,
            name: page.title,
            path: `spaces`,
            originalName: `${page.title}.pdf`,
            createdAt: page.createdAt,
            version,
            html,
        };
    }

    async getUploadFileTypeFrom(externalFile: ExternalFileType): Promise<UploadedFileType> {
        const clientId = await this.getClientId();
        let html: string;

        if (!isEmpty(externalFile.html)) {
            html = externalFile.html;
        } else {
            const { response: page } = await this.sdk.getPage(clientId, externalFile.id);
            html = page.body.styled_view.value;
        }

        const htmlToPdfConverter = new DocraptorHtmlToPdfConverter(this.httpService);

        const buffer = await htmlToPdfConverter.convertToPdfBuffer(html);

        return {
            fieldname: 'file',
            originalname: `${externalFile.name}.pdf`,
            encoding: '7bit',
            mimetype: buffer.mimetype,
            buffer: buffer.data,
            size: buffer.data.length,
        };
    }

    async getDeletedFiles(fileIds: Set<string>): Promise<Set<string>> {
        if (isEmpty(fileIds)) {
            return new Set();
        }

        const existingFileIds = new Set();
        const clientId = await this.getClientId();
        let nextPageUri: string | null = null;

        do {
            // eslint-disable-next-line no-await-in-loop
            const { data, nextPageUri: next } = await this.sdk.deletedFiles(
                clientId,
                fileIds,
                nextPageUri,
            );

            this.logger.log(
                PolloAdapter.cxt(`Page result from get deleted files.`, this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getDeletedFiles.name)
                    .setIdentifier({
                        pageToken: nextPageUri,
                        count: data?.length,
                        data,
                    }),
            );

            nextPageUri = next;
            if (!isEmpty(data)) {
                data.forEach((entry: any) => existingFileIds.add(entry.id));
            }
        } while (!isNil(nextPageUri));

        const result = new Set([...fileIds].filter(id => !existingFileIds.has(id)));

        this.logger.log(
            PolloAdapter.cxt(`Summary of get deleted files method.`, this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getDeletedFiles.name)
                .setIdentifier({
                    activeExternalPolicies: Array.from(fileIds),
                    activeExternalPoliciesCount: Array.from(fileIds).length,
                    incomingExternalPolicies: Array.from(existingFileIds),
                    incomingExternalPoliciesCount: Array.from(existingFileIds).length,
                    deletedPolicies: Array.from(result),
                    deletedPoliciesCount: Array.from(result).length,
                }),
        );

        return result;
    }

    getAcceptedUserPolicyVersions(): Promise<UserPolicyVersion[]> {
        throw new Error('Method not implemented.');
    }

    async getLastFileVersions(fileIds: Set<string>): Promise<ExternalFileLatestVersionType[]> {
        this.logger.log(
            PolloAdapter.cxt('Begin getLastFileVersions method', this.loggingContext)
                .setSubContext(this.getLastFileVersions.name)
                .setContext(this.constructor.name),
        );
        const clientId = await this.getClientId();
        const { data } = await this.sdk.listVersions(clientId, fileIds);
        const lastVersionMaped = await this.mapSearchResult(clientId, data);
        const lastFileVersions: ExternalFileLatestVersionType[] = [];

        lastVersionMaped.forEach(version => {
            lastFileVersions.push({
                id: version.id,
                version: version.version,
                html: version.html,
            });
        });

        this.logger.log(
            PolloAdapter.cxt('Finish getLastFileVersions method', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getLastFileVersions.name),
        );
        return lastFileVersions;
    }

    async getFileByVersion(fileId: string, version: string): Promise<ExternalFileWithUrl> {
        try {
            const clientId = await this.getClientId();
            const { response: page } = await this.sdk.getPageVersion(clientId, fileId, version);

            return {
                id: page.content.id,
                name: page.content.title,
                path: '',
                originalName: `${page.content.title}.pdf`,
                createdAt: page.when,
                version: page.number,
                url: page._links.base,
                html: get(page, 'content.body.styled_view.value'),
            };
        } catch (e) {
            if (e.message === 'Request failed with status code 404') {
                throw new NotFoundException(ErrorCode.VERSION_NOT_FOUND);
            }

            throw e;
        }
    }

    private async mapContentsResponseToExternalFileType(
        pages: any,
        clientId: string,
    ): Promise<ExternalFileType[]> {
        const externalPolicyFiles: ExternalFileType[] = [];
        const spacesIds: string[] = map(pages, r => get(r, 'spaceId', null)).filter(e => e);
        const spaces = await this.sdk.getSpacesByIds(clientId, spacesIds);
        const hasSpaces = !isEmpty(spaces);

        await asyncForEach(pages, async page => {
            const space = get(spaces, 'response.results', []).find(s => s.id === page.spaceId);
            if (space.type !== 'personal') {
                const parent = page.parent ? page.parent.title : null;
                const path = parent ? parent : space.name;
                externalPolicyFiles.push({
                    id: page.id,
                    name: page.title,
                    path,
                    originalName: `${page.title}.pdf`,
                    createdAt: page.createdAt,
                    version: toString(page.version.number),
                    spaceName: hasSpaces ? get(space, 'name', '') : '',
                });
            }
        });
        return externalPolicyFiles;
    }

    private mapPaginationResponse(
        { nameSearch, ...paginationOptions }: ListExternalDocumentsDto,
        externalFiles: ExternalFileType[],
        nextPageUri: string,
    ): PaginationType<ExternalFileType> {
        if (!isNil(nameSearch)) {
            externalFiles = externalFiles.filter(({ name }) => lcIncludes(name, nameSearch));
        }
        return {
            data: externalFiles,
            total: -1,
            limit: paginationOptions.limit,
            page: -1,
            nextPage: nextPageUri,
        };
    }
}
