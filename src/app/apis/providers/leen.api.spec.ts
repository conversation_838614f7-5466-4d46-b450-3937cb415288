import { faker } from '@faker-js/faker';
import { HttpService } from '@nestjs/axios';
import { Leen<PERSON><PERSON> } from 'app/apis/providers/leen.api';
import { Vendor } from 'app/apis/services/leen/leen-vendor.enum';
import { LeenSdk } from 'app/apis/services/leen/leen.sdk';
import { LeenConnectionTestType } from 'app/apis/types/leen/leen-connection-test.type';
import { LeenPaginatedResponse } from 'app/apis/types/leen/leen-paginated-response.type';
import { ServicePageableType } from 'app/apis/types/service/service-pageable.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { IVulnerabilityData } from 'app/vulnerability/interfaces/monitoring/vulnerability-data.interface';
import { VulnerabilitySlaConfig } from 'app/vulnerability/types/monitoring/vulnerability-sla-config.type';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { mock, MockProxy } from 'jest-mock-extended';

class TestLeenApi extends LeenApi<IVulnerabilityData, any> {
    async getClientAlias(): Promise<string> {
        return 'test-client';
    }

    transformApiData(
        item: any,
        slaMatrix: VulnerabilitySlaConfig[],
        vendor: Vendor,
    ): IVulnerabilityData {
        return {
            vulnerabilityFindingData: {
                id: item.id,
                title: item.title,
                severity: item.severity,
                state: item.state,
                firstFound: new Date(item.firstFound),
                lastFound: new Date(item.lastFound),
            },
            vulnerabilityFindingMetadata: {
                slaConfig: slaMatrix[0],
                vendor,
            },
            vulnerabilityResourceData: {
                region: item.region,
                resourceId: item.resourceId,
                resourceType: item.resourceType,
                tags: item.tags || {},
            },
            vulnerabilityEntryData: {
                source: vendor,
                externalId: item.id,
            },
        } as unknown as IVulnerabilityData;
    }

    async listFindings(): Promise<ServicePageableType<IVulnerabilityData>> {
        return {
            data: [],
            token: null,
            size: 0,
        };
    }

    getFindingIdsToUpdate(): string[] {
        return []
    }

    async listFindingsRequest(): Promise<LeenPaginatedResponse<any>> {
        return { } as LeenPaginatedResponse<any>;
    }

    async listFindingsByIdRequest(): Promise<LeenPaginatedResponse<any>> {
        return {} as LeenPaginatedResponse<any>;
    }

    async listFindingsById(): Promise<IVulnerabilityData[]> {
        return [];
    }
}

describe('LeenApi', () => {
    let leenApiAikido: TestLeenApi;
    let leenApiSnyk: TestLeenApi;
    let mockConnection: MockProxy<ConnectionEntity>;
    let mockHttpService: MockProxy<HttpService>;
    let mockCacheService: MockProxy<CacheService>;
    let mockAccount: MockProxy<Account>;
    let mockLeenSdk: MockProxy<LeenSdk>;
    let mockConnectionMetadata: ConnectionMetadata;
    let loggingContext: LoggingContext;

    beforeEach(() => {
        mockConnection = mock<ConnectionEntity>();
        mockHttpService = mock<HttpService>();
        mockCacheService = mock<CacheService>();
        mockAccount = mock<Account>();
        mockLeenSdk = mock<LeenSdk>();

        mockConnectionMetadata = {
            clientKey: faker.random.alphaNumeric(32),
            clientSecret: faker.random.alphaNumeric(64),
            organizationId: faker.datatype.uuid(),
            externalId: faker.datatype.uuid(),
        } as ConnectionMetadata;

        mockConnection.getMetadata.mockReturnValue(mockConnectionMetadata);
        mockConnection.deletedAt = null;

        mockAccount.domain = 'test-company.com';

        loggingContext = {
            domain: 'test-company.com',
            companyName: 'Test Company',
            traceId: faker.datatype.uuid(),
        };

        leenApiAikido = new TestLeenApi(
            mockConnection,
            mockHttpService,
            mockCacheService,
            loggingContext,
            mockAccount,
            ApiDataSource.LEEN_AIKIDO,
            Vendor.AIKIDO,
        );
        leenApiSnyk = new TestLeenApi(
            mockConnection,
            mockHttpService,
            mockCacheService,
            loggingContext,
            mockAccount,
            ApiDataSource.LEEN_SNYK,
            Vendor.SNYK,
        );

        jest.spyOn(leenApiAikido as any, 'getSdk').mockReturnValue(mockLeenSdk);
        jest.spyOn(leenApiSnyk as any, 'getSdk').mockReturnValue(mockLeenSdk);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });


    describe("handleTestConnection", () => {
        const testResultTrue: LeenConnectionTestType = {
            is_valid: true,
            message: null as unknown as string
        }
        describe('Expected cases.', () => {
            describe('Given a vendor SNYK.', () => {
                it("Should return true by default.", () => {
                    const result = leenApiSnyk.handleTestConnection(testResultTrue);
                    expect(result).toBe(true);
                })
            })
            describe('Given a vendor AIKIDO.', () => {
                describe('When Leen test result is true.', () => {
                    it("Should return true.", () => {
                        const result = leenApiAikido.handleTestConnection(testResultTrue);
                        expect(result).toBe(true);
                    })
                })
            })
        })
    
        describe('Unexpected cases.', () => {
            describe("Give a Leen test result.", () => {
                describe("When test result is null.", () => {
                    it("Should throw and error 'Leen test result is mandatory'.", () => {
                        expect(() => {
                            leenApiAikido.handleTestConnection(null as unknown as LeenConnectionTestType);
                        }).toThrow('Leen test result is mandatory')
                    })
                })
                describe("When test result is undefined.", () => {
                    it("Should throw an error 'Leen test result is mandatory'.", () => {
                        expect(() => {
                            leenApiAikido.handleTestConnection(undefined as unknown as LeenConnectionTestType);
                        }).toThrow('Leen test result is mandatory')
                    })
                })
            })
        })
    
        describe('Edge cases', () => {
            describe("Given a Leen test result timeout", () => {
                it("Should throw an error 'Ping failed for timeout'", () => {
                    expect(() => {
                        leenApiAikido.handleTestConnection({
                            is_valid: false,
                            message: "Connection validation timed out"
                        });
                    }).toThrow('Ping failed for timeout')
                })
            })
            describe('Give a Leen test result unexpected.', () => {
                it("Should return default test result.", () => {
                    const result = leenApiAikido.handleTestConnection({
                        is_valid: false,
                        message: "Other message"
                    });
                    expect(result).toBe(false);
                })
            })
        })
    
    })
});
