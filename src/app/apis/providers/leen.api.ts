import { ErrorCode, VulnerabilitySeverity } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { HttpStatus, PreconditionFailedException, ServiceUnavailableException } from '@nestjs/common';
import { SyncReadinessPollingChecker } from 'app/apis/classes/sync-readiness/sync-readiness-polling-checker.class';
import { LeenSeveritySort } from 'app/apis/enums/leen/leen-sort.enum';
import { ISyncReadinessChecker } from 'app/apis/interfaces/sync-readiness-checker.interface';
import { ISyncReadinessPollingApiProvider } from 'app/apis/interfaces/sync-readiness-polling-api-provider.interface';
import { IVulnerabilityFindingsServices } from 'app/apis/interfaces/vulnerability-finding-services.interface';
import { Api } from 'app/apis/providers/api';
import { Vendor } from 'app/apis/services/leen/leen-vendor.enum';
import { isConnectionActive } from 'app/apis/services/leen/leen.helper';
import { LeenSdk } from 'app/apis/services/leen/leen.sdk';
import { LeenConnectionTestType } from 'app/apis/types/leen/leen-connection-test.type';
import { LeenConnectionType } from 'app/apis/types/leen/leen-connection.type';
import { LeenJob } from 'app/apis/types/leen/leen-job.type';
import { LeenListParams } from 'app/apis/types/leen/leen-list-params.type';
import { LeenOrganizationType } from 'app/apis/types/leen/leen-organization.type';
import { LeenPaginatedResponse } from 'app/apis/types/leen/leen-paginated-response.type';
import { ServicePageableType as Paginated } from 'app/apis/types/service/service-pageable.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { VulnerabilityFindingEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding.entity';
import { IVulnerabilityData } from 'app/vulnerability/interfaces/monitoring/vulnerability-data.interface';
import { VulnerabilityInfrastructureInfoType } from 'app/vulnerability/types/monitoring/vulnerability-infrastructure-info.type';
import { VulnerabilitySlaConfig } from 'app/vulnerability/types/monitoring/vulnerability-sla-config.type';
import { retry } from 'app/worker/helpers/retry.helper';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { VulnerabilityServiceModeType } from 'commons/enums/vulnerability-service-mode.enum';
import { ErrorCodeException } from 'commons/exceptions/error-code.exception';
import { takeLastJob, transformSeverities } from 'commons/helpers/leen.helper';
import { calcNextOffset } from 'commons/helpers/pagination.helper';
import config from 'config';
import { first, isEmpty, isNil, some } from 'lodash';
import moment from 'moment';

export abstract class LeenApi<T extends IVulnerabilityData, V>
    extends Api
    implements IVulnerabilityFindingsServices, ISyncReadinessPollingApiProvider
{
    private sdk: LeenSdk;

    organizationId: string;

    private connectionId: string;
    private syncReadinessChecker: ISyncReadinessChecker<LeenApi<T, V>>;
    private readonly leenActiveStatus = ['open'];
    private readonly PING_MAX_RETRIES = 5;
    private readonly PING_INITIAL_DELAY_MS = 3000;
    private readonly PING_BACKOFF_FACTOR = 2;
    constructor(
        connection: ConnectionEntity,
        httpService: HttpService,
        cacheService: CacheService,
        loggingContext: LoggingContext,
        protected readonly account: Account,
        private readonly apiDataSource: ApiDataSource,
        protected readonly vendor: Vendor,
    ) {
        super(connection, httpService, cacheService, loggingContext);
        this.metadata = this.connection.getMetadata();
        this.organizationId = '';
        this.syncReadinessChecker = new SyncReadinessPollingChecker(
            this,
            this.logger,
            this.loggingContext,
        );
    }

    get connectionEntity() {
        return this.connection;
    }

    abstract getClientAlias(): Promise<string>;

    abstract transformApiData(item: V, slaMatrix: VulnerabilitySlaConfig[], vendor: Vendor): T;

    getDataSource(): ApiDataSource {
        return this.apiDataSource;
    }

    getSource(): ApiDataSource {
        return this.getDataSource();
    }

    /******************************** Connection life cycle methods ********************************/

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async hasCoverage(_regions: string[]): Promise<boolean> {
        return some(await this.getAccountOrg(this.account));
    }

    abstract listFindings(
        _regions: string[],
        severity: VulnerabilitySeverity[],
        firstFound: Date,
        slaMatrix: VulnerabilitySlaConfig[],
        nextPageToken: string | number | null,
        maxResults: number,
    ): Promise<Paginated<T>>;

    abstract getFindingIdsToUpdate(findings: VulnerabilityFindingEntity[]): string[];

    abstract listFindingsRequest(params: LeenListParams): Promise<LeenPaginatedResponse<V>>;

    abstract listFindingsByIdRequest(id: string[]): Promise<LeenPaginatedResponse<V>>;

    abstract listFindingsById(
        ids: string[],
        slaMatrix: VulnerabilitySlaConfig[],
        region?: string | null,
    ): Promise<T[]>;

    get syncGracePeriodMinutes(): number {
        return config.get('leen.syncGracePeriodMinutes');
    }

    get sleepTimeMilliseconds(): number {
        return config.get('leen.api.syncSleepTimeMilliseconds');
    }

    get syncWaitTimeLimitInMinutes(): number {
        return config.get('leen.api.syncWaitTimeLimitInMinutes');
    }

    async connectionReady(): Promise<boolean> {
        return this.syncReadinessChecker.connectionReady();
    }

    async initialize(): Promise<void> {
        try {
            await this.setupOrganization(this.account);
            await this.setupConnection(this.vendor);
        } catch (error) {
            if (error?.response?.status === HttpStatus.UNAUTHORIZED) {
                throw new ErrorCodeException(
                    'Unable to authenticate. please ensure your credentials are valid.',
                    HttpStatus.UNAUTHORIZED,
                    ErrorCode.VULNERABILITY_CONNECTION_UNAUTHORIZED,
                );
            }
            throw error;
        }
    }

    /**
     * Note: Ping should be used after initialize is executed
     * @returns
     */
    ping(): Promise<boolean> {
        return retry(
            async () => {
                const test:LeenConnectionTestType = await this.testConnection();
                return this.handleTestConnection(test);
            },
            {
                retries: this.PING_MAX_RETRIES,
                delay: this.PING_INITIAL_DELAY_MS,
                backoffFactor: this.PING_BACKOFF_FACTOR,
                onRetry: (attempt, error) => {
                    this.logger.warn(
                        PolloAdapter.acct(
                            `Trying to ping ${this.vendor} connection fails`,
                            this.account
                        )
                        .setContext(LeenApi.name)
                        .setSubContext(this.ping.name)
                        .setError(error)
                        .setMetadata({
                            attempt
                        })
                    );
                },
            }
        ).catch(this.handlePingException);
    }

    async isReadyToSync(): Promise<boolean> {
        const lastJob = await this.fetchLastJob();
        return lastJob?.status === 'SUCCESS';
    }

    handlePingException(exception): never {
        if (exception instanceof ErrorCodeException) {
            throw exception;
        }

        throw new ErrorCodeException(
            'Unable to ping SDK',
            HttpStatus.INTERNAL_SERVER_ERROR,
            ErrorCode.INTERNAL_SERVER_ERROR,
        );
    }

    protected getSdk(): LeenSdk {
        if (isNil(this.sdk)) {
            this.sdk = new LeenSdk(
                this.httpService,
                this.loggingContext,
                this.metadata.clientKey,
                this.metadata.clientSecret,
            );
        }
        return this.sdk;
    }

    getConnectionError(error): ErrorCode {
        return error.code;
    }

    /**************************** Business logic API provider ********************************/

    /**
     * Get Leen organization for the current account. If the org exists, use it, if not then create one using the account domain.
     *
     * @param account
     * @returns
     */
    async setupOrganization(account: Account) {
        try {
            let organization = await this.getAccountOrg(account);
            if (isNil(organization)) {
                // If no organization exists for this account exists then one should be created
                organization = await this.getSdk().createOrganization(account);
            }

            // Store the org id context in API and SDK classes
            this.organizationId = organization.id;
            this.getSdk().setOrganizationId(organization.id);

            return organization;
        } catch (error) {
            this.logger.error(
                PolloAdapter.cxt(`Unable to initialize Leen SDK`, this.loggingContext).setError(
                    error,
                ),
            );
            throw error;
        }
    }

    /**
     * Get the Leen connection for the specified vendor. If the Leen connection exists and is linked to this Drata connection, use it,
     * if not then create one.
     *
     * @param account
     * @returns
     */
    async setupConnection(vendor: Vendor) {
        if (isNil(this.organizationId)) {
            this.logger.warn(PolloAdapter.cxt(`No organization initialized`, this.loggingContext));
            return;
        }

        if (!isNil(this.connection?.deletedAt)) {
            // connection has been deleted no need to setup
            return;
        }

        try {
            const connection = await this.getConnection(vendor);
            this.connectionId = connection.id;
            this.getSdk().setConnectionId(connection.id);
        } catch (error) {
            this.logger.warn(
                PolloAdapter.cxt(
                    `Unable to establish connection with Leen`,
                    this.loggingContext,
                ).setError(error),
            );

            throw error;
        }
    }

    /**
     * Returns a Leen connection if one is linked to the Drata connection and is still active. Create a new one if there's no one available.
     * Throw exception if a connection for the required vendor exists and is not linked to the Drata connection.
     * @param vendor
     * @returns
     */
    async getConnection(vendor: Vendor): Promise<LeenConnectionType> {
        /*
         * If we have a connection stored locally on metadata
         * we should check if the connections stills active
         */
        if (this.hasConnectionStored()) {
            const conn = await this.getConnectionByStoredId();
            if (!isNil(conn) && isConnectionActive(conn)) {
                return conn;
            } else {
                throw new ErrorCodeException(
                    'Connection Leen resource not available',
                    HttpStatus.PRECONDITION_FAILED,
                    ErrorCode.LEEN_MISSING_LEEN_RESOURCE,
                );
            }
        }

        /*
         * If we don't have an active connection we should check for
         * connections for the same vendor
         */
        const connections = await this.getVendorConnections(vendor);
        const activeConnections = connections.filter(isConnectionActive);

        /*
         * If an active connection for the same vendor exists then we
         * should fail the initialize operation and throw and exception
         * because we're duplicating the connection
         */
        if (!isEmpty(activeConnections)) {
            throw new ErrorCodeException(
                'Duplicated Vendor Connection',
                HttpStatus.BAD_REQUEST,
                ErrorCode.CONFLICT_DUPLICATE_PROVIDER_CONNECTION,
            );
        }

        return this.getSdk().createConnection(this.organizationId, vendor);
    }

    /**
     * Returns a Leen organization. Query if one exists for the Drata connection using the orgID stored previously or the account domain.
     * @param account
     * @returns
     */
    async getAccountOrg(account: Account): Promise<LeenOrganizationType | undefined> {
        const orgId = this.metadata.organizationId || this.organizationId;

        let queryParams;

        if (isEmpty(orgId)) {
            queryParams = {
                identifier: account.domain,
            };
        } else {
            queryParams = {
                id: orgId,
            };
        }

        // Look for an organization for this account
        const { items } = await this.getSdk().getOrganizations(queryParams);
        // We expect just one result
        return first(items);
    }

    async getConnectionByStoredId(): Promise<LeenConnectionType | null | undefined> {
        const connId = this.getConnectionStored();

        if (isNil(connId)) {
            return null;
        }

        const { items } = await this.getSdk().getConnections(this.organizationId, { id: connId });
        // We expect just one result
        return first(items);
    }

    async getVendorConnections(vendor: Vendor): Promise<LeenConnectionType[]> {
        // Look for the vendor connection
        const { items } = await this.getSdk().getConnections(this.organizationId, {
            vendor: vendor,
        });
        return items;
    }

    testConnection(): Promise<LeenConnectionTestType> {
        return this.getSdk().testConnection(this.organizationId, this.connectionId);
    }

    protected async listLeenFindingsByIds(
        ids: string[],
        slaMatrix: VulnerabilitySlaConfig[],
    ): Promise<T[]> {
        const { items } = await this.listFindingsByIdRequest(ids);
        return items.map(item => this.transformApiData(item, slaMatrix, this.vendor));
    }

    protected async listLeenDataCursorPaginated(
        severity: VulnerabilitySeverity[],
        firstFound: Date,
        slaMatrix: VulnerabilitySlaConfig[],
        maxResults: number,
        cursor: string | null,
    ): Promise<Paginated<T>> {
        const params: LeenListParams = {
            limit: maxResults,
            enableCursor: true,
        };

        if (!isNil(firstFound) && isEmpty(cursor)) {
            params.firstSeenSince = moment(firstFound).toISOString();
        }

        if (!isEmpty(severity)) {
            params.severity = transformSeverities(severity);
            params.sort = LeenSeveritySort.SEVERITY_DESC;
        }

        if (!isEmpty(cursor)) {
            params.cursor = cursor;
        }

        const response = await this.listFindingsRequest(params);

        const activeItems = response.items.filter((item: any) =>
            this.leenActiveStatus.includes(item?.state?.toLowerCase()),
        );

        const data = activeItems.map(item => this.transformApiData(item, slaMatrix, this.vendor));

        const nextToken = response.next_cursor;
        return {
            token: nextToken,
            size: response.count,
            data,
        };
    }

    async fetchLastJob(): Promise<LeenJob | undefined> {
        const limit = config.get('leen.api.syncJobsResults');
        let nextOffset: number | null = 0;
        const lastJobs: LeenJob[] = [];

        do {
            // eslint-disable-next-line no-await-in-loop
            const jobsResponse = await this.listJobs({ offset: 0, limit });
            nextOffset = calcNextOffset(nextOffset, limit, jobsResponse.total);
            lastJobs.push(...jobsResponse.items);
        } while (!isNil(nextOffset));

        return takeLastJob(lastJobs);
    }

    async listJobs(
        params: {
            offset: number;
            limit: number;
        } = {
            offset: 0,
            limit: config.get('leen.api.syncJobsResults'),
        },
    ): Promise<LeenPaginatedResponse<LeenJob>> {
        return this.getSdk().listJobsByConnection(this.organizationId, this.connectionId, params);
    }

    /***************************************** Helper methods *******************************************/

    getConnectionStored(): string {
        return this.connection.getMetadata().externalId || this.connectionId;
    }

    hasConnectionStored(): boolean {
        return !isNil(this.getConnectionStored());
    }

    /***************************************** Inherited methods *******************************************/

    getClientId(): Promise<string> {
        return Promise.resolve(this.organizationId);
    }

    getMaxResults(): number {
        return config.get('leen.api.maxResults');
    }

    getMaxResultsUpdate(): number {
        return config.get('leen.api.maxResultsToUpdate');
    }

    getServiceMode(): VulnerabilityServiceModeType {
        return VulnerabilityServiceModeType.FINDINGS;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getNextPageToken(_region: string): string {
        return '';
    }

    getMetadataForDisplay(): Map<string, any> {
        const metadata = new Map();
        metadata['accountId'] = this.connectionId;
        return metadata;
    }

    getInfrastructureInfo(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        key: string,
    ): Promise<VulnerabilityInfrastructureInfoType> {
        return Promise.resolve({});
    }

    async connectionRemoved(metadata: ConnectionMetadata): Promise<void> {
        if (!isNil(metadata.organizationId) && !isNil(metadata.externalId)) {
            await this.getSdk().deleteConnection(metadata.organizationId, metadata.externalId);
        }
    }

    handleTestConnection (test: LeenConnectionTestType):boolean {
        if (isNil(this.vendor)) {
            this.logger.error(
                PolloAdapter.acct(
                    'Vendor is not provided',
                    this.account
                )
                    .setContext(this.handleTestConnection.name)
                    .setMessage(test.message)
            );
            throw new PreconditionFailedException('Vendor is mandatory');
        }
        if (isNil(this.account)) {
            this.logger.error(
                PolloAdapter.acct(
                    'Account is not provided',
                    this.account
                )
                    .setContext(this.handleTestConnection.name)
                    .setMessage(test.message)
            );
            throw new PreconditionFailedException('Account is mandatory');
        }
        if (isNil(test)) {
            this.logger.error(
                PolloAdapter.acct(
                    'Leen test result is not provided',
                    this.account
                )
                    .setContext(this.handleTestConnection.name)
            );
            throw new PreconditionFailedException('Leen test result is mandatory');
        }
        switch (this.vendor) {
            case Vendor.SNYK:
                /**
                 * For SNYK vendor, due it's Oauth operation, the test connection endpoint
                 * will return false at connection time therefore a hardcoded true is needed.
                 */
                return true;
            case Vendor.AIKIDO:
                switch(true){
                    case test.is_valid:
                        this.logger.log(
                        PolloAdapter.acct(
                            `${this.vendor} successful connection`,
                            this.account
                        )
                        .setContext(this.handleTestConnection.name)
                    );
                    return true;
                    case !test.is_valid && test.message.includes("Connection validation timed out"):
                                        this.logger.error(
                        PolloAdapter.acct(
                            `${this.vendor} connection fails for timeout`,
                            this.account
                        )
                            .setContext(this.handleTestConnection.name)
                            .setMessage(test.message)
                    );
                    throw new ServiceUnavailableException('Ping failed for timeout');
                }
                break;
        }
        return test.is_valid;
    }
}
