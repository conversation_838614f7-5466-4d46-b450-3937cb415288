/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { HttpService } from '@nestjs/axios';
import { NotFoundException } from '@nestjs/common';
import { Branch } from 'app/apis/classes/api-data/branch.class';
import { Collaborator } from 'app/apis/classes/api-data/collaborator.class';
import { RepositoryPermission } from 'app/apis/classes/api-data/repository-permission.class';
import { Repository } from 'app/apis/classes/api-data/repository.class';
import { User } from 'app/apis/classes/api-data/user.class';
import { WorkspacePermission } from 'app/apis/classes/api-data/workspace-permission.class';
import { Workspace } from 'app/apis/classes/api-data/workspace.class';
import { AuthorizedSdk } from 'app/apis/services/authorized.sdk';
import { BitbucketSdkConstants } from 'app/apis/services/bitbucket/constants/bitbucket-sdk.constants';
import { RetryOptions } from 'app/apis/services/bitbucket/types/retry-helper.types';
import { CollaboratorType } from 'app/apis/types/collaborator/collaborator.type';
import { BranchType } from 'app/apis/types/version-control/branch.type';
import { RepositoryType } from 'app/apis/types/version-control/repository.type';
import { VersionControlUserType } from 'app/apis/types/version-control/version-control-user.type';
import { AxiosResponse, HttpStatusCode, isAxiosError } from 'axios';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { getRandomizedExponentialWait } from 'commons/helpers/backoff.helper';
import { tokenize } from 'commons/helpers/pagination/pagination.helper';
import { TokenPaginationResponse } from 'commons/helpers/pagination/token.pagination.response';
import { replaceTextWith } from 'commons/helpers/string.helper';
import { getNameParts } from 'commons/helpers/user.helper';
import config from 'config';
import { get, isEmpty, isNil } from 'lodash';
import { firstValueFrom, lastValueFrom, retry, RetryConfig, tap, throwError, timer } from 'rxjs';
import { format } from 'util';

export class BitbucketSdk extends AuthorizedSdk {
    static BITBUCKET_SINGLE_PAGE = 1;

    static BITBUCKET_SINGLE_RESULT = 1;
    /**
     *
     * @param key
     * @param httpService
     * @param loggingContext
     */
    constructor(key: string, httpService: HttpService, loggingContext: LoggingContext) {
        super(key, httpService, loggingContext);
    }

    /**
     *
     * @param workspaceSlug
     * @param page
     * @param perPage
     * @returns
     */
    async listRepositories(
        workspaceSlug: string,
        page: number,
        perPage: number,
    ): Promise<{ data: RepositoryType[]; token: string }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.repositories'),
            workspaceSlug,
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(
            baseUrl,
            this.listRepositories.name,
            this.getDefaultRetryOptions(),
        );

        const data = get(response, 'data.values', []).map((repository: any) => {
            const defaultBranch = !isNil(repository.mainbranch) ? repository.mainbranch.name : null;

            return {
                id: repository.uuid,
                name: repository.name,
                defaultBranch: defaultBranch,
                ownerId: get(repository, 'owner.uuid', null),
                ownerName: get(repository, 'owner.display_name', null),
                workspaceSlug: get(repository, 'workspace.slug', null),
                raw: repository,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    /**
     *
     * @param workspaceSlug
     * @param page
     * @param perPage
     * @returns
     */
    async listRepositoriesBeta(
        workspaceSlug: string,
        perPage: number,
        page: number,
    ): Promise<TokenPaginationResponse<Repository>> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.repositories'),
            workspaceSlug,
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(
            baseUrl,
            this.listRepositoriesBeta.name,
            this.getDefaultRetryOptions(),
        );

        const data = get(response, 'data.values', []).map((repository: any) => {
            const defaultBranch = !isNil(repository.mainbranch) ? repository.mainbranch.name : null;

            return {
                id: repository.uuid,
                name: repository.name,
                defaultBranch: defaultBranch,
                ownerId: get(repository, 'owner.uuid', null),
                ownerName: get(repository, 'owner.display_name', null),
                workspaceSlug: get(repository, 'workspace.slug', null),
                raw: repository,
            };
        });

        const reposFiltered = [];
        for (const repoData of data) {
            if (isNil(repoData.defaultBranch)) {
                continue;
            }

            // eslint-disable-next-line no-await-in-loop
            const branchesResponse = await this.listBranches(
                repoData.workspaceSlug,
                repoData.raw.slug,
                BitbucketSdk.BITBUCKET_SINGLE_PAGE,
                BitbucketSdk.BITBUCKET_SINGLE_RESULT,
            );

            if (!isEmpty(branchesResponse.data)) {
                reposFiltered.push(repoData);
            }
        }

        return tokenize(reposFiltered, this.getNextPageToken(response, data, perPage));
    }

    /**
     * @param workspaceSlug
     * @param repoSlug
     * @param branchName
     *
     * This method will frequently return 404's since customers can move, change repos, branches, etc.
     * 404's will be logged as info level, everything else will be logged as errors
     * We will retry on 5XX errors until we exceed all retry attempts allowed
     */
    async getBranch(workspaceSlug: string, repoSlug: string, branchName: string): Promise<Branch> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.branch'),
            workspaceSlug,
            repoSlug,
            branchName,
        )}`;

        // Equivalent to previous handling that retried all errors except 404's.
        const isRetryableError = (error: any) => {
            return !this.isEntityNotFound(error);
        };

        const response = await this.getResponse(baseUrl, this.getBranch.name, {
            ...this.getDefaultRetryOptions(),
            isRetryableError,
        });

        const data = get(response, 'data', null);

        return {
            name: get(data, 'name', null),
            commitHash: get(data, 'target.hash', null),
        };
    }

    async listBranches(
        workspaceSlug: string,
        repoSlug: string,
        page: number,
        perPage: number,
        q?: string,
    ): Promise<{ data: BranchType[]; token: string }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.branches'),
            workspaceSlug,
            repoSlug,
        )}?pagelen=${perPage}&page=${page}&q=${q ? `name~"${q}"` : ''}`;

        const response = await this.getResponse(baseUrl, this.listBranches.name);

        const data = get(response, 'data.values', []).map((branch: any) => {
            return {
                name: get(branch, 'name', null),
                default_merge_strategy: get(branch, 'default_merge_strategy', null),
                mergeStrategies: get(branch, 'merge_strategies', null),
                type: get(branch, 'type', null),
                commitHash: get(branch, 'target.hash', null),
                raw: branch,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    /**
     *
     * @param perPage
     * @param page
     * @returns
     */
    async listWorkspacesBeta(
        perPage: number,
        page: number,
    ): Promise<TokenPaginationResponse<Workspace>> {
        const baseUrl = `${config.get('bitbucket.api.url')}${config.get(
            'bitbucket.api.endpoints.workspaces',
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listWorkspacesBeta.name);

        const data = get(response, 'data.values', []).map((workspace: any) => {
            return {
                id: workspace.uuid,
                name: workspace.name,
                slug: workspace.slug,
                raw: workspace,
            };
        });

        return tokenize(data, this.getNextPageToken(response, data, perPage));
    }

    /**
     *
     * @param repository
     * @param perPage
     * @param page
     * @returns
     */
    async listCollaborators(repository: Repository, perPage: number, page: number): Promise<any> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.collaborators'),
            repository.workspaceSlug,
            replaceTextWith(repository.name),
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listCollaborators.name);

        const data = get(response, 'data.values', []).map((collaborator: any) => {
            const writePermission =
                collaborator.permission === 'admin' || collaborator.permission === 'write';

            const adminPermission = collaborator.permission === 'admin';

            return {
                id: get(collaborator, 'user.uuid', null),
                name: get(collaborator, 'user.display_name', null),
                repo: get(collaborator, 'repository.name', null),
                writePermission: writePermission,
                admin: adminPermission,
                raw: collaborator,
            };
        });

        return tokenize(data, this.getNextPageToken(response, data, perPage));
    }

    /**
     *
     * @param repository
     * @param page
     * @param perPage
     * @returns
     */
    async listRepositoryCollaboratorsBeta(
        repository: Repository,
        perPage: number,
        page: number,
    ): Promise<TokenPaginationResponse<Collaborator>> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.collaborators'),
            repository.workspaceSlug,
            replaceTextWith(repository.name),
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(
            baseUrl,
            this.listRepositoryCollaboratorsBeta.name,
            this.getDefaultRetryOptions(),
        );

        const data = get(response, 'data.values', []).map((collaborator: any) => {
            const writePermission =
                collaborator.permission === 'admin' || collaborator.permission === 'write';

            const adminPermission = collaborator.permission === 'admin';

            return {
                id: get(collaborator, 'user.uuid', null),
                name: get(collaborator, 'user.display_name', null),
                repo: get(collaborator, 'repository.name', null),
                writePermission: writePermission,
                admin: adminPermission,
                raw: collaborator,
            };
        });

        return tokenize(data, this.getNextPageToken(response, data, perPage));
    }

    /**
     *
     * @param repository
     * @param page
     * @param perPage
     * @returns
     */
    async listRepositoryCollaborators(
        repository: Repository,
        page: number,
        perPage: number,
    ): Promise<{ data: CollaboratorType[]; token: string }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.collaborators'),
            repository.workspaceSlug,
            replaceTextWith(repository.name),
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listRepositoryCollaborators.name);

        const data = get(response, 'data.values', []).map((collaborator: any) => {
            const writePermission =
                collaborator.permission === 'admin' || collaborator.permission === 'write';

            const adminPermission = collaborator.permission === 'admin';

            return {
                id: get(collaborator, 'user.uuid', null),
                name: get(collaborator, 'user.display_name', null),
                repo: get(collaborator, 'repository.name', null),
                writePermission: writePermission,
                admin: adminPermission,
                raw: collaborator,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    /**
     *
     * @param workspaceSlug
     * @param teamSlug
     * @param page
     * @param perPage
     * @returns
     */
    async listWorkspaceMembers(
        workspaceSlug: string,
        teamSlug: string,
        page: number,
        perPage: number,
    ): Promise<{
        data: VersionControlUserType[];
        token: string;
    }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.groups'),
            workspaceSlug,
            teamSlug,
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listWorkspaceMembers.name);

        const data = get(response, 'data', []).map((member: any) => {
            return {
                id: member.uuid,
                name: member.display_name,
                raw: member,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    /**
     *
     * @param workspaceSlug
     * @param page
     * @param perPage
     * @returns
     */
    async listWorkspaceUsers(
        workspaceSlug: string,
        page: number,
        perPage: number,
    ): Promise<{ data: User[]; token: string }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.members'),
            workspaceSlug,
        )}?pagelen=${perPage}&page=${page}&fields=${encodeURIComponent(
            '+values.user.has_2fa_enabled',
        )}`;

        const response = await this.getResponse(baseUrl, this.listWorkspaceUsers.name);

        const data = get(response, 'data.values', []).map((member: any) => {
            const displayName = get(member, 'user.display_name', null);
            const [firstName, lastName] = getNameParts(displayName);
            return {
                id: get(member, 'user.uuid', null),
                name: displayName,
                firstName,
                lastName,
                hasMfa: get(member, 'user.has_2fa_enabled', null) ?? false,
                raw: member,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    async listWorkspaces(
        page: number,
        perPage: number,
    ): Promise<{ data: Workspace[]; token: string }> {
        const baseUrl = `${config.get('bitbucket.api.url')}${config.get(
            'bitbucket.api.endpoints.workspaces',
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listWorkspaces.name);

        const data = get(response, 'data.values', []).map((workspace: any) => {
            return {
                id: workspace.uuid,
                name: workspace.name,
                slug: workspace.slug,
                raw: workspace,
            };
        });

        return {
            data,
            token: this.getNextPageToken(response, data, perPage),
        };
    }

    /**
     *
     * @param page
     * @param perPage
     * @returns
     */
    async listWorkspacePermissions(page: number, perPage: number): Promise<WorkspacePermission[]> {
        const baseUrl = `${config.get('bitbucket.api.url')}${config.get(
            'bitbucket.api.endpoints.workspacePermissions',
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listWorkspacePermissions.name);

        return get(response, 'data.values', []).map((permission: any) => {
            return {
                permissionLevel: permission.permission,
                user: {
                    name: permission.user.display_name,
                },
                workspace: {
                    id: get(permission, 'workspace.uuid', null),
                    name: get(permission, 'workspace.name', null),
                    slug: get(permission, 'workspace.slug', null),
                    raw: permission.workspace,
                },
                raw: permission,
            };
        });
    }

    /**
     *
     * @param page
     * @param perPage
     * @returns
     */
    async listRepositoryPermissions(
        page: number,
        perPage: number,
    ): Promise<RepositoryPermission[]> {
        const baseUrl = `${config.get('bitbucket.api.url')}${config.get(
            'bitbucket.api.endpoints.repositoryPermissions',
        )}?pagelen=${perPage}&page=${page}`;

        const response = await this.getResponse(baseUrl, this.listRepositoryPermissions.name);

        return get(response, 'data.values', []).map((permission: any) => {
            return {
                permissionLevel: permission.permission,
                user: {
                    name: get(permission, 'user.display_name', null),
                },
                repository: {
                    id: get(permission, 'repository.uuid', null),
                    name: get(permission, 'repository.full_name', null),
                },
                raw: permission,
            };
        });
    }

    /**
     *
     * @param repository
     * @param workspaceSlug
     * @param defaultBranch
     * @param nextPage
     * @returns
     */
    listRepositoryProtection(
        repository: string,
        workspaceSlug: string,
        defaultBranch: string | null,
        nextPage: number,
    ): Promise<any> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.branchRestrictions'),
            workspaceSlug,
            repository,
        )}?page=${nextPage}`;

        return this.getResponse(baseUrl, this.listRepositoryProtection.name);
    }

    /**
     *
     * @param response
     * @param page
     * @param maxResults
     * @returns
     */
    protected getNextPageToken(response: any, page: any[], maxResults: number): string {
        let nextPageToken = null;
        const pageData = get(response, 'data', null);

        if (!isNil(pageData) && page.length === maxResults) {
            nextPageToken = ++pageData.page;
        }

        return nextPageToken;
    }

    /**
     *
     * @param baseUrl
     * @returns
     */
    async getResponse(
        baseUrl: string,
        hint: string,
        retryOptions?: RetryOptions,
    ): Promise<AxiosResponse<any, any>> {
        let reqSeq = this.httpService.get(baseUrl, {
            headers: this.getAuthorization(),
        });

        if (retryOptions) {
            const retryObsConfig = this.buildRetryObserverConfig({ baseUrl, hint }, retryOptions);
            reqSeq = reqSeq.pipe(retry(retryObsConfig));
        }

        try {
            const result = await lastValueFrom(reqSeq);
            this.ifDidReceiveError(result, baseUrl, hint);

            return result;
        } catch (error) {
            if (this.isEntityNotFound(error)) {
                this.handleError(null, error, baseUrl, hint, this.logger.warn.bind(this.logger));
                throw new NotFoundException(error);
            } else {
                this.handleError(null, error, baseUrl, hint);
                throw error;
            }
        }
    }

    async deleteBranch(workspaceSlug: string, repoSlug: string, branchName: string) {
        const encodedBranchName = encodeURIComponent(branchName);

        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.branch'),
            workspaceSlug,
            repoSlug,
            encodedBranchName,
        )}`;

        try {
            await this.deleteResource(baseUrl, this.deleteBranch.name);
        } catch (error) {
            if (!(error instanceof NotFoundException)) {
                throw error;
            }
        }
    }

    async createPullRequest(
        repoName: string,
        headRef: string,
        baseRef: string,
        title: string,
        body: string,
    ): Promise<any> {
        const maxRetries = 3;
        const retryDelay = 2000;
        let attempt = 0;
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.pullrequests'),
            repoName,
        )}`;

        while (attempt < maxRetries) {
            try {
                // eslint-disable-next-line no-await-in-loop
                return await firstValueFrom(
                    this.httpService.post(
                        baseUrl,
                        {
                            title: title,
                            source: {
                                branch: {
                                    name: headRef,
                                },
                            },
                            destination: {
                                branch: {
                                    name: baseRef,
                                },
                            },
                            description: body,
                        },
                        {
                            headers: this.getAuthorization(),
                        },
                    ),
                );
            } catch (error) {
                attempt++;
                if (attempt >= maxRetries) {
                    this.handleError(null, error, baseUrl, this.createPullRequest.name);
                    throw error;
                } else {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        return null;
    }

    async createPullRequestComment(
        repoName: string,
        pullRequestIdentifier: string,
        body: string,
    ): Promise<any> {
        const maxRetries = 3;
        const retryDelay = 2000;
        let attempt = 0;
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.pullrequests'),
            repoName,
        )}/${pullRequestIdentifier}/comments`;

        while (attempt < maxRetries) {
            try {
                // eslint-disable-next-line no-await-in-loop
                return await firstValueFrom(
                    this.httpService.post(
                        baseUrl,
                        {
                            content: { raw: body },
                        },
                        {
                            headers: this.getAuthorization(),
                        },
                    ),
                );
            } catch (error) {
                attempt++;
                if (attempt >= maxRetries) {
                    this.handleError(null, error, baseUrl, this.createPullRequestComment.name);
                    throw error;
                } else {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        return null;
    }

    async updatePullRequestSummary(
        repoName: string,
        pullRequestId: string,
        updatedBody: string,
    ): Promise<any> {
        const maxRetries = 3;
        const retryDelay = 2000;
        let attempt = 0;
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.pullrequests'),
            repoName,
        )}/${pullRequestId}`;

        while (attempt < maxRetries) {
            try {
                // eslint-disable-next-line no-await-in-loop
                return await firstValueFrom(
                    this.httpService.put(
                        baseUrl,
                        {
                            description: updatedBody, // summary: { raw: updatedBody },
                        },
                        {
                            headers: this.getAuthorization(),
                        },
                    ),
                );
            } catch (error) {
                attempt++;
                if (attempt >= maxRetries) {
                    this.handleError(null, error, baseUrl, this.updatePullRequestSummary.name);
                    throw error;
                } else {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        return null;
    }

    async declinePullRequest(repoName: string, pullRequestId: string) {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.pullrequests'),
            repoName,
        )}/${pullRequestId}/decline`;

        await this.httpService
            .post(
                baseUrl,
                {},
                {
                    headers: this.getAuthorization(),
                },
            )
            .toPromise()
            .catch(error => {
                this.handleError(null, error, baseUrl, this.declinePullRequest.name);
            });
    }

    /**
     *
     * @param baseUrl
     * @param hint
     * @returns
     */
    private async deleteResource(baseUrl: string, hint: string): Promise<AxiosResponse<any, any>> {
        try {
            const response = await lastValueFrom(
                this.httpService.delete(baseUrl, {
                    headers: this.getAuthorization(),
                }),
            );

            this.ifDidReceiveError(response, baseUrl, hint);

            return response;
        } catch (error) {
            if (this.isEntityNotFound(error)) {
                throw new NotFoundException(error);
            } else {
                this.handleError(null, error, baseUrl, hint);
                throw error;
            }
        }
    }

    async getOpenPullRequestsByPrefix(
        fullRepoName: string,
        accountId: string,
    ): Promise<{ branchName: string; pullRequestId: string }[]> {
        const baseUrl = `${config.get('bitbucket.api.url')}${format(
            config.get('bitbucket.api.endpoints.pullrequests'),
            fullRepoName,
        )}`;

        const branchPrefix = `drata/${accountId}`;

        const query = `state="OPEN"`;
        const url = `${baseUrl}?q=${encodeURIComponent(query)}`;

        const response = await this.getAllPages(url, this.getOpenPullRequestsByPrefix.name);

        return response
            .filter(pr => pr.source.branch.name.startsWith(branchPrefix))
            .map(pr => ({
                branchName: pr.source.branch.name,
                pullRequestId: pr.id,
            }));
    }

    async getAllPages(baseUrl: string, hint: string, retryOptions?: RetryOptions): Promise<any[]> {
        let allData = [];
        let nextPageUrl = baseUrl;
        const retryConfig = retryOptions
            ? this.buildRetryObserverConfig({ baseUrl, hint }, retryOptions)
            : null;

        do {
            let reqSeq = this.httpService.get(nextPageUrl, {
                headers: this.getAuthorization(),
            });
            if (retryConfig) {
                reqSeq = reqSeq.pipe(retry(retryConfig));
            }

            try {
                // eslint-disable-next-line no-await-in-loop
                const response = await lastValueFrom(reqSeq);
                this.ifDidReceiveError(response, baseUrl, hint);

                const data = get(response, 'data.values', []);
                allData = allData.concat(data);

                nextPageUrl = get(response, 'data.next', null);
            } catch (error) {
                this.handleError(null, error, baseUrl, hint);

                if (this.isEntityNotFound(error)) {
                    throw new NotFoundException(error);
                } else {
                    throw error;
                }
            }
        } while (nextPageUrl);

        return allData;
    }

    private getDefaultRetryOptions(): RetryOptions {
        return {
            maxRetries: BitbucketSdkConstants.DEFAULT_MAX_RETRIES,
            baseDelay: BitbucketSdkConstants.DEFAULT_RETRY_BASE_DELAY,
            exponential: BitbucketSdkConstants.DEFAULT_RETRY_EXPONENTIAL,
            maxDelay: BitbucketSdkConstants.DEFAULT_MAX_RETRY_DELAY,
        };
    }

    private buildRetryObserverConfig(
        requestInfo: { baseUrl: string; hint: string },
        retryOpts: RetryOptions,
    ): RetryConfig {
        const { baseUrl, hint } = requestInfo;
        const {
            maxRetries,
            baseDelay = BitbucketSdkConstants.DEFAULT_RETRY_BASE_DELAY,
            exponential = false,
            maxDelay = BitbucketSdkConstants.DEFAULT_MAX_RETRY_DELAY,
            isRetryableError = (error: any) => this.isRetryableError(error),
        } = retryOpts;
        return {
            delay: (error, retryAttempt) => {
                if (retryAttempt <= maxRetries && isRetryableError(error)) {
                    const waitTime = exponential
                        ? getRandomizedExponentialWait(baseDelay, retryAttempt, maxDelay)
                        : baseDelay * retryAttempt;

                    return timer(waitTime).pipe(
                        tap({
                            subscribe: () => this.logRetry(baseUrl, hint, retryAttempt, waitTime),
                        }),
                    );
                }
                return throwError(() => error);
            },
        };
    }

    private isRetryableError(error: any): boolean {
        return isAxiosError(error) && error.response?.status === HttpStatusCode.TooManyRequests;
    }

    private logRetry(baseUrl: string, hint: string, retryAttempt: number, waitTime: number): void {
        this.logger.log(
            this.buildPolloMessage(`Retrying ${hint} after ${waitTime}ms delay`)
                .setContext(this.constructor.name)
                .setSubContext(hint)
                .setIdentifier({ baseUrl, retryAttempt }),
        );
    }
}
