import { ErrorCode } from '@drata/enums';
import {
    ExecutionContext,
    ForbiddenException,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common';
import { BulkImportFeatureFlagService } from 'app/bulk-import/bulk-import-api/services/bulk-import-feature-flag.service';
import { BulkImportType } from 'app/flatfile-bulk-import/enums/entity-type.enum';
import { JwtResponseType } from 'auth/types/jwt-response.type';
import { BaseGuard } from 'commons/guards/base.guard';
import { get } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { TenancyServiceFactory } from 'tenancy/factories/tenancy-service.factory';

@Injectable()
export class BulkImportFeatureFlagGuard extends BaseGuard {
    constructor(
        private readonly factory: TenancyServiceFactory,
        private readonly flatFileFlags: BulkImportFeatureFlagService,
    ) {
        super();
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        try {
            const request = context.switchToHttp().getRequest();
            const { user, account } = get(request, 'user') as JwtResponseType;

            // Check the base bulk import feature flag first
            const isFeatureEnabled = await this.flatFileFlags.isBulkImportFeatureEnabled(account);

            // Check entity type for specific feature flags
            const entityType = request.params?.entityType || request.body?.entityType;
            let isEntityTypeEnabled = false;

            switch (entityType) {
                case BulkImportType[BulkImportType.RISK]:
                    isEntityTypeEnabled = await this.flatFileFlags.isBulkImportRisksFeatureEnabled(
                        user,
                        account,
                    );
                    break;
                case BulkImportType[BulkImportType.CONTROL]:
                    isEntityTypeEnabled =
                        await this.flatFileFlags.isBulkImportControlsFeatureEnabled(user, account);
                    break;
                case BulkImportType[BulkImportType.TRAINING]:
                    isEntityTypeEnabled =
                        await this.flatFileFlags.isBulkImportTrainingsFeatureEnabled(user, account);
                    break;
                default:
                    return false;
            }

            const isEnabled = isFeatureEnabled && isEntityTypeEnabled;

            this.logger.debug(
                PolloMessage.msg('Checking bulk import feature flag')
                    .setDomain(account.domain)
                    .setIdentifier({
                        enabled: isEnabled,
                        entityType,
                        baseFeatureEnabled: isFeatureEnabled,
                        entityTypeEnabled: isEntityTypeEnabled,
                    }),
            );

            return isEnabled;
        } catch (error) {
            if (error instanceof ForbiddenException) {
                throw error;
            }

            // Check unauthorized error to send the correct exception
            if ((error as Error).message === 'Unauthorized') {
                throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            this.logger.error(
                PolloMessage.msg('Error in FlatFile feature flag middleware').setError(error),
            );

            throw new ForbiddenException(
                'Feature flag check failed',
                ErrorCode.FEATURE_FLAG_DISABLED.toString(),
            );
        }
    }
}
