import { NotFoundError, Space } from '@flatfile/api/api';
import FlatfileListener, { FlatfileEvent } from '@flatfile/listener';
import { PubSubDriver } from '@flatfile/listener-driver-pubsub';
import { Inject, Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { WorkflowExecutionAlreadyStartedError } from '@temporalio/client';
import { BulkImportFeatureFlagService } from 'app/bulk-import/bulk-import-api/services/bulk-import-feature-flag.service';
import { SpaceMetadata as SecuredSpaceMetadata } from 'app/flatfile-bulk-import/classes/space-metadata';
import { FlatFileSdk } from 'app/flatfile-bulk-import/services/flat-file.sdk';
import { SpaceMetadata } from 'app/flatfile-bulk-import/types/space-metadata.type';
import {
    fileCreatedSignal,
    flatfileSpaceMonitorWorkflow,
    sheetSubmittedSignal,
    sheetValidatedSignal,
    SignalDefinitionType,
    spaceClosedSignal,
    workbookMapJobCompletedSignal,
} from 'app/worker/workflows/bulk-import/flatfile-space-monitor.v1.workflow';
import { FlatfileEventInput } from 'app/worker/workflows/bulk-import/types/flatfile-processing-workflow.types';
import {
    FileCreatedEvent,
    SheetSubmitEvent,
    WorkbookMapJobCompletedEvent,
} from 'app/worker/workflows/bulk-import/types/flatfile-space-monitor.types';
import { APIType } from 'commons/enums/api-type.enum';
import { configCoerceBoolean } from 'commons/helpers/boolean.helper';
import { getApiType, getEnvironmentName, isLocal } from 'commons/helpers/environment.helper';
import { getTemporalClient, TemporalClient } from 'commons/helpers/temporal/client';
import { AccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import { isEmpty } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * Service that bridges Flatfile PubSub events to Temporal workflows.
 * This service:
 * 1. Listens to ALL Flatfile events globally (no space filtering)
 * 2. Converts events to Temporal signals
 * 3. Uses signalWithStart to ensure space monitor workflows exist
 * 4. Handles duplicate prevention through Temporal's workflow IDs
 * 5. Survives server restarts by reconnecting to PubSub automatically
 */
@Injectable()
export class FlatfileTemporalBridgeService implements OnModuleInit, OnModuleDestroy {
    private handler: FlatfileListener;
    private driver: PubSubDriver;
    private temporalClient: TemporalClient;
    private logger: PolloLogger<PolloMessage>;
    private flatfileEnvironmentId: string;
    private sdk = new FlatFileSdk();
    private runtimeEnvironment: string;
    // temporal solution until Stephen merge his cache PR
    private spaceCache = new Map<string, Space>();

    constructor(
        @Inject('FLATFILE_API_KEY') private readonly FLATFILE_API_KEY: string,
        @Inject('FLATFILE_ENVIRONMENT_ID') private readonly FLATFILE_ENVIRONMENT_ID: string,
        private readonly bulkImportFeatureFlagService: BulkImportFeatureFlagService,
    ) {
        this.logger = PolloLogger.logger(this.constructor.name);
    }

    private init() {
        process.env.FLATFILE_BEARER_TOKEN = this.FLATFILE_API_KEY;
        this.flatfileEnvironmentId = this.FLATFILE_ENVIRONMENT_ID;
        this.runtimeEnvironment = getEnvironmentName();

        this.driver = new PubSubDriver(this.flatfileEnvironmentId);
        this.handler = new FlatfileListener();
    }

    async onModuleInit(): Promise<void> {
        try {
            if (this.isOnCliMode()) {
                this.logger.log(PolloMessage.msg('Skipping Flatfile-Temporal bridge in CLI mode'));
                return;
            }

            if (this.isTemporalDisabled()) {
                this.logger.log(PolloMessage.msg('Temporal is disabled - skipping bridge'));
                return;
            }

            if (this.isWorkerProcess()) {
                this.logger.log(PolloMessage.msg('Skipping Flatfile-Temporal bridge in worker'));
                return;
            }

            const isFeatureEnabled =
                await this.bulkImportFeatureFlagService.isBulkImportFeatureEnabledGlobally();

            if (!isFeatureEnabled) {
                this.logger.log(PolloMessage.msg('Bulk import feature disabled - skipping bridge'));
                return;
            }

            if (getApiType() !== APIType.PRIVATE) {
                this.logger.log(
                    PolloMessage.msg('Skipping Flatfile-Temporal bridge in non-private API'),
                );
                return;
            }
            this.init();

            this.temporalClient = await getTemporalClient();

            // Set up global event bridge
            this.setupGlobalEventBridge();

            // Start the PubSub driver
            this.driver.mountEventHandler(this.handler);
            await this.driver.start(true);

            this.logger.log(PolloMessage.msg('✅ Flatfile-Temporal bridge started successfully!'));
        } catch (error) {
            // Just log, don't throw
            this.logger.warn(
                PolloMessage.msg('Failed to start Flatfile-Temporal bridge')
                    .setIdentifier({
                        temporalEnabled: process.env.TEMPORAL_ENABLED,
                        isWorkerProcess: process.env.TEMPORAL_WORKER,
                        taskStartedBy: process.env.TASK_STARTED_BY,
                        cliMode: process.env.CLI_MODE,
                        apiType: getApiType(),
                        mode: process.env.MODE,
                        processType: process.env.PROCESS_TYPE,
                        temporalWorker: process.env.TEMPORAL_WORKER,
                    })
                    .setError(error),
            );
        }
    }

    async onModuleDestroy(): Promise<void> {
        try {
            if (this.driver) {
                this.driver.shutdown();
                this.logger.log(PolloMessage.msg('Flatfile-Temporal bridge shut down'));
            }
        } catch (error) {
            this.logger.error(PolloMessage.msg('Error during bridge shutdown').setError(error));
        }
    }

    private isOnCliMode(): boolean {
        return (
            process.env.TASK_STARTED_BY === 'scheduled_task' ||
            process.env.CLI_MODE === 'cron' ||
            process.env.CLI_MODE === 'manual'
        );
    }

    private isWorkerProcess(): boolean {
        // Multiple checks to ensure we detect worker process correctly
        const checks = [
            // Check command line arguments
            process.argv.some(arg => arg.includes('worker')),
            process.argv.some(arg => arg.includes('app/worker/main')),

            // Check environment variables
            process.env.PROCESS_TYPE === 'worker',
            process.env.TEMPORAL_WORKER === 'true',

            // Check process title
            process.title.includes('worker'),

            // Check if main file is worker
            process.argv[1]?.includes('worker/main'),
        ];

        const isWorker = checks.some(check => check);

        if (isWorker) {
            this.logger.debug(
                PolloMessage.msg('Detected worker process').setIdentifier({
                    argv: process.argv,
                    processType: process.env.PROCESS_TYPE,
                    mainFile: process.argv[1],
                }),
            );
        }

        return isWorker;
    }

    private isTemporalDisabled(): boolean {
        return configCoerceBoolean(config.get('temporal.enabled')) === false;
    }

    private setupGlobalEventBridge(): void {
        // this.handler.use(ZipExtractor());
        this.handler.on('file:created', async event => {
            await this.validateSpace(event, () => this.bridgeFileCreatedEvent(event));
        });

        this.handler.on('commit:created', async event => {
            await this.validateSpace(event, () => this.bridgeSheetValidatedEvent(event));
        });

        this.handler.on('space:deleted', async event => {
            await this.validateSpace(event, () => this.bridgeSpaceDeletedEvent(event));
        });

        this.handler.on('job:completed', { job: 'workbook:map' }, async event => {
            await this.validateSpace(event, () => this.bridgeWorkbookMapJobCompletedEvent(event));
        });

        this.handler.on('job:ready', { job: 'workbook:submitAction' }, async event => {
            await this.validateSpace(event, () => this.bridgeSubmitActionCompletedEvent(event));
        });
    }

    private async validateSpace(
        event: FlatfileEvent,
        bridgeEvent: () => Promise<void>,
    ): Promise<void> {
        const spaceId = event.context?.spaceId;

        if (!spaceId) {
            this.logger.warn(PolloMessage.msg(`Received ${event.topic} event without spaceId`));
            return;
        }

        if (!(await this.shouldProcessEvent(spaceId))) {
            return; // Skip silently
        }

        await bridgeEvent();
    }

    private async getContextFromEvent(
        event: FlatfileEvent,
    ): Promise<Record<string, AccountIdType>> {
        const spaceId = event.context?.spaceId;
        const accountId = await this.extractAccountId(event);
        return {
            spaceId,
            accountId,
        };
    }

    /**
     * Bridge file:created events to Temporal space monitor workflows
     */
    private async bridgeFileCreatedEvent(event: FlatfileEvent): Promise<void> {
        try {
            const { spaceId, accountId } = await this.getContextFromEvent(event);
            const fileId = event.context?.fileId;

            if (!spaceId || !fileId || !accountId) {
                this.logger.error(
                    PolloMessage.msg('Missing required fields in file:created event').setIdentifier(
                        { spaceId, fileId, accountId, eventContext: event.context },
                    ),
                );
                return;
            }

            const fileCreatedEvent: FileCreatedEvent = {
                spaceId,
                fileId,
                accountId,
                fileName: event.context?.fileName,
                fileSize: event.context?.fileSize,
                context: event.context,
            };

            await this.signalSpaceMonitorWorkflow(
                spaceId,
                accountId,
                fileCreatedSignal,
                fileCreatedEvent,
            );

            this.logger.log(
                PolloMessage.msg('File created event bridged to Temporal').setIdentifier({
                    spaceId,
                    fileId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to bridge file:created event')
                    .setContext(event.context)
                    .setError(error),
            );
        }
    }

    /**
     * Bridge sheet:validated events to Temporal space monitor workflows
     */
    private async bridgeSheetValidatedEvent(event: FlatfileEvent): Promise<void> {
        const { spaceId, accountId } = await this.getContextFromEvent(event);
        try {
            const sheetId = event.context?.sheetId;
            const eventId = event.src.id;

            if (!spaceId || !sheetId || !accountId) {
                this.logger.error(
                    PolloMessage.msg(
                        'Missing required fields in sheet:validated event',
                    ).setIdentifier({ spaceId, sheetId, accountId, eventContext: event.context }),
                );
                return;
            }

            const flatfileEvent: FlatfileEventInput = {
                eventId,
                sheetId,
                topic: event.topic,
                context: event.context,
                createdAt: event.createdAt || new Date(),
                domain: event.domain,
                origin: event.origin,
                src: event.src,
            };

            await this.signalSpaceMonitorWorkflow(
                spaceId,
                accountId,
                sheetValidatedSignal,
                flatfileEvent,
            );

            this.logger.log(
                PolloMessage.msg('Sheet validated event bridged to Temporal').setIdentifier({
                    spaceId,
                    sheetId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to bridge sheet:validated event')
                    .setError(error)
                    .setContext(event.context),
            );
        }
    }

    /**
     * Handle space deletion by signaling the monitor workflow to stop
     */
    private async bridgeSpaceDeletedEvent(event: FlatfileEvent): Promise<void> {
        try {
            const spaceId = event.context?.spaceId;

            if (!spaceId) {
                this.logger.error(
                    PolloMessage.msg('Missing spaceId in space:deleted event').setContext(
                        event.context,
                    ),
                );
                return;
            }

            // Signal the space monitor workflow to close
            const workflowId = `flatfileSpaceMonitor-${spaceId}`;

            try {
                await this.temporalClient.sendSignal(workflowId, spaceClosedSignal, []);

                this.logger.log(
                    PolloMessage.msg('Space deletion signaled to monitor workflow').setIdentifier({
                        spaceId,
                        workflowId,
                    }),
                );
            } catch (signalError) {
                // Workflow might not exist - that's OK
                this.logger.debug(
                    PolloMessage.msg('Could not signal space closure (workflow may not exist)')
                        .setIdentifier({ spaceId, workflowId })
                        .setError(signalError),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to bridge space:deleted event')
                    .setContext(event.context)
                    .setError(error),
            );
        }
    }

    /**
     * Bridge job:completed events to Temporal space monitor workflows
     */
    private async bridgeWorkbookMapJobCompletedEvent(event: FlatfileEvent): Promise<void> {
        const { spaceId, accountId } = await this.getContextFromEvent(event);
        try {
            const jobId = event.origin['id'] as string;
            const eventId = event.src.id;
            const workbookId = event.context?.workbookId;

            if (!spaceId || !jobId || !accountId || !eventId) {
                this.logger.error(
                    PolloMessage.msg(
                        'Missing required fields in job:completed event',
                    ).setIdentifier({
                        accountId,
                        spaceId,
                        eventId,
                        jobId,
                        eventContext: event.context,
                    }),
                );
                return;
            }

            const sheets = await this.sdk.getWorkbookSheets(workbookId);
            const sheetId = sheets.find(sheet => sheet.slug === 'risks')?.id;

            const workbookMapJobCompletedEvent: WorkbookMapJobCompletedEvent = {
                accountId,
                spaceId,
                eventId,
                jobId,
                sheetId,
                workbookId,
            };

            await this.signalSpaceMonitorWorkflow(
                spaceId,
                accountId,
                workbookMapJobCompletedSignal,
                workbookMapJobCompletedEvent,
            );

            this.logger.log(
                PolloMessage.msg(
                    'Workbook Map Job completion event bridged to Temporal',
                ).setIdentifier({
                    spaceId,
                    jobId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to bridge job:completed event')
                    .setContext(event.context)
                    .setError(error),
            );
        }
    }

    /**
     * Bridge job:completed events to Temporal space monitor workflows
     */
    private async bridgeSubmitActionCompletedEvent(event: FlatfileEvent): Promise<void> {
        const { spaceId, accountId } = await this.getContextFromEvent(event);
        try {
            const jobId = event.origin['id'] as string;
            const eventId = event.src.id;

            if (!spaceId || !jobId || !accountId || !eventId) {
                this.logger.error(
                    PolloMessage.msg('Missing required fields in job:ready event').setIdentifier({
                        accountId,
                        spaceId,
                        eventId,
                        jobId,
                        eventContext: event.context,
                    }),
                );
                return;
            }

            const sheetSubmittedJobReadyEvent: SheetSubmitEvent = {
                sheetId: event.context?.sheetId,
                sheetName: event.context?.sheetName,
                accountId,
                spaceId,
                eventId,
                jobId,
                userId: event.context?.userId,
            };

            await this.signalSpaceMonitorWorkflow(
                spaceId,
                accountId,
                sheetSubmittedSignal,
                sheetSubmittedJobReadyEvent,
            );

            this.logger.log(
                PolloMessage.msg(
                    'Workbook Map Job completion event bridged to Temporal',
                ).setIdentifier({
                    spaceId,
                    jobId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to bridge job:completed event')
                    .setContext(event.context)
                    .setError(error),
            );
        }
    }

    /**
     * Signal a space monitor workflow using signalWithStart pattern
     * This ensures the workflow exists and receives the signal
     */
    private async signalSpaceMonitorWorkflow(
        spaceId: string,
        accountId: string,
        signal: SignalDefinitionType,
        signalArgs:
            | FileCreatedEvent
            | FlatfileEventInput
            | WorkbookMapJobCompletedEvent
            | SheetSubmitEvent,
    ): Promise<void> {
        // attach accountID to workflowId
        const workflowId = `flatfileSpaceMonitor-${spaceId}`;
        try {
            await this.temporalClient.signalWithStart(flatfileSpaceMonitorWorkflow, {
                workflowId,
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [spaceId, accountId],
                memo: {
                    accountId,
                },
                signal,
                signalArgs: [signalArgs],
            });

            this.logger.debug(
                PolloMessage.msg('Signaled space monitor workflow').setIdentifier({
                    spaceId,
                    workflowId,
                }),
            );
        } catch (error) {
            if (error instanceof WorkflowExecutionAlreadyStartedError) {
                // This is expected - workflow already exists, just signal it
                try {
                    await this.temporalClient.sendSignal(workflowId, signal, [signalArgs]);

                    this.logger.debug(
                        PolloMessage.msg('Signaled existing space monitor workflow').setIdentifier({
                            spaceId,
                            workflowId,
                        }),
                    );
                } catch (signalError) {
                    this.logger.error(
                        PolloMessage.msg('Failed to signal existing workflow')
                            .setError(signalError)
                            .setIdentifier({ spaceId, workflowId }),
                    );
                    throw signalError;
                }
            } else {
                this.logger.error(
                    PolloMessage.msg('Failed to signal space monitor workflow')
                        .setError(error)
                        .setIdentifier({ spaceId, workflowId }),
                );
                throw error;
            }
        }
    }

    /**
     * Extract account ID from Flatfile event context
     * This may need to be customized based on how account info is stored in events
     */
    private async extractAccountId(event: FlatfileEvent): Promise<AccountIdType> {
        const space = await this.getSpace(event.context?.spaceId);
        return SecuredSpaceMetadata.parse(space.metadata?.spaceMetadata).accountId;
    }

    private async extractWorkspaceId(event: FlatfileEvent): Promise<number> {
        const space = await this.getSpace(event.context?.spaceId);
        return Number(space.metadata.workspaceId);
    }

    private async shouldProcessEvent(spaceId: string): Promise<boolean> {
        try {
            // TODO: cache the response ENG-72102
            const space = await this.getSpace(spaceId);
            const metadata = space.metadata;
            const spaceEnvironment = metadata?.environment;
            // for local development, you may get events from the same environment
            // for now lets ignore them
            const localWorkflowId = metadata?.beingHandledByWorkflowId;
            const isBeingHandledByWorkflow = !isEmpty(localWorkflowId);

            if (isLocal() && isBeingHandledByWorkflow) {
                const isLocalHandlingIt = await this.isMyLocalHandlingIt(localWorkflowId);
                if (!isLocalHandlingIt) {
                    this.logger.debug(
                        PolloMessage.msg('Skipping event from local environment').setIdentifier({
                            spaceId,
                            localWorkflowId,
                        }),
                    );
                    return false;
                }
                return isLocalHandlingIt;
            }

            if (!spaceEnvironment) {
                // Legacy space - only process spaces with env attached to avoid breaking existing functionality
                this.logger.debug(
                    PolloMessage.msg(
                        'Skipping legacy space in non-production environment',
                    ).setIdentifier({ spaceId }),
                );

                return false;
            }

            const shouldProcess = spaceEnvironment === this.runtimeEnvironment;

            if (!shouldProcess) {
                this.logger.debug(PolloMessage.msg('Skipping event from different environment'));
            }

            return shouldProcess;
        } catch (error) {
            if (error instanceof NotFoundError) {
                this.logger.debug(
                    PolloMessage.msg('Skipping event from deleted space').setIdentifier({
                        spaceId,
                    }),
                );
                return false;
            }

            this.logger.error(
                PolloMessage.msg('Failed to check space environment')
                    .setError(error)
                    .setIdentifier({ spaceId }),
            );
            return false;
        }
    }

    private async updateSpaceMetadata(
        spaceId: string,
        metadata: Pick<SpaceMetadata, 'beingHandledByWorkflowId'>,
    ): Promise<void> {
        try {
            const space = await this.sdk.getSpace(spaceId);
            const previousMetadata = space.data.metadata;
            await this.sdk.updateSpaceMetadata(spaceId, {
                ...previousMetadata,
                ...metadata,
            });
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to update space metadata')
                    .setError(error)
                    .setIdentifier({ spaceId }),
            );
        }
    }

    private async isMyLocalHandlingIt(workflowId: string): Promise<boolean> {
        const activeWorkflows = await this.temporalClient.listWorkflowExecutions({
            query: `WorkflowId = "${workflowId}" AND ExecutionStatus = "Running"`,
        });
        return !isEmpty(activeWorkflows.executions);
    }

    private async getSpace(spaceId: string): Promise<Space> {
        const cachedSpace = this.spaceCache.get(spaceId);
        let space: Space = {} as Space;
        if (!cachedSpace) {
            space = (await this.sdk.getSpace(spaceId)).data;
            this.spaceCache.set(spaceId, space);
        } else {
            space = cachedSpace;
        }
        return space;
    }
}
