import { Test, TestingModule } from '@nestjs/testing';
import { ControlBlueprintField } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.fields';
import { ControlValidationService } from 'app/bulk-import/bulk-import-commerce/validations/controls/control-validations.service';
import { ControlService } from 'app/control/control.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Role } from 'commons/enums/users/role.enum';
import { mock, MockProxy } from 'jest-mock-extended';
import { first } from 'lodash';
import moment from 'moment';

describe('ControlValidationService', () => {
    let service: ControlValidationService;
    let mockControlService: MockProxy<ControlService>;
    let mockUserService: MockProxy<UsersCoreService>;

    const propertyOrder = {
        name: 0,
        code: 1,
        owners: 2,
        description: 3,
        question: 4,
        activity: 5,
        notes: 6,
        approvers: 7,
        approvalDeadline: 8,
    };

    beforeEach(async () => {
        mockControlService = mock<ControlService>();
        mockUserService = mock<UsersCoreService>();
        
        mockControlService.findControlsByCodeAndName.mockResolvedValue([]);
        
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ControlValidationService,
                { provide: ControlService, useValue: mockControlService },
                { provide: UsersCoreService, useValue: mockUserService },
            ],
        }).compile();

        service = module.get<ControlValidationService>(ControlValidationService);
    });

    const createValidControlData = () => ({
        [ControlBlueprintField.NAME]: 'Test Control',
        [ControlBlueprintField.CODE]: 'TC-001',
        [ControlBlueprintField.DESCRIPTION]: 'Test control description',
        [ControlBlueprintField.OWNERS]: ['<EMAIL>'],
        [ControlBlueprintField.QUESTION]: 'Test question?',
        [ControlBlueprintField.ACTIVITY]: 'Test activity',
        [ControlBlueprintField.NOTES]: ['Test notes'],
    });

    const setupAccountMock = () => {
        const mockProduct = 1;
        service.setWorkspaceId(mockProduct);
    };

    describe('constructor', () => {
        it('should be defined', () => {
            expect(service).toBeDefined();
        });

        it('should initialize with email validation dependency', () => {
            expect(service).toBeInstanceOf(ControlValidationService);
        });
    });

    describe('validate', () => {
        beforeEach(() => {
            setupAccountMock();
        });

        describe('NAME field validation', () => {
            it('should pass validation for valid name', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const [nameResult] = results;
                expect(nameResult?.valid).toBe(true);
            });

            it('should fail when name is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.NAME] = '';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const nameResult = results.find(r => r.field === ControlBlueprintField.NAME);
                expect(nameResult?.valid).toBe(false);
                const firstMessage = first(nameResult?.messages);
                expect(firstMessage?.message).toContain('is required');
            });

            it('should fail when name exceeds 191 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.NAME] = 'a'.repeat(192);
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const nameResult = results.find(r => r.field === ControlBlueprintField.NAME);
                expect(nameResult?.valid).toBe(false);
                const firstMessage = first(nameResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 191 characters.',
                );
            });
        });

        describe('CODE field validation', () => {
            it('should pass validation for valid unique code', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const [, codeResult] = results;
                expect(codeResult?.valid).toBe(true);
            });

            it('should fail when code is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.CODE] = '';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const codeResult = results.find(r => r.field === ControlBlueprintField.CODE);
                expect(codeResult?.valid).toBe(false);
                const firstMessage = first(codeResult?.messages);
                expect(firstMessage?.message).toContain('is required');
            });

            it('should fail when code already exists', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: false });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const codeResult = results[propertyOrder.code];
                expect(codeResult?.valid).toBe(false);
                const firstMessage = first(codeResult?.messages);
                expect(firstMessage?.message).toContain('already exists');
            });

            it('should fail when code exceeds 20 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.CODE] = 'a'.repeat(21);
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const codeResult = results.find(r => r.field === ControlBlueprintField.CODE);
                expect(codeResult?.valid).toBe(false);
                const firstMessage = first(codeResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 20 characters.',
                );
            });
        });

        describe('OWNERS field validation', () => {
            it('should pass validation for valid email array', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const ownersResult = results[propertyOrder.owners];
                expect(ownersResult?.valid).toBe(true);
                expect(mockUserService.getUserByEmailNoFail).toHaveBeenCalledWith(
                    '<EMAIL>',
                );
            });

            it('should pass validation when owners is empty (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.OWNERS] = [];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const ownersResult = results[propertyOrder.owners];
                expect(ownersResult?.valid).toBe(true);
            });

            it('should fail when owner email does not exist', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue(null);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const ownersResult = results[propertyOrder.owners];
                expect(ownersResult?.valid).toBe(false);
            });

            it('should fail when owner email is invalid format', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.OWNERS] = ['invalid-email'];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const ownersResult = results[propertyOrder.owners];
                expect(ownersResult?.valid).toBe(false);
            });
        });

        describe('DESCRIPTION field validation', () => {
            it('should pass validation for valid description', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const descResult = results[propertyOrder.description];
                expect(descResult?.valid).toBe(true);
            });

            it('should fail when description is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.DESCRIPTION] = '';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const descResult = results[propertyOrder.description];
                expect(descResult?.valid).toBe(false);
                const firstMessage = first(descResult?.messages);
                expect(firstMessage?.message).toContain('is required');
            });

            it('should fail when description exceeds 30000 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.DESCRIPTION] = 'a'.repeat(30001);
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const descResult = results[propertyOrder.description];
                expect(descResult?.valid).toBe(false);
                const firstMessage = first(descResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 30,000 characters.',
                );
            });
        });

        describe('QUESTION field validation', () => {
            it('should pass validation for valid question (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const questionResult = results[propertyOrder.question];
                expect(questionResult?.valid).toBe(true);
            });

            it('should pass validation when question is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                delete controlData[ControlBlueprintField.QUESTION];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const questionResult = results[propertyOrder.question];
                expect(questionResult?.valid).toBe(true);
            });

            it('should fail when question exceeds 30000 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.QUESTION] = 'a'.repeat(30001);
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const questionResult = results[propertyOrder.question];
                expect(questionResult?.valid).toBe(false);
                const firstMessage = first(questionResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 30,000 characters.',
                );
            });
        });

        describe('ACTIVITY field validation', () => {
            it('should pass validation for valid activity (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const activityResult = results[propertyOrder.activity];
                expect(activityResult?.valid).toBe(true);
            });

            it('should pass validation when activity is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                delete controlData[ControlBlueprintField.ACTIVITY];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const activityResult = results[propertyOrder.activity];
                expect(activityResult?.valid).toBe(true);
            });

            it('should fail when activity exceeds 30000 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.ACTIVITY] = 'a'.repeat(30001);
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const activityResult = results[propertyOrder.activity];
                expect(activityResult?.valid).toBe(false);
                const firstMessage = first(activityResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 30,000 characters.',
                );
            });
        });

        describe('NOTES field validation', () => {
            it('should pass validation for valid notes (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const notesResult = results[propertyOrder.notes];
                expect(notesResult?.valid).toBe(true);
            });

            it('should pass validation when notes is empty', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.NOTES] = [];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const notesResult = results[propertyOrder.notes];
                expect(notesResult?.valid).toBe(true);
            });

            it('should fail when notes exceed 30000 characters', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.NOTES] = ['a'.repeat(30001)];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const notesResult = results[propertyOrder.notes];
                expect(notesResult?.valid).toBe(false);
                expect(notesResult?.messages).toHaveLength(1);
                const firstMessage = first(notesResult?.messages);
                expect(firstMessage?.message).toContain(
                    'must be a minimum of 1 and a maximum of 30,000 characters.',
                );
            });
        });

        describe('APPROVERS field validation', () => {
            it('should pass validation when approvers and approval deadline are not set (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                delete controlData[ControlBlueprintField.APPROVERS];
                delete controlData[ControlBlueprintField.APPROVAL_DEADLINE];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                expect(approversResult?.valid).toBe(true);
            });

            it('should pass validation for valid approver emails with required roles', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = [
                    '<EMAIL>',
                    '<EMAIL>',
                ];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                expect(approversResult?.valid).toBe(true);
            });

            it('should fail when approver email is invalid format', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = ['invalid-email'];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                const firstMessage = first(approversResult?.messages);
                expect(approversResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain('must be a valid email');
            });

            it('should fail when approver email does not exist in system', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue(null);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                const firstMessage = first(approversResult?.messages);
                expect(approversResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain('not found');
            });

            it('should fail when approver does not have required roles', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.EMPLOYEE }], // Employee role is not in the required roles list
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                const firstMessage = first(approversResult?.messages);
                expect(approversResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain(
                    'do not have one of the required roles: Admins, Workspace admins, Information Security Leads or Control Managers.',
                );
            });

            it('should fail when approvers is provided without approval deadline', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = ''; // Empty deadline
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                expect(approversResult?.valid).toBe(false);
                const firstMessage = first(approversResult?.messages);
                expect(firstMessage?.message).toContain(
                    'Both Approver and Approval Date are required',
                );
            });

            it('should fail when approvers is provided without control owners', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                delete controlData[ControlBlueprintField.OWNERS]; // Empty owners
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const approversResult = results[propertyOrder.approvers];
                const firstMessage = first(approversResult?.messages);
                expect(approversResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain('A Control Owner is required');
            });

            it('should pass validation for multiple valid approvers with different required roles', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);

                mockUserService.getUserByEmailNoFail.mockResolvedValueOnce({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any); // Owner call
                const totalCallRounds = 2;
                for (let i = 0; i <= totalCallRounds; i++) {
                    mockUserService.getUserByEmailNoFail
                        .mockResolvedValueOnce({ id: 1, roles: [{ role: Role.ADMIN }] } as any)
                        .mockResolvedValueOnce({ id: 2, roles: [{ role: Role.TECHGOV }] } as any)
                        .mockResolvedValueOnce({
                            id: 3,
                            roles: [{ role: Role.CONTROL_MANAGER }],
                        } as any);
                }

                // Act
                const results = await service.validate(controlData);
                expect(mockUserService.getUserByEmailNoFail).toHaveBeenCalledTimes(7);
                // Assert
                const approversResult = results[propertyOrder.approvers];
                expect(approversResult?.valid).toBe(true);
            });

            it('should fail validation for multiple invalid approvers with different required roles', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVERS] = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ];
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '2025-12-31';
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);

                mockUserService.getUserByEmailNoFail.mockResolvedValueOnce({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any); // Owner call
                const totalCallRounds = 2;
                for (let i = 0; i <= totalCallRounds; i++) {
                    mockUserService.getUserByEmailNoFail
                        .mockResolvedValueOnce({ id: 1, roles: [{ role: Role.EMPLOYEE }] } as any)
                        .mockResolvedValueOnce({ id: 2, roles: [{ role: Role.EMPLOYEE }] } as any)
                        .mockResolvedValueOnce({
                            id: 3,
                            roles: [{ role: Role.EMPLOYEE }],
                        } as any);
                }

                // Act
                const results = await service.validate(controlData);
                expect(mockUserService.getUserByEmailNoFail).toHaveBeenCalledTimes(7);
                // Assert
                const approversResult = results[propertyOrder.approvers];
                expect(approversResult?.valid).toBe(false);
                const firstMessage = first(approversResult?.messages);
                expect(firstMessage?.message).toContain(
                    // eslint-disable-next-line max-len
                    'Approver(s) <NAME_EMAIL>, <EMAIL>, <EMAIL> do not have one of the required roles: Admins, Workspace admins, Information Security Leads or Control Managers.',
                );
            });
        });

        describe('APPROVAL_DEADLINE field validation', () => {
            it('should pass validation when approval deadline and approvers are empty (optional field)', async () => {
                // Arrange
                const controlData = createValidControlData();
                delete controlData[ControlBlueprintField.APPROVERS];
                delete controlData[ControlBlueprintField.APPROVAL_DEADLINE];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                expect(deadlineResult?.valid).toBe(true);
            });

            it('should pass validation for future date', async () => {
                // Arrange
                const controlData = createValidControlData();
                const futureDate = new Date();
                futureDate.setDate(futureDate.getDate() + 30);
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = futureDate
                    .toISOString()
                    .split('T')[0];
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);
                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                console.log(deadlineResult);
                expect(deadlineResult?.valid).toBe(true);
            });

            it("should not pass validation for today's date", async () => {
                // Arrange
                const controlData = createValidControlData();
                const today = new Date();
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] =
                    moment(today).format('MM-DD-YYYY');
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);
                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                expect(deadlineResult?.valid).toBe(false);
            });

            it('should fail validation for past date', async () => {
                // Arrange
                const controlData = createValidControlData();
                const pastDate = new Date();
                pastDate.setDate(pastDate.getDate() - 1);
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] =
                    moment(pastDate).format('YYYY-MM-DD');
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                const firstMessage = first(deadlineResult?.messages);
                expect(deadlineResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain('must be in the future');
            });

            it('should fail when approval deadline is provided without approvers', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '12-31-2025';
                delete controlData[ControlBlueprintField.APPROVERS]; // Empty approvers
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                const firstMessage = first(deadlineResult?.messages);
                expect(deadlineResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain(
                    'Both Approver and Approval Date are required',
                );
            });

            it('should fail when approval deadline is provided without control owners', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = '12-31-2025';
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                delete controlData[ControlBlueprintField.OWNERS]; // Empty owners
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                const firstMessage = first(deadlineResult?.messages);
                expect(deadlineResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain('A Control Owner is required');
            });

            it('should handle invalid date format gracefully', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.APPROVAL_DEADLINE] = 'invalid-date';
                controlData[ControlBlueprintField.APPROVERS] = ['<EMAIL>'];
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({
                    id: 1,
                    roles: [{ role: Role.ADMIN }],
                } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const deadlineResult = results[propertyOrder.approvalDeadline];
                const firstMessage = first(deadlineResult?.messages);
                expect(deadlineResult?.valid).toBe(false);
                expect(firstMessage?.message).toContain(
                    'must be a valid date with format: YYYY-MM-DD.',
                );
            });
        });

        describe('Integration tests', () => {
            it('should validate all fields together for a complete valid control', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act
                const results = await service.validate(controlData);

                // Assert
                expect(results).toHaveLength(9); // All 9 fields should be validated
                results.forEach(result => {
                    expect(result.valid).toBe(true);
                });
            });

            it('should return multiple validation errors for invalid control data', async () => {
                // Arrange
                const controlData = createValidControlData();
                controlData[ControlBlueprintField.NAME] = ''; // Required field empty
                controlData[ControlBlueprintField.CODE] = ''; // Required field empty
                controlData[ControlBlueprintField.DESCRIPTION] = ''; // Required field empty
                controlData[ControlBlueprintField.OWNERS] = ['invalid-email']; // Invalid email
                mockControlService.isAvailable.mockResolvedValue({ available: true });
                mockControlService.findControlsByCodes.mockResolvedValue([]);

                // Act
                const results = await service.validate(controlData);

                // Assert
                const invalidResults = results.filter(r => !r.valid);
                expect(invalidResults.length).toBeGreaterThan(0);

                // Check specific validation failures
                const nameResult = results[propertyOrder.name];
                expect(nameResult?.valid).toBe(false);

                const codeResult = results[propertyOrder.code];
                expect(codeResult?.valid).toBe(false);

                const descResult = results[propertyOrder.description];
                expect(descResult?.valid).toBe(false);

                const ownersResult = results[propertyOrder.owners];
                expect(ownersResult?.valid).toBe(false);
            });

            it('should handle service errors gracefully', async () => {
                // Arrange
                const controlData = createValidControlData();
                mockControlService.isAvailable.mockRejectedValue(new Error('Service error'));
                mockControlService.findControlsByCodes.mockResolvedValue([]);
                mockUserService.getUserByEmailNoFail.mockResolvedValue({ id: 1 } as any);

                // Act & Assert
                await expect(service.validate(controlData)).rejects.toThrow('Service error');
            });
        });
    });
});
