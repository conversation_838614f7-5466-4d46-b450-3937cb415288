import { EnumPropertyOption, ValidationType } from '@flatfile/api/api';
import { Injectable } from '@nestjs/common';
import { RecordCollector } from 'app/bulk-import/bulk-import-api/interfaces/record-collector.interface';
import { ControlBlueprintField } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.fields';
import { ControlService } from 'app/control/control.service';
import { User } from 'app/users/entities/user.entity';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { RecordValidation } from 'bulk-import-validation/classes/record-validation';
import { ChainsConfig } from 'bulk-import-validation/classes/validation-chain-builder';
import { OptionsValidator } from 'bulk-import-validation/validators/common/enum-options-validator';
import {
    DateValidator,
    DependsOnValidator,
    EmailValidator,
    OptionalValidator,
    RequiredValidator,
    StringLengthValidator,
    UniqueValidator,
    ValidatorArray,
    WhiteSpaceValidator,
} from 'bulk-import-validation/validators/common/generic-validators';
import {
    ControlApprovalDeadline,
    ControlNameAndCodeValidator,
} from 'bulk-import-validation/validators/domain/control-validators';
import {
    UserEmailValidator,
    UserHasRolesValidator,
} from 'bulk-import-validation/validators/domain/user-validators';
import { Role } from 'commons/enums/users/role.enum';

enum ValidationErrorMessages {
    APPROVERS_DEPENDS_ON_APPROVAL_DEADLINE_AND_VICE_VERSA = 'Both Approver and Approval Date are required to setup a Control approval request.',
    CONTROL_OWNER_REQUIRED = 'A Control Owner is required to setup a Control approval request.',
    // eslint-disable-next-line max-len
    APPROVERS_INVALID_EMAIL = 'Approver(s) with email <emailValue> do not have one of the required roles: Admins, Workspace admins, Information Security Leads or Control Managers.',
}

@Injectable()
export class ControlValidationService extends RecordValidation<unknown> implements RecordCollector {
    private records: Set<string> = new Set();

    protected chains: ChainsConfig<unknown> = {
        [ControlBlueprintField.NAME]: {
            steps: [
                { use: RequiredValidator },
                { use: StringLengthValidator, args: [{ min: 1, max: 191 }] },
                { use: ControlNameAndCodeValidator, args: ['getControlsByCode'] },
                {
                    use: UniqueValidator,
                    args: ['checkControlNameExists'],
                },
                {
                    use: UniqueValidator,
                    args: [
                        'validateNameUniqueness',
                        'Each Control Name can only appear once in the sheet. Please remove duplicates.',
                    ],
                },
            ],
            fieldName: 'Control Name',
        },
        [ControlBlueprintField.CODE]: {
            steps: [
                { use: RequiredValidator },
                { use: WhiteSpaceValidator },
                {
                    use: UniqueValidator,
                    args: [
                        'checkControlCodeExists',
                        'This Control Code already exists. This Control will be updated with the provided values.',
                        ValidationType.Warn,
                    ],
                },
                {
                    use: UniqueValidator,
                    args: [
                        'validateCodeUniqueness',
                        'Each Control Code can only appear once in the sheet. Please remove duplicates.',
                    ],
                },
                { use: StringLengthValidator, args: [{ min: 1, max: 20 }] },
            ],
            fieldName: 'Control Code',
        },
        [ControlBlueprintField.OWNERS]: {
            steps: [
                { use: OptionalValidator },
                { use: ValidatorArray(EmailValidator) },
                { use: ValidatorArray(UserEmailValidator), args: ['checkEmailExists'] },
            ],
            fieldName: 'Control Owner',
        },
        [ControlBlueprintField.DESCRIPTION]: {
            steps: [
                { use: RequiredValidator },
                { use: StringLengthValidator, args: [{ min: 1, max: 30000 }] },
            ],
        },
        [ControlBlueprintField.QUESTION]: {
            steps: [
                { use: OptionalValidator },
                { use: StringLengthValidator, args: [{ min: 1, max: 30000 }] },
            ],
            fieldName: 'Control Question',
        },
        [ControlBlueprintField.ACTIVITY]: {
            steps: [
                { use: OptionalValidator },
                { use: StringLengthValidator, args: [{ min: 1, max: 30000 }] },
            ],
            fieldName: 'Control Activity',
        },
        [ControlBlueprintField.NOTES]: {
            steps: [
                { use: OptionalValidator },
                { use: ValidatorArray(StringLengthValidator), args: [{ min: 1, max: 30000 }] },
            ],
            fieldName: 'Internal Notes',
        },
        [ControlBlueprintField.APPROVERS]: {
            steps: [
                { use: OptionalValidator },
                { use: ValidatorArray(EmailValidator) },
                { use: ValidatorArray(UserEmailValidator), args: ['checkEmailExists'] },
                {
                    use: DependsOnValidator,
                    args: [
                        [ControlBlueprintField.APPROVAL_DEADLINE],
                        ValidationErrorMessages.APPROVERS_DEPENDS_ON_APPROVAL_DEADLINE_AND_VICE_VERSA,
                    ],
                },
                {
                    use: DependsOnValidator,
                    args: [
                        [ControlBlueprintField.OWNERS],
                        ValidationErrorMessages.CONTROL_OWNER_REQUIRED,
                    ],
                },
                {
                    use: ValidatorArray(UserHasRolesValidator, true),
                    args: [
                        'getUser',
                        [
                            Role.ADMIN,
                            Role.WORKSPACE_ADMINISTRATOR,
                            Role.TECHGOV,
                            Role.CONTROL_MANAGER,
                        ],
                        ValidationErrorMessages.APPROVERS_INVALID_EMAIL,
                    ],
                },
            ],
        },
        [ControlBlueprintField.APPROVAL_DEADLINE]: {
            steps: [
                { use: OptionalValidator },
                {
                    use: DependsOnValidator,
                    args: [
                        [ControlBlueprintField.APPROVERS],
                        ValidationErrorMessages.APPROVERS_DEPENDS_ON_APPROVAL_DEADLINE_AND_VICE_VERSA,
                    ],
                },
                {
                    use: DependsOnValidator,
                    args: [
                        [ControlBlueprintField.OWNERS],
                        ValidationErrorMessages.CONTROL_OWNER_REQUIRED,
                    ],
                },
                { use: DateValidator },
                { use: ControlApprovalDeadline },
            ],
        },
    };

    constructor(
        private readonly controlService: ControlService,
        private readonly userService: UsersCoreService,
    ) {
        super({
            checkEmailExists: async (email: string) => {
                try {
                    return await this.userService
                        .getUserByEmailNoFail(email)
                        .then((result: User | null) => Boolean(result));
                } catch (error) {
                    return false;
                }
            },
            getUser: async (email: string) => {
                try {
                    return await this.userService.getUserByEmailNoFail(email);
                } catch (error) {
                    return null;
                }
            },
            validateCodeUniqueness: (code: string) => {
                // if exist lets negate it
                return this.records.has(code);
            },
            validateNameUniqueness: (name: string) => {
                // if exist lets negate it
                return this.records.has(name);
            },
        });
    }

    addFrameworkRequirementsChain(
        isWorkspaceEnabled: boolean,
        workspaceName: string,
        frameworkFieldsNames: Map<string, string>,
        frameworkRequirementsMap: Map<string, EnumPropertyOption[]>,
    ): void {
        for (const [field, frameworkName] of frameworkFieldsNames.entries()) {
            const requirementOptions = frameworkRequirementsMap.get(field) ?? [];
            const customEntitlementMessage = isWorkspaceEnabled
                ? `%s not found in ${frameworkName} framework within ${workspaceName} workspace.`
                : `%s requirement was not found in ${frameworkName} framework.`;
            const validatorConfig = {
                validOptions: requirementOptions,
                caseSensitive: false,
                customMessage: `${customEntitlementMessage} This requirement will not be mapped to this Control.`,
            };
            this.chains[field] = {
                steps: [
                    { use: OptionalValidator },
                    {
                        use: ValidatorArray(OptionsValidator),
                        args: [validatorConfig],
                    },
                ],
            };
        }
    }

    clearRecords(): void {
        this.records.clear();
    }

    addRecord(record: { code?: string; name?: string }): void {
        this.addRecordXProperty(record, 'code');
        this.addRecordXProperty(record, 'name');
    }

    private addRecordXProperty(record: { [property: string]: string }, property: string): void {
        if (!record[property]) {
            return;
        }
        if (this.records.has(record[property])) {
            return;
        }
        this.records.add(record[property]);
    }

    getRecords(): unknown[] {
        return Array.from(this.records.values());
    }

    public setWorkspaceId(workspaceId: number): void {
        this.appendDependencies({
            checkControlCodeExists: (code: string) =>
                this.controlService
                    .isAvailable({ code, name: '', workspaceId })
                    .then(result => !result.available),
            getControlsByCode: (code: string, name: string) =>
                this.controlService.findControlsByCodeAndName(code, name, workspaceId),
            checkControlNameExists: async (name: string) =>
                this.controlService
                    .isAvailable({ name, code: undefined, workspaceId })
                    .then(result => !result.available),
        });
    }
}
