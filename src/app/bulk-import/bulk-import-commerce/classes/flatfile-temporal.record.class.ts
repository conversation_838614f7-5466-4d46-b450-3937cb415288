import { CellValueUnion, RecordData, ValidationMessage, ValidationType } from '@flatfile/api/api';
import { JsonlRecord } from '@flatfile/api/v2/records/types';
import { FlatFileSdk } from 'app/bulk-import/bulk-import-commerce/services/flat-file.sdk';
import { isEmpty, pick } from 'lodash';

export class FlatfileTemporalRecord {
    private sdk: FlatFileSdk;
    private sheetId: string;
    private recordData: RecordData;
    private requestForceUpdate = false;

    static FRAMEWORK_FIELD_PREFIX = 'framework-';
    private CUSTOM_FIELD_PREFIX = 'customField_';

    constructor(
        public raw: JsonlRecord,
        private readonly blueprintFields: Record<string, string>,
    ) {
        const selectedFields = pick(this.raw, Object.values(blueprintFields));
        const customFields = this.getCustomFields();
        const frameworkFields = this.getFrameworkFields();
        const mergedFields = { ...selectedFields, ...customFields, ...frameworkFields };
        const recordData = this.toRecordData(mergedFields);
        this.recordData = recordData;
    }

    get data() {
        return this.recordData;
    }

    /**
     * Setter to clean up messages when the validation result has no messages from any of the properties
     */
    set data(recordData: RecordData) {
        this.recordData = recordData;
    }

    get didRequestUpdate() {
        return this.requestForceUpdate;
    }

    /**
     * Setter to request an update on the record even if there are no messages
     */
    setRequestForceUpdate(requestUpdate: boolean) {
        this.requestForceUpdate = requestUpdate;
    }

    get obj() {
        const selectedFields = pick(this.raw, Object.values(this.blueprintFields));
        const customFields = this.getCustomFields();
        const frameworkFields = this.getFrameworkFields();
        const frameworkRecords = this.getFrameworkRecords(frameworkFields);
        const mergedFields = { ...selectedFields, ...customFields, ...frameworkRecords };

        if (!this.recordData) {
            this.recordData = this.toRecordData(mergedFields);
        }

        // Return updated values from recordData, falling back to raw values
        const result = { ...mergedFields };
        for (const [key, value] of Object.entries(this.recordData)) {
            if (mergedFields.hasOwnProperty(key)) {
                result[key] = value.value;
            }
        }
        return result;
    }

    getValue(field: string): CellValueUnion | undefined {
        if (this.recordData && this.recordData[field]) {
            const fieldData = this.recordData[field];
            return fieldData.value;
        }
        return this.raw[field];
    }

    set(field: string, value?: CellValueUnion) {
        if (!this.recordData[field]) {
            this.addNewField(field, this.raw[field]);
        }

        this.raw[field] = value;
        this.recordData[field].value = value;
    }

    getData(field: string) {
        return this.recordData[field];
    }

    setSheetId(sheetId: string) {
        this.sheetId = sheetId;
    }

    addMessage(message: ValidationMessage) {
        if (!message.field) {
            throw new Error('Validation message must have a field');
        }

        if (!this.recordData[message.field]) {
            const value = this.raw[message.field];
            this.addNewField(message.field, value);
        }

        this.recordData[message.field].messages?.push(message);
    }

    getErrorMessages(field: string, index?: number): string[] {
        if (!this.recordData[field]?.messages) {
            return [];
        }

        return this.recordData[field].messages
            .filter(msg => msg.type === ValidationType.Error)
            .filter(msg => index === undefined || msg.path === `$.value[${index}]`)
            .map(msg => msg.message)
            .filter(msg => msg !== undefined);
    }

    setSdk(sdk: FlatFileSdk) {
        this.sdk = sdk;
    }

    private addNewField(field: string, value: CellValueUnion) {
        this.recordData[field] = { value, messages: [] };
    }

    toRecordData(obj: Record<string, unknown>): RecordData {
        const result: Record<string, { value: unknown; messages: unknown[] }> = {};
        for (const [key, value] of Object.entries(obj)) {
            result[key] = { value, messages: [] };
        }
        return result as RecordData;
    }

    getCustomFields(): Record<string, unknown> {
        const customFieldRecords: Record<string, unknown> = {};
        const customFieldKeys = Object.keys(this.raw).filter(key =>
            key.includes(this.CUSTOM_FIELD_PREFIX),
        );
        for (const customFieldKey of customFieldKeys) {
            customFieldRecords[customFieldKey] = this.raw[customFieldKey];
        }
        return customFieldRecords;
    }

    getFrameworkFields(): string[] {
        return Object.keys(this.raw).filter(key =>
            key.includes(FlatfileTemporalRecord.FRAMEWORK_FIELD_PREFIX),
        );
    }

    getFrameworkRecords(frameworkKeys: string[]): RecordData {
        const frameworkRecords: RecordData = {};
        for (const frameworkKey of frameworkKeys) {
            frameworkRecords[frameworkKey] = this.raw[frameworkKey];
        }
        return frameworkRecords;
    }

    async commit(forceUpdateFields?: string[]) {
        // Remove fields that have no messages, but keep forceUpdateFields
        for (const [key, value] of Object.entries(this.recordData)) {
            if (value.messages?.length === 0 && !forceUpdateFields?.includes(key)) {
                delete this.recordData[key];
            }
        }

        if (isEmpty(this.recordData)) {
            return;
        }

        return this.sdk.updateRecords(this.sheetId, [
            {
                id: String(this.raw.__k),
                values: this.recordData,
            },
        ]);
    }
}
