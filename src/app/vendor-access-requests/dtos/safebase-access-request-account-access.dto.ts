import { ApiProperty } from '@nestjs/swagger';
import { VendorTrustCenterAccessRequestStatus } from 'app/users/vendors/enums/vendor-trust-center-access-request-status.enum';
import { TokenDto } from 'app/vendor-access-requests/dtos/safebase-access-request-token.dto';
import { Type } from 'class-transformer';
import { IsIn, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Trim } from 'commons/decorators/trim.decorator';
import { getValues } from 'commons/helpers/enum.helper';

export class AccountAccessDto {
    @ApiProperty({
        type: 'string',
        description: 'Account name',
        example: 'Test Account',
    })
    @IsString()
    @IsOptional()
    @Trim()
    name: string;

    @ApiProperty({
        type: 'string',
        description: 'Access status',
        example: 'granted',
        enum: VendorTrustCenterAccessRequestStatus,
    })
    @IsIn(getValues(VendorTrustCenterAccessRequestStatus))
    status: VendorTrustCenterAccessRequestStatus;

    @ApiProperty({
        description: 'Access token (only if granted)',
        type: TokenDto,
        required: false,
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => TokenDto)
    token?: TokenDto;
}
