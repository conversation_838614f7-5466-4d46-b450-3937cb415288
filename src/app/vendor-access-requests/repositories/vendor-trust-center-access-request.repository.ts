import { VendorTrustCenterAccessRequest } from 'app/users/vendors/entities/vendor-trust-center-access-request.entity';
import { BaseRepository } from 'commons/repositories/base.repository';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';

@CustomRepository(VendorTrustCenterAccessRequest)
export class VendorTrustCenterAccessRequestRepository extends BaseRepository<VendorTrustCenterAccessRequest> {
    async findAccessRequestByVendorIdAndRequestedId(
        email: string,
        vendorId: number,
    ): Promise<{ token?: string | null; expiresAt: Date; status: string } | null> {
        const accessRequest = await this.createQueryBuilder('AccessRequest')
            .select(['AccessRequest.token', 'AccessRequest.expiresAt', 'AccessRequest.status'])
            .where('AccessRequest.requested_by = :email', { email })
            .andWhere('AccessRequest.fk_vendor_id = :vendorId', { vendorId })
            .orderBy('AccessRequest.created_at', 'DESC')
            .getOne();

        if (!accessRequest) {
            return null;
        }

        return {
            token: accessRequest?.token,
            expiresAt: accessRequest.expiresAt,
            status: accessRequest.status,
        };
    }

    async softDeleteAccessRequestByVendorAndEmail(email: string, vendorId: number): Promise<void> {
        await this.createQueryBuilder()
            .softDelete()
            .where('requested_by = :email', { email })
            .andWhere('fk_vendor_id = :vendorId', { vendorId })
            .execute();
    }
}
