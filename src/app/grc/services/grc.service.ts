import {
    ApprovalStatus,
    CheckR<PERSON>ultStatus,
    CheckStatus,
    <PERSON>rror<PERSON><PERSON>,
    FrameworkTag,
    RequirementIndexCategory,
    SortDir,
    SortType,
    TestSource,
} from '@drata/enums';
import {
    BadRequestException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ControlEvidenceAddedEvent } from 'app/analytics/observables/events/control-evidence-added.event';
import { ControlFilteredByEvidenceEvent } from 'app/analytics/observables/events/control-filtered-by-evidence.event';
import { ControlFilteredByPolicyEvent } from 'app/analytics/observables/events/control-filtered-by-policy-event';
import { ControlOwnerAddedEvent } from 'app/analytics/observables/events/control-owner-added';
import { ControlOwnerDeletedEvent } from 'app/analytics/observables/events/control-owner-deleted';
import { ControlTemplateAppliedEvent } from 'app/analytics/observables/events/control-template-applied.event';
import { ControlTestAddedEvent } from 'app/analytics/observables/events/control-test-added-event';
import { ControlTestDeletedEvent } from 'app/analytics/observables/events/control-test-deleted-event';
import { CreateControlEvent } from 'app/analytics/observables/events/create-control-event';
import { EditControlInfoEvent } from 'app/analytics/observables/events/edit-control-info';
import { EvidenceAssociatedToControlEvent } from 'app/analytics/observables/events/evidence-associated-to-control';
import { EvidenceUnassociatedToControlEvent } from 'app/analytics/observables/events/evidence-unassociated-to-control';
import { PolicyAssociatedToControlEvent } from 'app/analytics/observables/events/policy-associated-to-control-event';
import { PolicyUnassociatedToControlEvent } from 'app/analytics/observables/events/policy-unassocciated-to-control-event';
import { RequirementAssociatedToControlEvent } from 'app/analytics/observables/events/requirement-associated-to-control';
import { RequirementUnassociatedToControlEvent } from 'app/analytics/observables/events/requirement-unassociated-to-control';
import { UploadExternalEvidenceEvent } from 'app/analytics/observables/events/upload-external-evidence-event';
import { isTestEvidenceFlowFeatureFlagActive } from 'app/autopilot/helper/test-evidence-workflow.helper';
import { ConnectionFeatureClientTypes } from 'app/companies/connections/maps/connection-feature-client-types.map';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Company } from 'app/companies/entities/company.entity';
import { FileNames } from 'app/companies/helpers/file-names.helper';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { AuditDatesType } from 'app/companies/types/audit-dates.type';
import { ControlService } from 'app/control/control.service';
import { isCustomFieldsEnabled } from 'app/custom-fields/helpers/custom-fields.helper';
import { CustomFieldsSubmissionsCSVService } from 'app/custom-fields/services/custom-fields-submission-csv.service';
import { EvidenceType } from 'app/custom-workflows/custom-workflows-common/enums/evidence-type.enum';
import { ControlAttachedToEvidenceWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-attached-to-evidence-trigger.workflow.event';
import { ControlAttachedToPolicyWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-attached-to-policy-trigger.workflow.event';
import { ControlDetailsUpdatedWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-details-updated-trigger.worklow';
import { ControlMappedToRequirementWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-mapped-to-requirement-trigger.workflow.event';
import { ControlMappedToTestWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-mapped-to-test-trigger.workflow.event';
import { ControlOwnerUpdatedWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-owner-updated-trigger.worklow';
import { ControlUnattachedFromPolicyWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-unattached-from-policy-trigger.workflow.event';
import { ControlUnmappedFromRequirementWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-unmapped-from-requirement-trigger.workflow.event';
import { ControlUnmappedFromTestWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-unmapped-from-test-trigger.workflow.event';
import { AuditHubLinkedLibraryDocumentVersionsEvidenceEvent } from 'app/customer-request/observables/events/audit-hub-uploaded-evidence/audit-hub-linked-library-document-versions-evidence-event';
import { AuditHubLinkedPolicyVersionsEvidenceEvent } from 'app/customer-request/observables/events/audit-hub-uploaded-evidence/audit-hub-linked-policy-versions-evidence-event';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { TriggerEventEvidenceData } from 'app/document-library/evidence-library/types/trigger-event-evidence-data.type';
import { DocumentLibraryCoreService } from 'app/document-library/services/document-library-core.service';
import { EventsService } from 'app/events/events.service';
import { Requirement } from 'app/frameworks/entities/requirement.entity';
import { reportDataTransformHelper } from 'app/frameworks/helpers/framework.helper';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { RequirementIndexViewRepository } from 'app/frameworks/repositories/requirement-index-view.repository';
import { RequirementRepository } from 'app/frameworks/repositories/requirement.repository';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { FrameworkTagType } from 'app/frameworks/types/framework-tag.type';
import { FrameworkWithSection } from 'app/frameworks/types/frameworks-with-sections.enum';
import { ISO27001AnnexAArray } from 'app/frameworks/types/iso-27001-annex-a-array';
import {
    ISO270012022AnnexAArray,
    ISO270012022ISMSArray,
} from 'app/frameworks/types/requirement-categories/iso27001-2022-category.enum';
import { RequirementDetailsReadyType } from 'app/frameworks/types/requirement-details-ready.type';
import { PaginationFacetAccumulator } from 'app/grc/classes/pagination-facet-accumulator.class';
import { ControlCustomTasksRequestDto } from 'app/grc/dtos/control-custom-tasks-request.dto';
import { ControlEvidenceRequestDto } from 'app/grc/dtos/control-evidence-request.dto';
import { ControlOwnerBulkRequestDto } from 'app/grc/dtos/control-owner-bulk-request.dto';
import { ControlOwnerRequestDto } from 'app/grc/dtos/control-owner-request.dto';
import { ControlOwnersRequestDto } from 'app/grc/dtos/control-owners-request.dto';
import { ControlPoliciesRequestDto } from 'app/grc/dtos/control-policies-request.dto';
import { ControlPolicyRequestDto } from 'app/grc/dtos/control-policy-request.dto';
import { ControlPolicyVersionRequestDto } from 'app/grc/dtos/control-policy-version.request.dto';
import { ControlReportRequestDto } from 'app/grc/dtos/control-report-request.dto';
import { ControlReportsRequestDto } from 'app/grc/dtos/control-reports-request.dto';
import { ControlRequirementRequestDto } from 'app/grc/dtos/control-requirement-request.dto';
import { ControlRiskBulkRequestDto } from 'app/grc/dtos/control-risk-bulk-request.dto';
import { ControlRisksRequestDto } from 'app/grc/dtos/control-risks-request.dto';
import { ControlTestBulkRequestDto } from 'app/grc/dtos/control-test-bulk-request.dto';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { CreateControlRequestDto } from 'app/grc/dtos/create-control-request.dto';
import { MappedRequirementsRequestDto } from 'app/grc/dtos/mapped-requirements-request.dto';
import { ControlEvidences } from 'app/grc/entities/control-evidences.type';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { ControlWorkspaceGroup } from 'app/grc/entities/control-workspace-group.entity';
import { Control } from 'app/grc/entities/control.entity';
import { CustomTask } from 'app/grc/entities/custom-task.entity';
import { ExternalEvidence } from 'app/grc/entities/external-evidence.entity';
import { ControlEvidenceSourceType } from 'app/grc/enums/control-evidence-source-type.enum';
import { ApprovalFacet } from 'app/grc/facets/control/approval-facet.class';
import { ApproverFacet } from 'app/grc/facets/control/approver-facet.class';
import { ControlFacet } from 'app/grc/facets/control/control-facet.class';
import { IsArchivedFacet } from 'app/grc/facets/control/is-archived-facet.class';
import { IsReadyFacet } from 'app/grc/facets/control/is-ready-facet.class';
import { OwnerFacet } from 'app/grc/facets/control/owner-facet.class';
import { SearchFacet } from 'app/grc/facets/control/search-facet.class';
import { TaskOwnerFacet } from 'app/grc/facets/control/task-owner-facet.class';
import { TestFacet } from 'app/grc/facets/control/test-facet.class';
import { TicketFacet } from 'app/grc/facets/control/ticket-facet.class';
import { WorskpaceFacet } from 'app/grc/facets/control/workspace-facet.class';
import { FacetRunner } from 'app/grc/facets/facet-runner.class';
import { PaginatedRequirementsSortedByStatusFacet } from 'app/grc/facets/requirement/paginated-requirements-sorted-by-status-facet.class';
import { PaginatedRequirementsWithControlsFacet } from 'app/grc/facets/requirement/paginated-requirements-with-controls-facet.class';
import { RequirementIndexFacet } from 'app/grc/facets/requirement/requirement-index-facet.class';
import {
    getControlTypeFromControl,
    getEvidenceUploadSourcetypeFromRequest,
} from 'app/grc/helpers/control-evidence-upload.helper';
import { sortControls } from 'app/grc/helpers/control-sort.helper';
import { mergeControlTestInstances } from 'app/grc/helpers/control-test-evidence.helper';
import { IControlIssuesStats } from 'app/grc/interfaces/control-issues-stats.interface';
import { ControlsReindexEvent } from 'app/grc/observables/events/controls-reindex.event';
import { MappedPoliciesResetOnControlEvent } from 'app/grc/observables/events/mapped-policies-reset-on-control.event';
import { MappedRequirementsResetOnControlEvent } from 'app/grc/observables/events/mapped-requirements-reset-on-control.event';
import { MappedTestsResetOnControlEvent } from 'app/grc/observables/events/mapped-tests-reset-on-control.event';
import { RiskMappedToControlEvent } from 'app/grc/observables/events/risk-mapped-to-control.event';
import { ControlFlagsViewRepository } from 'app/grc/repositories/control-flags-view.repository';
import { ControlIsReadyViewRepository } from 'app/grc/repositories/control-is-ready-view.repository';
import { ControlTicketViewRepository } from 'app/grc/repositories/control-ticket-view.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ExternalEvidenceRepository } from 'app/grc/repositories/external-evidence.repository';
import { ControlApprovalsService } from 'app/grc/services/control-approvals.service';
import { ExternalEvidenceService } from 'app/grc/services/external-evidence.service';
import {
    ControlCompare,
    ControlComparePolicy,
    ControlCompareTest,
} from 'app/grc/types/control-compare.type';
import { ControlFlags } from 'app/grc/types/control-flags.type';
import { ControlListWithFlags } from 'app/grc/types/control-list.type';
import { ControlMappedRequirementsRequestType } from 'app/grc/types/control-mapped-requirements-request.type';
import { ControlModifyRequestType } from 'app/grc/types/control-modify-request.type';
import { ControlOwnersPutRequestType } from 'app/grc/types/control-owners-put-request.type';
import { ControlOwnersRequestType } from 'app/grc/types/control-owners-request.type';
import { ControlRequirements } from 'app/grc/types/control-requirements.type';
import { ControlUserType } from 'app/grc/types/control-user.type';
import { ControlWithEvidence } from 'app/grc/types/control-with-evidence.type';
import { EvidenceUnion } from 'app/grc/types/evidence-union.type';
import { ControlReadinessEvent } from 'app/grc/workflow-services/events/control-readiness.workflow.event';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceCheckType } from 'app/monitors/entities/monitor-instance-check-type.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { IndexMonitorResultControlsAndFrameworksEvent } from 'app/monitors/observables/events/index-monitor-result-controls-and-frameworks.event';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { MonitorsService } from 'app/monitors/services/monitors.service';
import { CreateControlNoteRequestDto } from 'app/notes/dtos/create-control-note-request.dto';
import { ControlNotesOrchestrationService } from 'app/notes/services/control-notes-orchestration.service';
import { NotificationControl } from 'app/notifications/entities/notification-control.entity';
import { Risk } from 'app/risk-management/entities/risk.entity';
import { RiskRepository } from 'app/risk-management/repositories/risk.repository';
import { scannable } from 'app/scanner-uploader/helpers/scannable-file-validator.helper';
import { ScannerUploaderService } from 'app/scanner-uploader/services/scanner-uploader.service';
import { SecurityReportOrchestrationService } from 'app/security-report/security-report-orchestration.service';
import { TrustCenterMonitoringControl } from 'app/trust-center/entities/monitoring-controls.entity';
import { TrustCenterMonitoringControlRepository } from 'app/trust-center/repositories/monitoring-controls.repository';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyStatus } from 'app/users/policies/enums/policy-status.enum';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { PolicyForEvent } from 'app/users/policies/types/policy-event.type';
import { PolicyWithControlWorkspaceGroup } from 'app/users/policies/types/policy-with-control-workspace-group.type';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { grcEvidenceDownloadWorkflowV1 } from 'app/worker/workflows';
import { getAuditorFrameworkTimePeriodForEvidence } from 'auditors/helpers/auditor.helper';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheBusterWithPrefix, CacheWithPrefix } from 'cache/cache.decorator';
import { isURL } from 'class-validator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { ConnectionFeature } from 'commons/enums/connection-feature.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { LibraryDocumentVersionType } from 'commons/enums/library-document-version-type.enum';
import { RenewalScheduleType } from 'commons/enums/renewal-schedule.enum';
import { ReportVisibilityType } from 'commons/enums/report-visibility-type.enum';
import { ScannerUploaderStatus } from 'commons/enums/scanner-uploader/scanner-uploader-status.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Action } from 'commons/enums/users/action.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { asyncForEach } from 'commons/helpers/array.helper';
import {
    getCSVCustomStream,
    getCSVStream,
    getStreamsFromFiles,
    mapFilesToPrefix,
} from 'commons/helpers/buffer.helper';
import { formatCustomControlReportData } from 'commons/helpers/control.helper';
import { shouldEmitEvent } from 'commons/helpers/event.helper';
import { sanitizeFileName } from 'commons/helpers/file.helper';
import { getAutopilotTaskTypesByProviderType } from 'commons/helpers/monitor.helper';
import { isPositiveInteger } from 'commons/helpers/number.helper';
import { getStreamsFromPolicyVersionsFiles } from 'commons/helpers/policy.helper';
import {
    accountConnectionProviderTypes,
    filterControlTests,
    getProductId,
    hasAssociatedProduct,
    sanitizeProduct,
} from 'commons/helpers/products.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { checkIsDocumentOrImage, getFileExtension } from 'commons/helpers/upload.helper';
import { hasRole, hasUserPermission } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { CursorPage } from 'commons/types/cursor-page.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { UploaderPayloadType } from 'dependencies/uploader/types/uploader-payload.type';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import {
    clone,
    cloneDeep,
    concat,
    differenceBy,
    get,
    includes,
    isEmpty,
    isNil,
    isUndefined,
    pick,
    replace,
} from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { ControlTestTemplate } from 'site-admin/entities/control-test-template.entity';
import { MonitorTemplate } from 'site-admin/entities/monitor-template.entity';
import { RequirementTemplate } from 'site-admin/entities/requirement-template.entity';
import { ControlTemplateService } from 'template/services/control-template.service';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { In, IsNull, Not, Repository, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class GrcService extends AppService {
    private unlimitedRequest = { page: 1, limit: null, excludeIds: null };

    constructor(
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly monitorsService: MonitorsService,
        private readonly usersCoreService: UsersCoreService,
        private readonly externalEvidenceService: ExternalEvidenceService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly documentLibraryCoreService: DocumentLibraryCoreService,
        private readonly eventsService: EventsService,
        private readonly scannerUploaderService: ScannerUploaderService,
        @InjectRepository(AuditorFrameworkRepository)
        private readonly auditorFrameworkRepository: AuditorFrameworkRepository,
        private readonly controlTemplateService: ControlTemplateService,
        private readonly controlService: ControlService,
        private readonly controlApprovalsService: ControlApprovalsService,
        private readonly customFieldsSubmissionsCSVService: CustomFieldsSubmissionsCSVService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly controlNotesOrchestrationService: ControlNotesOrchestrationService,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly securityReportOrchestrationService: SecurityReportOrchestrationService,
    ) {
        super();
    }

    async resolveSecurityReport(account: Account) {
        const isFeatureFlagEnabled = await this.isSecurityReportPerformanceEnabled(account);

        return isFeatureFlagEnabled
            ? this.securityReportOrchestrationService.getSecurityReportV2(account)
            : this.securityReportOrchestrationService.getSecurityReport(account);
    }

    /**
     * @deprecated Use SecurityReportOrchestrationService.getSecurityReport
     */
    async getSecurityReport(account: Account): Promise<Array<Control>> {
        const company = await this.companyRepository.findOneByOrFail({
            accountId: account.id,
        });

        const primaryProduct = await this.workspacesCoreService.getPrimaryProduct();
        account.setCurrentProduct(primaryProduct);

        const requestDto = new ControlsRequestDto();
        requestDto.page = 1;
        requestDto.limit = 1000;
        requestDto.isArchived = false;

        if (company.securityReportVisibility === ReportVisibilityType.MONITORING) {
            requestDto.hasEvidence = true;
        }

        if (company.securityReportVisibility === ReportVisibilityType.PASSING) {
            requestDto.hasPassingTest = true;
        }

        const facetRunner = new FacetRunner();
        facetRunner.addFacetsByClass([RequirementIndexFacet], account, this.index, requestDto);

        const { include: controlIds } = await facetRunner.run();

        const controls = await this.controlRepository.getControlsForSecurityReport(
            controlIds,
            config.get('securityReport.checkStatusExcludeList'),
        );

        this.editSection(controls);

        const companyName = account.companyName;

        // Simplify logic operations for making more CPU efficient on nested loop operations
        for (const control of controls) {
            control.description = control.description?.replace(/%s/g, companyName) ?? '';

            for (const test of control.controlTestInstances ?? []) {
                test.description = test.description?.replace(/%s/g, companyName) ?? '';

                for (const monitor of test.monitorInstances ?? []) {
                    monitor.evidenceCollectionDescription =
                        monitor.evidenceCollectionDescription?.replace(/%s/g, companyName) ?? '';
                }
            }
        }

        return sortControls(controls);
    }

    /**
     * @deprecated Use ControlOrchestrationService.findControlForPrimaryProductByCode
     */
    async findControlForPrimaryProductByCode(code: string): Promise<Control> {
        const primaryProductId = await this.workspacesCoreService.getPrimaryProductId();

        return this.controlRepository
            .createQueryBuilder('Control')
            .innerJoinAndSelect('Control.products', 'Product', 'Product.id = :primaryProductId', {
                primaryProductId,
            })
            .where('Control.code = :code', { code })
            .getOneOrFail();
    }

    /**
     *
     * @param account
     * @param user
     * @param id
     * @param requestDto
     * @returns
     */
    @CacheBusterWithPrefix<ControlEvidences>({
        stores: [
            Caches.ACCOUNT,
            Caches.USER_BY_ENTRY_ID,
            Caches.LIST_CONTROLS,
            Caches.FRAMEWORK,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async putEvidence(
        account: Account,
        user: User,
        id: number,
        requestDto: ControlReportRequestDto,
    ): Promise<ControlEvidences> {
        const { reportIds, requestId } = requestDto;

        const control = await this.controlRepository.findOneOrFail({
            where: {
                id,
                enabledAt: Not(IsNull()),
            },
            relations: ['owners'],
        });
        const before = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        if (isEmpty(reportIds)) {
            return { control, evidences: [] };
        }

        const controlsMap = await this.documentLibraryCoreService.linkEvidenceToControl(
            account,
            user,
            control,
            reportIds,
        );

        let linkedLibraryDocumentsEvidence = controlsMap.map(
            controlMap => controlMap.libraryDocument,
        );

        const after = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        control.description = control.description?.replace(/%s/g, account.companyName);
        linkedLibraryDocumentsEvidence = linkedLibraryDocumentsEvidence.map(ld => {
            ld.libraryDocumentControlsMap = ld.libraryDocumentControlsMap.map(ldc => {
                if (ldc.control?.description) {
                    ldc.control.description = ldc.control.description.replace(
                        /%s/g,
                        account.companyName,
                    );
                }
                return ldc;
            });
            return ld;
        });

        this._eventBus.publish(
            new EvidenceAssociatedToControlEvent(
                account,
                user,
                linkedLibraryDocumentsEvidence,
                control,
                before.isReady,
                after.isReady,
            ),
        );

        this._eventBus.publish(
            new ControlEvidenceAddedEvent(
                account,
                user,
                control,
                getControlTypeFromControl(control),
                getEvidenceUploadSourcetypeFromRequest(requestId),
                ControlEvidenceSourceType.EVIDENCE_LIBRARY,
            ),
        );

        if (!isNil(requestId)) {
            const libraryDocumentVersions = linkedLibraryDocumentsEvidence.map(libraryDocument => {
                const version = libraryDocument.currentVersion;
                version.libraryDocument = new LibraryDocument();
                version.libraryDocument.id = libraryDocument.id;
                version.libraryDocument.name = libraryDocument.name;
                return version;
            });
            this._eventBus.publish(
                new AuditHubLinkedLibraryDocumentVersionsEvidenceEvent(
                    account,
                    user,
                    requestId,
                    control.code,
                    control.name,
                    libraryDocumentVersions,
                ),
            );
        }

        return { control, evidences: linkedLibraryDocumentsEvidence };
    }

    @CacheBusterWithPrefix<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.USER_BY_ENTRY_ID,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async deleteReports(
        account: Account,
        user: User,
        id: number,
        requestDto: ControlReportRequestDto,
    ): Promise<void> {
        // TODO: Remove feature flag validation with this ticket:https://drata.atlassian.net/browse/ENG-58940
        const testEvidenceFeatureFlag = await this.featureFlagService.evaluateAs(
            {
                name: FeatureFlag.RELEASE_MERGE_TEST_EVIDENCE_INTO_EL,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            account,
        );

        const baseRelations = !testEvidenceFeatureFlag
            ? ['libraryDocuments']
            : [
                  'libraryDocuments',
                  'libraryDocuments.libraryDocument',
                  'libraryDocuments.libraryDocument.libraryDocumentVersions',
              ];

        const control = await this.controlRepository.findOneOrFail({
            where: { id, enabledAt: Not(IsNull()) },
            relations: baseRelations,
        });

        const before = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        const reportIdsToUnlink = control.libraryDocuments
            .filter(report => {
                const isReportLinked =
                    requestDto.reportIds.indexOf(report.libraryDocument.id) !== -1;

                if (!testEvidenceFeatureFlag) {
                    return isReportLinked;
                }

                const currentType = get(report, 'libraryDocument.currentVersion.type');
                const isTestEvidence =
                    !isNil(currentType) && currentType === LibraryDocumentVersionType.TEST_RESULT;
                if (isReportLinked && isTestEvidence) {
                    throw new BadRequestException(
                        'An evidence generated by Drata test cannot be unlinked',
                    );
                }
                return isReportLinked;
            })
            .map(report => report.libraryDocument.id);

        const unlinkedReports = await this.documentLibraryCoreService.unlinkEvidenceToControl(
            account,
            control,
            reportIdsToUnlink,
        );

        const after = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        this._eventBus.publish(
            new EvidenceUnassociatedToControlEvent(
                account,
                user,
                unlinkedReports,
                control,
                before.isReady,
                after.isReady,
            ),
        );
    }

    @CacheBusterWithPrefix<ControlRequirements>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async putRequirements(
        account: Account,
        user: User,
        id: number,
        requestDto: ControlRequirementRequestDto,
    ): Promise<ControlRequirements> {
        const control = await this.controlRepository.findOneOrFail({
            where: { id, enabledAt: Not(IsNull()) },
            relations: [
                'requirements',
                'requirements.requirementIndex',
                'requirements.requirementIndex.framework',
            ],
        });
        control.description = control.description.replace(/%s/g, account.companyName);
        const requestedRequirements = await this.requirementRepository.getRequirementsById(
            requestDto.requirementIds,
        );

        const existingControlRequirementIds = control.requirements.map(
            requirement => requirement.id,
        );

        let newRequirements = requestedRequirements.filter(
            requirement => existingControlRequirementIds.indexOf(requirement.id) === -1,
        );

        if (!isEmpty(newRequirements)) {
            control.requirements.push(...newRequirements);

            // if control has requirements that are in scope, control should be in scope
            if (
                !isEmpty(control.requirements) &&
                !isEmpty(control.requirements.filter(requirement => isNil(requirement.archivedAt)))
            ) {
                control.archivedAt = null;
            }

            // if there is at least 1 requirement still mapped to the control, but all those requirements are OOS
            // then the control should be marked OOS as well
            if (
                !isEmpty(control.requirements) &&
                isEmpty(control.requirements.filter(requirement => isNil(requirement.archivedAt)))
            ) {
                control.archivedAt = new Date();
            }

            await this.controlRepository.save(control);

            newRequirements = newRequirements.map(requirement => {
                requirement.controls = requirement.controls.map(c => {
                    c.description = c.description.replace(/%s/g, account.companyName);
                    return c;
                });
                return requirement;
            });

            this._eventBus.publish(
                new RequirementAssociatedToControlEvent(account, user, newRequirements, control),
            );

            const workspaceId = account.getCurrentProduct()?.id;

            // Trigger reindex for control requirements update
            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }

            if (!isNil(workspaceId)) {
                const newRequirementsIds = newRequirements.map(requirement => requirement.id);

                // Trigger for Custom Workflow
                this._eventBus.publish(
                    new ControlMappedToRequirementWorkflowTriggerEvent(
                        account,
                        user,
                        newRequirementsIds,
                        control.id,
                        workspaceId,
                    ),
                );

                this._eventBus.publish(
                    new IndexMonitorResultControlsAndFrameworksEvent(account, workspaceId, null),
                );

                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            } else {
                this.logger.warn(
                    PolloMessage.msg('Unable to get workspace Id ')
                        .setIdentifier({ reason: 'Unable to get workspace Id' })
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext('putRequirements'),
                );
            }
        }

        return {
            controlId: control.id,
            requirementIds: newRequirements.map(nR => nR.id),
        };
    }

    @CacheBusterWithPrefix<ControlRequirements>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async deleteRequirements(
        account: Account,
        user: User,
        id: number,
        requestDto: ControlRequirementRequestDto,
    ): Promise<ControlRequirements> {
        const control = await this.controlRepository.getAllRequirements(id);
        control.description = control.description.replace(/%s/g, account.companyName);
        const existingRequirementsToDelete = control.requirements.filter(
            requirement => requestDto.requirementIds.indexOf(requirement.id) !== -1,
        );

        if (!isEmpty(existingRequirementsToDelete)) {
            control.requirements = control.requirements.filter(
                requirement => requestDto.requirementIds.indexOf(requirement.id) === -1,
            );

            // if there is at least 1 requirement still mapped to the control, but all those requirements are OOS
            // then the control should be marked OOS as well
            if (
                !isEmpty(control.requirements) &&
                isEmpty(control.requirements.filter(requirement => isNil(requirement.archivedAt)))
            ) {
                control.archivedAt = new Date();
            }

            // if control has requirements that are in scope, control should be in scope
            if (
                !isEmpty(control.requirements) &&
                !isEmpty(control.requirements.filter(requirement => isNil(requirement.archivedAt)))
            ) {
                control.archivedAt = null;
            }

            await this.controlRepository.save(control);

            this._eventBus.publish(
                new RequirementUnassociatedToControlEvent(
                    account,
                    user,
                    existingRequirementsToDelete,
                    control,
                ),
            );

            const existingRequirementsToDeleteIds = existingRequirementsToDelete.map(
                requirement => requirement.id,
            );

            const workspaceId = account.getCurrentProduct().id;

            // Trigger reindex for control requirements update
            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }

            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlUnmappedFromRequirementWorkflowTriggerEvent(
                    account,
                    user,
                    existingRequirementsToDeleteIds,
                    control.id,
                    workspaceId,
                ),
            );

            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }
        }

        return {
            controlId: control.id,
            requirementIds: existingRequirementsToDelete.map(nR => nR.id),
        };
    }

    @CacheBusterWithPrefix<Control>({
        stores: [
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async putPolicies(
        account: Account,
        user: User,
        controlId: number,
        requestDto: ControlPolicyRequestDto,
    ): Promise<Control> {
        const { policyIds, updateDisabledControls, requestId, filterByCurrentPublishedVersion } =
            requestDto;

        try {
            const [control, policies, readiness] = await Promise.all([
                filterByCurrentPublishedVersion
                    ? this.controlRepository.getControlWithCurrentPublishedPoliciesVersionDetailsOrFail(
                          controlId,
                          updateDisabledControls,
                      )
                    : this.controlRepository.getControlWithPoliciesDetailsOrFail(
                          controlId,
                          updateDisabledControls,
                      ),
                this.policiesCoreService.getPoliciesForEventByPolicyIds(policyIds),
                this.controlReadyRepository.findOneBy({
                    controlId,
                }),
            ]);

            this._eventBus.publish(
                new ControlEvidenceAddedEvent(
                    account,
                    user,
                    control,
                    getControlTypeFromControl(control),
                    getEvidenceUploadSourcetypeFromRequest(requestId),
                    ControlEvidenceSourceType.POLICY,
                ),
            );
            const omitPoliciesWithoutVersion = filterByCurrentPublishedVersion ?? false;
            return await this.associatePoliciesToControl(
                control,
                policies,
                account,
                user,
                readiness.isReady,
                omitPoliciesWithoutVersion,
                requestId,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Could not update Policy Control mappings`, account).setError(
                    error,
                ),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.putPoliciesBulk
     *
     * @param account
     * @param user
     * @param controlIds
     * @param requestDto
     */
    async putPoliciesBulk(
        account: Account,
        user: User,
        controlIds: number[],
        requestDto: ControlPolicyRequestDto,
    ): Promise<void> {
        const { policyIds, updateDisabledControls } = requestDto;

        try {
            const [controls, readiness, requestedPolicies] = await Promise.all([
                this.controlRepository.getControlsWithPoliciesDetails(
                    controlIds,
                    updateDisabledControls,
                ),
                this.controlReadyRepository.find({
                    where: {
                        controlId: In(controlIds),
                    },
                }),
                this.policiesCoreService.getPoliciesForEventByPolicyIds(policyIds),
            ]);

            controls.sort((a, b) => a.id - b.id);
            readiness.sort((a, b) => a.controlId - b.controlId);

            const controlPromises: Promise<Control>[] = [];
            for (let i = 0; i < controls.length; i++) {
                controlPromises.push(
                    this.associatePoliciesToControl(
                        controls[i],
                        requestedPolicies,
                        account,
                        user,
                        false,
                        readiness[i].isReady,
                    ),
                );
            }

            await Promise.allSettled(controlPromises);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Error while updating Policy Control mappings`, account)
                    .setIdentifier({
                        controls: controlIds,
                    })
                    .setError(error),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.associatePoliciesToControl
     *
     * Associates policies to controls and throws the corresponding events
     * @param control
     * @param policies
     * @param account
     * @param user
     * @param isBeforeReady
     * @param omitPoliciesWithoutVersion
     * @param customerRequestId
     * @returns Promise<Control>
     */
    private async associatePoliciesToControl(
        control: Control,
        policies: PolicyForEvent[],
        account: Account,
        user: User,
        isBeforeReady: boolean,
        omitPoliciesWithoutVersion: boolean,
        customerRequestId?: number,
    ): Promise<Control> {
        control.description = control.description.replace(/%s/g, account.companyName);

        const existingControlPolicyIds = control.policies.map(policy => policy.id);

        const newPolicies = policies.filter(
            policy => existingControlPolicyIds.indexOf(policy.id) === -1,
        );
        const newPolicyIds = newPolicies.map(policy => policy.id);

        /** Typeorm maps these by IDs, there is no need to pull the entire policy object
         * since the PolicyForEvent object overlaps enough
         * */
        control.policies.push(...(newPolicies as Policy[]));

        await this.controlRepository.save(control, { reload: false });
        const after = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        const currentProduct = await this.productRepository.getProductByControlId(control.id);

        // Trigger reindex for control policies update
        if (!isNil(currentProduct?.id)) {
            this._eventBus.publish(
                new ControlsReindexEvent(account, currentProduct.id, [control.id]),
            );
        }

        this._eventBus.publish(
            new PolicyAssociatedToControlEvent(
                account,
                user,
                newPolicies,
                control,
                isBeforeReady,
                after.isReady,
                currentProduct,
            ),
        );

        if (!isEmpty(newPolicyIds) && !isNil(currentProduct)) {
            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlAttachedToPolicyWorkflowTriggerEvent(
                    account,
                    user,
                    newPolicyIds,
                    control.id,
                    currentProduct.id,
                ),
            );
            try {
                this._eventBus.publish(
                    new ControlReadinessEvent(account, control.id, currentProduct.id, user),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Could not emit the control readiness audit event for control with id: ${control.id}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.associatePoliciesToControl.name)
                        .setError(error),
                );
            }

            this._eventBus.publish(
                new ControlsReindexEvent(account, currentProduct.id, [control.id]),
            );
        }

        if (!isNil(customerRequestId)) {
            const policyVersions = newPolicies
                .map(newPolicy =>
                    newPolicy.versions
                        .filter(policyVersion => policyVersion.current)
                        .map(policyVersion => policyVersion),
                )
                .flat();
            this._eventBus.publish(
                new AuditHubLinkedPolicyVersionsEvidenceEvent(
                    account,
                    user,
                    customerRequestId,
                    control.code,
                    control.name,
                    policyVersions,
                ),
            );
        }

        if (omitPoliciesWithoutVersion) {
            control.policies = control.policies.filter(policy => !isEmpty(policy.versions));
        }

        return control;
    }

    @CacheBusterWithPrefix<void>({
        stores: [
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async deletePolicies(
        account: Account,
        user: User,
        controlId: number,
        requestDto: ControlPolicyRequestDto,
    ): Promise<void> {
        const { policyIds, updateDisabledControls } = requestDto;

        try {
            const [control, readyness] = await Promise.all([
                this.controlRepository.getControlWithPoliciesDetailsOrFail(
                    controlId,
                    updateDisabledControls,
                ),
                this.controlReadyRepository.findOneBy({
                    controlId,
                }),
            ]);

            await this.unmapPoliciesFromControl(
                control,
                policyIds,
                account,
                user,
                readyness.isReady,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Could not delete the mapping between policy for control with id ${controlId}`,
                    account,
                ).setError(error),
            );
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.deletePoliciesBulk
     *
     * @param account
     * @param user
     * @param controlIds
     * @param requestDto
     */
    async deletePoliciesBulk(
        account: Account,
        user: User,
        controlIds: number[],
        requestDto: ControlPolicyRequestDto,
    ): Promise<void> {
        const { policyIds, updateDisabledControls } = requestDto;

        try {
            const [controls, readyness] = await Promise.all([
                this.controlRepository.getControlsWithPoliciesDetails(
                    controlIds,
                    updateDisabledControls,
                ),
                this.controlReadyRepository.find({
                    where: {
                        controlId: In(controlIds),
                    },
                }),
            ]);
            controls.sort((a, b) => a.id - b.id);
            readyness.sort((a, b) => a.controlId - b.controlId);

            const controlPromises: Promise<void>[] = [];
            for (let i = 0; i < controls.length; i++) {
                controlPromises.push(
                    this.unmapPoliciesFromControl(
                        controls[i],
                        policyIds,
                        account,
                        user,
                        readyness[i].isReady,
                    ),
                );
            }

            await Promise.allSettled(controlPromises);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Could not delete the mapping between policy for control with ids ${controlIds.join(
                        ',',
                    )}`,
                    account,
                ).setError(error),
            );
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.unmapPoliciesFromControl
     *
     * @param control
     * @param policyIds
     * @param account
     * @param user
     * @param beforeIsReady
     */
    private async unmapPoliciesFromControl(
        control: Control,
        policyIds: number[],
        account: Account,
        user: User,
        beforeIsReady: boolean,
    ): Promise<void> {
        control.description = control.description.replace(/%s/g, account.companyName);
        const deletedPolicyIds = control.policies
            .filter(policy => policyIds.indexOf(policy.id) !== -1)
            .map(p => p.id);

        const filteredPolicies = control.policies.filter(
            //filter out any policy who is
            policy => policyIds.indexOf(policy.id) === -1,
        );

        control.policies = filteredPolicies;

        await this.controlRepository.save(control);

        const [controlForEvent, existingPoliciesToDelete, after] = await Promise.all([
            this.controlRepository.getControlDataForEvent(control.id),
            this.policiesCoreService.getPoliciesWithCurrentPublishedVersionForEventByIds(
                deletedPolicyIds,
            ),
            this.controlReadyRepository.findOneBy({
                controlId: control.id,
            }),
        ]);

        this._eventBus.publish(
            new PolicyUnassociatedToControlEvent(
                account,
                user,
                existingPoliciesToDelete,
                controlForEvent,
                beforeIsReady,
                after.isReady,
            ),
        );

        // Sanity check we have what we need
        if (isEmpty(deletedPolicyIds) || isEmpty(control)) {
            return;
        }
        // Get workspace ID for reindexing
        const workspaceId = (await this.productRepository.getProductByControlId(control.id))?.id;

        if (isNil(workspaceId)) {
            return;
        }

        this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [control.id]));

        // Trigger for Custom Workflow
        this._eventBus.publish(
            new ControlUnattachedFromPolicyWorkflowTriggerEvent(
                account,
                user,
                deletedPolicyIds,
                control.id,
                workspaceId,
            ),
        );

        this._eventBus.publish(new ControlReadinessEvent(account, control.id, workspaceId, user));
    }

    async getControlRequirementsWithCursor(
        controlId: number,
        controlMappedRequirementsRequestType: ControlMappedRequirementsRequestType,
    ): Promise<CursorPage<Requirement>> {
        // Ensure control exists
        await this.controlRepository.findOneOrFail({
            where: { id: controlId },
            select: { id: true },
            loadEagerRelations: false,
        });

        return this.requirementRepository.getControlRequirementsWithCursor(
            controlId,
            controlMappedRequirementsRequestType,
        );
    }

    /**
     *
     * @param id
     * @returns
     */
    async getControlRequirements(
        id: number,
        requestDto: MappedRequirementsRequestDto,
    ): Promise<PaginationType<Requirement>> {
        return this.requirementRepository.getControlRequirements(id, requestDto);
    }

    /**
     * # getControlRequirementsWithControls
     *
     * Get the Requirements mapped to a given Control,
     * and get the Controls mapped to each Requirement.
     */
    async getControlRequirementsWithControls(
        account: Account,
        id: number,
        requestDto: MappedRequirementsRequestDto,
    ): Promise<PaginationType<RequirementDetailsReadyType>> {
        const paginationFacetAccumulator = new PaginationFacetAccumulator();

        const regularFacet = new PaginatedRequirementsWithControlsFacet(
            account,
            this.requirementRepository,
            requestDto,
            id,
            paginationFacetAccumulator,
        );
        const statusFacet = new PaginatedRequirementsSortedByStatusFacet(
            account,
            this.requirementRepository,
            requestDto,
            id,
            paginationFacetAccumulator,
        );

        const runner = new FacetRunner();

        runner.addFacets([regularFacet, statusFacet]);

        const { include: requirementIds } = await runner.run();

        const paginationData = paginationFacetAccumulator.getPaginationData();

        const requirementsReady =
            requirementIds.length > 0
                ? await this.requirementRepository.getRequirementsWithControls(
                      account,
                      paginationData.limit,
                      requirementIds,
                      null,
                  )
                : [];

        const requirements: RequirementDetailsReadyType[] = [];

        for (const requirementId of requirementIds) {
            const requirement = requirementsReady.find(reqReady => reqReady.id === requirementId);

            if (isNil(requirement)) {
                throw new NotFoundException('Could not find some of the requested control.');
            }

            requirements.push(requirement);
        }

        return {
            data: requirements,
            total: paginationData.total,
            page: paginationData.page,
            limit: paginationData.limit,
        };
    }

    /**
     * @deprecated Use ExternalEvidenceCoreService.getControlEvidence
     *
     * @param id
     * @returns
     */
    async getControlEvidence(
        id: number,
        requestDto: ControlEvidenceRequestDto,
    ): Promise<PaginationType<ExternalEvidence>> {
        return this.externalEvidenceRepository.getControlEvidence(id, requestDto);
    }

    /**
     * @deprecated Use PoliciesCoreService.getControlPolicies
     *
     * @param id
     * @param requestDto
     * @returns
     */
    async getControlPolicies(
        id: number,
        requestDto: ControlPoliciesRequestDto,
    ): Promise<PaginationType<Policy>> {
        return this.policiesCoreService.getControlPolicies(id, requestDto);
    }

    /**
     * This method retrieves policies with version by control Id
     * @param controlId
     * @param dto
     * @returns {Promise<PaginationType<Policy>>}
     */
    async getControlPoliciesWithVersionByControlId(
        account: Account,
        controlId: number,
        controlPolicyVersionRequestDto: ControlPolicyVersionRequestDto,
    ): Promise<PaginationType<PolicyWithControlWorkspaceGroup>> {
        return controlPolicyVersionRequestDto.onlyCurrentPublishedVersion
            ? this.policiesCoreService.getControlPoliciesWithCurrentPublishedVersion(
                  controlId,
                  controlPolicyVersionRequestDto,
              )
            : this.policiesCoreService.getPoliciesWithLatestVersionAndWorkspaceGroupByControlId(
                  account,
                  controlId,
                  controlPolicyVersionRequestDto,
              );
    }

    async getControlRisksWithCurrentVersion(
        controlId: number,
        requestDto: ControlRisksRequestDto,
    ): Promise<PaginationType<Risk>> {
        return this.riskRepository.getControlRisks(controlId, requestDto);
    }

    getControlEvidenceLibrary(
        account: Account,
        id: number,
        requestDto: ControlReportsRequestDto,
    ): Promise<PaginationType<LibraryDocument>> {
        return this.documentLibraryCoreService.getControlEvidence(account, id, requestDto);
    }

    @CacheBusterWithPrefix<PaginationType<EvidenceUnion>>({
        stores: [Caches.FIND_CONTROL_DETAILS_BY_ID],
    })
    async getControlEvidenceUnion(
        account: Account,
        controlId: number,
        page: number,
        limit: number,
        sort = SortType.STATUS,
        sortDir = SortDir.ASC,
    ): Promise<PaginationType<EvidenceUnion>> {
        // TODO: Remove feature flag validation with this ticket:https://drata.atlassian.net/browse/ENG-58940
        const hideTestEvidence = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_HIDE_TEST_EVIDENCES,
                category: FeatureFlagCategory.NONE,
                defaultValue: true,
            },
            account,
        );

        const controlEvidence = await this.controlRepository.getEvidenceUnion(
            account,
            controlId,
            page,
            limit,
            sort,
            sortDir,
            hideTestEvidence,
        );

        /**
         * This type overlap is required due the way the source of the is ready is being deprecated
         */
        type controlType = Omit<Control, 'isReady'> & {
            isReady: '0' | '1';
        } & ControlWorkspaceGroup;

        const promises: Promise<controlType[]>[] = [];

        for (const evidence of controlEvidence.data) {
            if (evidence.sourceTable === 'EXTERNAL_EVIDENCE') {
                promises.push(
                    this.controlRepository.getControlWithReadyByEvidenceId(
                        account,
                        evidence.evidenceId,
                    ),
                );
            }

            if (evidence.sourceTable === 'LIBRARY_DOCUMENT') {
                promises.push(
                    this.controlRepository.getControlsWithReadyByLibraryDocumentId(
                        account,
                        evidence.evidenceId,
                    ),
                );
            }
        }

        const settledPromises = await Promise.allSettled(promises);

        const controlLinkedWorkspaces: {
            workspace: {
                id: number;
                name: string;
            };
            controlId: number;
            hasIndependentControlOwners: boolean;
            groupingGeneratorId: number;
        }[] = [];

        settledPromises.forEach((result, i) => {
            if (result.status === 'fulfilled') {
                controlEvidence.data[i].controls = result.value.map((value: any) => {
                    const controlValue = value as {
                        control: Control;
                        isReady: boolean;
                        workspaceGroup: ControlWorkspaceGroup;
                    };

                    if (
                        isNil(
                            controlLinkedWorkspaces.find(
                                clw =>
                                    clw.controlId === value.fk_control_id &&
                                    clw.groupingGeneratorId === value.fk_grouping_generator_id,
                            ),
                        )
                    ) {
                        controlLinkedWorkspaces.push({
                            workspace: {
                                id: value.fk_workspace_id,
                                name: value.workspace_name,
                            },
                            controlId: value.fk_control_id,
                            hasIndependentControlOwners: value.has_independent_owners === '1',
                            groupingGeneratorId: value.fk_grouping_generator_id,
                        });
                    }

                    controlValue.control = new Control();
                    controlValue.control.id = value.id;
                    controlValue.control.code = value.code;
                    controlValue.control.name = value.name;
                    controlValue.control.description = value.description;
                    controlValue.control.question = value.question;
                    controlValue.control.slug = value.slug;
                    controlValue.control.rationale = value.rationale;
                    controlValue.control.archivedAt = value.archivedAt;
                    controlValue.control.enabledAt = value.enabledAt;
                    controlValue.control.domain = value.domain;
                    controlValue.control.category = value.category;

                    controlValue.isReady = value.isReady === '1';

                    return controlValue;
                });
            } else {
                this.logger.error(
                    PolloMessage.msg(`Failed to get controls for evidence. Error: ${result.reason}`)
                        .setError(new Error(result.reason))
                        .setContext(this.constructor.name)
                        .setDomain(account.domain)
                        .setSubContext('getControlEvidenceUnion'),
                );
            }
        });

        for (const data of controlEvidence.data) {
            for (const control of data.controls) {
                control.workspaceGroup = controlLinkedWorkspaces.filter(
                    clw => clw.controlId === control.control.id,
                ) as unknown as ControlWorkspaceGroup;
            }
        }

        return controlEvidence;
    }

    getControlEvidenceWithinATimeFrame(
        id: number,
        requestDto: ControlReportsRequestDto,
        auditDates?: AuditDatesType,
    ): Promise<LibraryDocument[]> {
        return this.documentLibraryCoreService.getControlEvidenceWithinATimeFrame(
            id,
            requestDto,
            auditDates,
        );
    }

    /**
     * @deprecated Use ControlService.listControls
     *
     * @param account
     * @param requestDto
     * @returns
     */
    async listControls(
        account: Account,
        requestDto: ControlsRequestDto,
    ): Promise<PaginationType<Control>> {
        const { page, limit } = requestDto;

        const [controlIds, total] = await this.index.getControlIds(account, requestDto);

        const data = await this.controlRepository.getControlsFromIds(controlIds);

        for (const control of data) {
            control.description = control.description.replace(/%s/g, account.companyName);
        }

        return {
            data,
            page,
            limit,
            total,
        };
    }

    /**
     * @deprecated Use ControlsOrchestrationService.makeFacetRunner
     */
    makeFacetRunner(account: Account, requestDto: ControlsRequestDto): FacetRunner {
        const controlFacet = new ControlFacet(account, this.controlRepository, requestDto);

        const isArchivedFacet = new IsArchivedFacet(account, this.controlRepository, requestDto);

        const productId = getProductId(account) ?? requestDto?.workspaceId;

        const ownerFacet = new OwnerFacet(account, this.controlRepository, requestDto);

        const isReadyFacet = new IsReadyFacet(
            account,
            this.controlIsReadyViewRepository,
            requestDto,
        );

        const searchFacet = new SearchFacet(account, this.controlRepository, requestDto);

        const testFacet = new TestFacet(account, this.controlRepository, requestDto);

        const ticketFacet = new TicketFacet(account, this.controlTicketViewRepository, requestDto);

        const approvalFacet = new ApprovalFacet(account, this.controlRepository, requestDto);

        const approverFacet = new ApproverFacet(account, this.controlRepository, requestDto);

        const taskOwnerFacet = new TaskOwnerFacet(account, this.controlRepository, requestDto);

        const workspaceFacet = new WorskpaceFacet(account, this.controlRepository, productId);

        const facetRunner = new FacetRunner();

        facetRunner.addFacet(controlFacet);
        facetRunner.addFacet(isArchivedFacet);
        facetRunner.addFacet(ownerFacet);
        facetRunner.addFacet(isReadyFacet);
        facetRunner.addFacet(searchFacet);
        facetRunner.addFacet(testFacet);
        facetRunner.addFacet(ticketFacet);
        facetRunner.addFacet(approvalFacet);
        facetRunner.addFacet(approverFacet);
        facetRunner.addFacet(taskOwnerFacet);
        facetRunner.addFacet(workspaceFacet);
        facetRunner.addExcludeIds(requestDto.excludeIds ?? []);

        return facetRunner;
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getPaginatedControlIds
     */
    async getPaginatedControlIds(
        account: Account,
        requestDto: ControlsRequestDto,
    ): Promise<[number[], number]> {
        const facetRunner = this.makeFacetRunner(account, requestDto);

        const facetResponse = await facetRunner.run();

        // if facets activated and no data then early return
        if (facetRunner.isAnyFacetActivated() && facetResponse.include.length === 0) {
            return [[], facetResponse.include.length];
        }

        const page = await this.controlRepository.getPageIds(
            facetResponse.include,
            facetResponse.exclude,
            requestDto.page,
            requestDto.limit,
            requestDto.sort,
            requestDto.sortDir,
        );

        return [page.data, page.total ?? 0];
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getControlIds
     */
    async getControlIds(account: Account, requestDto: ControlsRequestDto): Promise<number[]> {
        const facetResponse = await this.makeFacetRunner(account, requestDto).run();
        return facetResponse.include;
    }

    /**
     * @deprecated Use ControlsOrchestrationService.listControlsGroupBy
     * @param requestDto
     * @param account
     * @returns
     */
    @CacheWithPrefix<PaginationType<ControlListWithFlags>>(null, {
        store: Caches.LIST_CONTROLS,
        useArgs: 1,
        ttl: config.get('cache.ttl.hour'),
    })
    async listControlsGroupBy(
        requestDto: ControlsRequestDto,
        account: Account,
        user?: User,
    ): Promise<PaginationType<ControlListWithFlags>> {
        const { page, limit } = requestDto;
        if (!isNil(requestDto.hasTicket)) {
            const clientTypes = ConnectionFeatureClientTypes.get(
                ConnectionFeature.TICKETING_TICKET_MANAGEMENT,
            );

            const ticketingWriteConnectionIds =
                await this.connectionsCoreService.getConnectionIdsWithWriteAccessByClientTypes(
                    clientTypes,
                );

            if (!isEmpty(ticketingWriteConnectionIds)) {
                requestDto.connectionIds = ticketingWriteConnectionIds;
            }
        }
        if (requestDto.hasEvidence) {
            this._eventBus.publish(new ControlFilteredByEvidenceEvent(account, user));
        }
        if (requestDto.hasPolicy) {
            this._eventBus.publish(new ControlFilteredByPolicyEvent(account, user));
        }

        /**
         * Defaults to the primary product ID if no product ID is supplied in the dto
         * or extracted from the account. Without a supplied product ID, controls will be
         * returned without frameworkTags. This was done to account for a gap in our public api
         */

        let productId = getProductId(account);
        // productId should only be supplied in requestDto via public api
        if (requestDto?.workspaceId) {
            const product = await this.workspacesCoreService.getProductById(
                requestDto?.workspaceId,
            );

            if (isNil(product)) {
                throw new NotFoundException(
                    `Workspace not found for ID: ${requestDto?.workspaceId}`,
                );
            }

            productId = requestDto?.workspaceId;
        } else if (isNil(productId)) {
            productId = (await this.workspacesCoreService.getPrimaryProduct()).id ?? null;
        }

        const productFrameworksEnabled =
            await this.frameworksCoreService.getFrameworksEnabledByProduct(productId);

        const activeFrameworks = productFrameworksEnabled.map(item => item.framework);

        if (!isEmpty(requestDto.libraryDocumentId)) {
            const controlIdsEvidence = await this.documentLibraryCoreService.getEvidenceControlsIds(
                requestDto.libraryDocumentId,
            );
            if (!isEmpty(controlIdsEvidence)) {
                if (!isEmpty(requestDto.excludeIds)) {
                    requestDto.excludeIds = requestDto.excludeIds.concat(controlIdsEvidence);
                } else {
                    requestDto.excludeIds = controlIdsEvidence;
                }
            }
        }

        const [controlIds, total] = await this.getPaginatedControlIds(account, requestDto);

        const controls = await this.controlRepository.getControlsFromIdsGroupBy(
            controlIds,
            requestDto.sort,
            requestDto.sortDir,
        );

        const foundControlIds = controls.map(c => c.id);

        const controlFlags = !isEmpty(foundControlIds)
            ? await this.controlFlagsViewRepository.getControlFlags(foundControlIds)
            : [];

        let data: ControlListWithFlags[] = [];

        if (controlIds.length > 0) {
            for (const control of controls) {
                control.description = control.description.replace(/%s/g, account.companyName);

                const controlFlagSet = controlFlags.find(flags => flags.controlId == control.id);

                if (!isEmpty(activeFrameworks)) {
                    const frameworkTags = controlFlagSet.frameworkTags.reduce((acc, tag) => {
                        if (tag !== FrameworkTag.CUSTOM) {
                            // Incorporating the filter condition inside reduce
                            const frameworkTagData = activeFrameworks.find(
                                framework => framework.tag === tag,
                            );

                            if (!isUndefined(frameworkTagData)) {
                                // Only add to acc if frameworkTagData is defined
                                acc.push({
                                    label: frameworkTagData.pill,
                                    color: frameworkTagData.color,
                                    bgColor: frameworkTagData.bgColor,
                                });
                            }
                        }
                        return acc;
                    }, []);

                    const customFrameworkPill: FrameworkTagType[] = activeFrameworks
                        .filter(framework =>
                            controlFlagSet.customFrameworkSlugs.includes(framework.slug),
                        )
                        .map(framework => ({
                            label: framework.pill,
                            color: framework.color,
                            bgColor: framework.bgColor,
                        }));

                    control.frameworkTags = concat(frameworkTags, customFrameworkPill);
                } else {
                    control.frameworkTags = [];
                }

                control.topics = controlFlagSet.topics;

                data.push({
                    control,
                    hasEvidence: controlFlagSet.hasEvidence,
                    hasPolicy: controlFlagSet.hasPolicy,
                    hasOwner: controlFlagSet.hasOwner,
                    isMonitored: controlFlagSet.isMonitored,
                    isReady: controlFlagSet.isReady,
                    hasTicket: controlFlagSet.hasTicket,
                });
            }

            const getControlIssuesFeature = await this.featureFlagService.evaluateAsTenant(
                {
                    name: FeatureFlag.RELEASE_CONTROL_ACTION_PANEL,
                    category: FeatureFlagCategory.NONE,
                    defaultValue: false,
                },
                account,
            );

            if (getControlIssuesFeature) {
                const notReadyControls = data
                    ?.filter(control => !control?.isReady)
                    .map(control => control?.control?.id);
                const issuesMapping = await this.getControlFindings(notReadyControls, account);

                if (!isEmpty(issuesMapping)) {
                    data = data.map((control: ControlListWithFlags) => {
                        const findings = issuesMapping[String(control.control.id)];
                        control.issues = findings;
                        return control;
                    });
                }
            }
        }

        return {
            data,
            page,
            limit,
            total,
        };
    }

    /**
     *
     * This method is in charge of retrieving all the issues related to a control that makes it as not ready
     *
     * @param controlIds
     * @param account
     * @returns
     */
    async getControlFindings(
        controlIds: number[],
        account: Account,
    ): Promise<Record<number, IControlIssuesStats>> {
        const controlIssueMapping: Record<number, IControlIssuesStats> = {};
        if (isEmpty(controlIds)) {
            return {};
        }

        const promises = controlIds.map(async controlId => {
            const { policies, evidence, tests } = await this.controlService.findControlDetailsById(
                controlId,
                account,
            );
            const [approval] = await this.controlApprovalsService.listControlApprovals(
                controlId,
                true,
                false,
                account,
            );
            const isApprovalNeeded =
                !isEmpty(approval) && approval?.status === ApprovalStatus.READY_FOR_REVIEWS;
            const policyCount = policies?.notReady?.length;
            const evidenceCount =
                get(evidence, 'external.notReady.length', 0) +
                get(evidence, 'library.notReady.length', 0);

            const testsCount = tests?.notReady?.length;

            const stat: IControlIssuesStats = {
                approvals: 0,
                evidence: evidenceCount,
                policies: {
                    count: policyCount,
                    unapprovedPoliciesIds: policies?.notReady,
                },
                tests: testsCount,
                total: policyCount + evidenceCount + testsCount,
            };

            if (isApprovalNeeded) {
                stat.approvals += 1;
                stat.total += 1;
            }

            controlIssueMapping[controlId] = stat;
        });

        await Promise.allSettled(promises);

        return controlIssueMapping;
    }

    /**
     *
     * @param controlTemplateId
     * @param account
     * @returns
     */
    @CacheWithPrefix<ControlTemplate>(ControlTemplate, {
        store: Caches.CONTROL_TEMPLATE,
        useArgs: 1,
        ttl: config.get('cache.ttl.15min'),
    })
    async getControlTemplate(
        controlTemplateId: number,
        account: Account,
    ): Promise<ControlTemplate> {
        let controlTemplate: ControlTemplate;
        try {
            controlTemplate =
                await this.controlTemplateService.findOneByIdOrFail(controlTemplateId);

            controlTemplate.description = controlTemplate.description.replace(
                /%s/g,
                account.companyName,
            );
        } catch (err) {
            this.logger.error(PolloAdapter.acct(err.message, account).setError(err));

            throw err;
        }

        return controlTemplate;
    }

    /**
     * Copied here to remove trust center dependency
     * @param code
     */
    getMonitoringContolByControlCode(code: string): Promise<TrustCenterMonitoringControl | null> {
        return this.monitoringControlRepository.findOne({
            where: {
                controlCode: code,
            },
        });
    }

    /**
     * Copied here to remove trust center dependency
     * @param {string} code
     * @param {QueryDeepPartialEntity<TrustCenterMonitoringControl>} partialEntity
     * @returns {Promise<TrustCenterMonitoringControl>}
     */
    updateMonitoringControl(
        code: string,
        partialEntity: QueryDeepPartialEntity<TrustCenterMonitoringControl>,
    ): Promise<UpdateResult> {
        return this.monitoringControlRepository.update({ controlCode: code }, partialEntity);
    }

    @CacheBusterWithPrefix<ControlWithEvidence>({
        store: Caches.LIST_CONTROLS,
    })
    async modifyControl(
        account: Account,
        user: User,
        id: number,
        controlModifyRequestType: ControlModifyRequestType,
        isPropagated: boolean = false,
    ): Promise<ControlWithEvidence> {
        const CODE_SUBSTRING_INITIAL_INDEX = 0;
        const CODE_SUBSTRING_FINAL_INDEX = 3;

        const [oldControl, oldControlUnfilledDescription] = await Promise.all([
            this.controlService.findControlById(id, account, ['products']),
            this.controlRepository.getUnfilledControlDescriptionByControlId(id),
        ]);

        const oldControlCodePrefix = oldControl.code
            .substring(CODE_SUBSTRING_INITIAL_INDEX, CODE_SUBSTRING_FINAL_INDEX)
            .toLowerCase();

        const drataTestCodePrefix = config.get('api.codePrefix');

        const workspaceId = account.getCurrentProduct().id;

        let updateCode = false;

        if (
            !isNil(controlModifyRequestType.code) &&
            oldControl.code !== controlModifyRequestType.code
        ) {
            if (oldControlCodePrefix === drataTestCodePrefix) {
                throw new BadRequestException('Cannot change DCF code prefix');
            }
            updateCode = (
                await this.controlService.isAvailable({
                    code: controlModifyRequestType.code,
                    name: controlModifyRequestType.name,
                    workspaceId,
                })
            ).available;

            if (!updateCode && !isPropagated) {
                throw new BadRequestException('Updated code already exists');
            }
        }

        const controlDescriptionUpdate =
            controlModifyRequestType.description === oldControl.description
                ? oldControlUnfilledDescription
                : controlModifyRequestType.description;

        await this.controlRepository.update(
            { id: id },
            {
                name: controlModifyRequestType.name,
                activity: controlModifyRequestType.activity,
                question: controlModifyRequestType.question,
                description: controlDescriptionUpdate,
                lastUpdatedBy: user,
                code: updateCode ? controlModifyRequestType.code : oldControl.code,
                controlNumber: updateCode
                    ? this.getControlNumber(controlModifyRequestType.code)
                    : oldControl.controlNumber,
            },
        );

        // Trigger reindex for control details update
        if (!isNil(workspaceId)) {
            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [id]));
        }

        const controlInTrust = await this.getMonitoringContolByControlCode(oldControl.code);

        if (!isNil(controlInTrust)) {
            await this.updateMonitoringControl(oldControl.code, {
                name: controlModifyRequestType.name,
                controlCode: controlModifyRequestType.code,
            });
        }

        const { control, hasEvidence, isReady, hasTicket, isMonitored, hasPolicy } =
            await this.controlService.findControlWithEvidenceByIdOrFail(account, id);

        const { control: controlForEvent, evidences } = await this.buildControlForAnalyticsEvent(
            account,
            control,
        );

        if (controlModifyRequestType?.hasControlTemplateValues) {
            this._eventBus.publish(
                new ControlTemplateAppliedEvent(account, user, controlForEvent, evidences),
            );
        }

        this._eventBus.publish(
            new EditControlInfoEvent(account, user, controlForEvent, evidences, false),
        );
        this._eventBus.publish(
            new ControlDetailsUpdatedWorkflowTriggerEvent(account, user, control.id, workspaceId),
        );

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, workspaceId, null),
        );

        return {
            control,
            hasEvidence,
            isReady,
            hasTicket,
            isMonitored,
            hasPolicy,
        };
    }

    /**
     * @deprecated Use ControlsOrchestrationService.modifyControlOwners
     *
     * @param controlId
     * @param controlOwnersPutRequestType
     * @returns
     */
    async modifyControlOwners(
        controlId: number,
        controlOwnersPutRequestType: ControlOwnersPutRequestType,
    ): Promise<User[]> {
        const ownersToAdd = await this.usersCoreService.getUsersByIds(
            controlOwnersPutRequestType.ownerUserIds,
        );
        if (ownersToAdd.length !== controlOwnersPutRequestType.ownerUserIds.length) {
            throw new BadRequestException('One or more of the supplied Owner IDs does not exist');
        }
        await this.putAllControlOwnerHelper(controlId, ownersToAdd);
        return ownersToAdd;
    }

    @CacheBusterWithPrefix<PaginationType<User>>({
        stores: [Caches.LIST_CONTROLS, Caches.USER_BY_ENTRY_ID],
    })
    async putControlOwners(
        account: Account,
        id: number,
        requestDto: ControlOwnerRequestDto,
        user?: User,
    ): Promise<PaginationType<User>> {
        if (
            user &&
            !hasUserPermission(user, Action.READ, Subject.ViewAllControls) &&
            hasUserPermission(user, Action.READ, Subject.Control)
        ) {
            throw new ForbiddenException(
                `Restricted view enabled for ${user.email}`,
                ErrorCode.RESTRICTED_VIEW_ENABLED,
            );
        }
        const ownersIdsToModify = await this.validateModifiedOwners(requestDto.ownerIds, id);
        const ownersToAdd = await this.usersCoreService.getUsersByIds(requestDto.ownerIds);
        const control = await this.putAllControlOwnerHelper(id, ownersToAdd);
        const ret: PaginationType<User> = {
            data: [],
            page: 1,
            limit: config.get('controls.maxOwners'),
            total: control.owners.length,
        };
        control.owners.sort((a, b) => a.firstName.localeCompare(b.firstName));
        ret.data = control.owners;

        if (!isEmpty(user) && !isNil(user)) {
            const currentProductOfControl = await this.getCurrentProductOfControl(control.id);
            this._eventBus.publish(
                new ControlOwnerAddedEvent(
                    account,
                    user,
                    control,
                    ownersToAdd,
                    currentProductOfControl,
                ),
            );

            const { control: controlForEvent, evidences } =
                await this.buildControlForAnalyticsEvent(account, control);

            this._eventBus.publish(
                new EditControlInfoEvent(account, user, controlForEvent, evidences, true),
            );

            const workspaceId = account.getCurrentProduct()?.id;

            if (!isNil(workspaceId)) {
                // Trigger for Custom Workflow
                this._eventBus.publish(
                    new ControlOwnerUpdatedWorkflowTriggerEvent(
                        account,
                        user,
                        ownersIdsToModify,
                        control.id,
                        workspaceId,
                    ),
                );
            } else {
                this.logger.warn(
                    PolloMessage.msg('Unable to get workspace Id ')
                        .setIdentifier({ reason: 'Unable to get workspace Id' })
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext('putControlOwners'),
                );
            }
        }

        return ret;
    }

    @CacheBusterWithPrefix<PaginationType<ControlUserType>>({
        store: Caches.LIST_CONTROLS,
    })
    async bulkPutControlOwners(
        account: Account,
        user: User,
        requestDto: ControlOwnerBulkRequestDto,
    ): Promise<PaginationType<ControlUserType>> {
        const controls = await this.controlRepository.find({
            where: { id: In(requestDto.controlIds) },
            relations: ['owners'],
        });
        const ownersToAdd = await this.usersCoreService.getUsersByIds(requestDto.ownerIds);
        const ret: PaginationType<ControlUserType> = {
            data: [],
            page: 1,
            limit: config.get('controls.maxOwners'),
            total: 0,
        };
        await Promise.all(
            controls.map(async control => {
                const controlSave = await this.putControlOwnerHelper(
                    control.id,
                    ownersToAdd,
                    account,
                    user,
                );
                controlSave.owners.sort((a, b) => a.firstName.localeCompare(b.firstName));
                controlSave.owners.forEach(owner => {
                    ret.data.push({ control: controlSave, user: owner });
                    ++ret.total;
                });
            }),
        );

        return ret;
    }

    /**
     * @deprecated Use GrcCoreService.listAllControlsForNotification
     *
     * @param notificationControls
     * @returns
     */
    async listAllControlsForNotification(notificationControls: NotificationControl[]): Promise<{
        withAllNotReady: Control[];
        withFailingTest: Control[];
        withExpiredEvidenceNotMonitored: Control[];
        withMissingEvidenceNotMonitored: Control[];
    }> {
        /**
         * We are not required to filter by products so pass an empty account
         */
        const [, , controlsNotReady] = await this.index.getControlIds(null, {
            isReady: false,
            isArchived: false,
        } as any);
        const notificationControlIds = notificationControls.map(control => control.control.id);

        const withAllNotReady = await this.groupNotificationControlsBy(
            controlsNotReady,
            notificationControlIds,
            flag => flag.isReady === false,
        );

        const withFailingTest = await this.groupNotificationControlsBy(
            controlsNotReady,
            notificationControlIds,
            flag => flag.isMonitored === true,
        );

        const withExpiredEvidenceNotMonitored = await this.groupNotificationControlsBy(
            controlsNotReady,
            notificationControlIds,
            flag => flag.isMonitored === false && flag.hasEvidence === true,
        );

        const withMissingEvidenceNotMonitored = await this.groupNotificationControlsBy(
            controlsNotReady,
            notificationControlIds,
            flag =>
                flag.isMonitored === false &&
                flag.hasEvidence === false &&
                flag.hasPolicy === false,
        );

        return {
            withAllNotReady,
            withFailingTest,
            withExpiredEvidenceNotMonitored,
            withMissingEvidenceNotMonitored,
        };
    }

    private async groupNotificationControlsBy(
        controlsNotReady: ControlFlags[],
        notificationControlIds: number[],
        predicate: (flag: ControlFlags) => boolean,
    ): Promise<Control[]> {
        return this.controlRepository.getControlsFromIds(
            controlsNotReady
                .filter(controlFlag => {
                    return (
                        predicate(controlFlag) &&
                        notificationControlIds.includes(controlFlag.controlId)
                    );
                })
                .map(control => control.controlId),
        );
    }

    private async putControlOwnerHelper(
        controlId: number,
        ownersToAdd: User[],
        account: Account,
        user: User,
    ): Promise<Control> {
        const control = await this.controlRepository.findOneOrFail({
            where: { id: controlId },
            relations: ['owners'],
        });

        const newOwners = [];
        ownersToAdd.forEach(newOwner => {
            if (
                !hasRole(newOwner, [
                    Role.ADMIN,
                    Role.TECHGOV,
                    Role.WORKSPACE_ADMINISTRATOR,
                    Role.CONTROL_MANAGER,
                ])
            ) {
                throw new BadRequestException(
                    'User must be ADMIN, TECHGOV, CONTROL_MANAGER or WORKSPACE ADMIN',
                );
            }
            // no dupes
            if (!control.owners.find(owner => owner.id === newOwner.id)) {
                control.owners.push(newOwner);
                newOwners.push(newOwner);
            }
        });

        await this.controlRepository.save(control);

        const currentProductOfControl = await this.getCurrentProductOfControl(control.id);

        this._eventBus.publish(
            new ControlOwnerAddedEvent(account, user, control, newOwners, currentProductOfControl),
        );

        const ownersToAddIds = ownersToAdd.map(owner => owner.id);

        const workspaceId = account.getCurrentProduct()?.id;

        // Trigger reindex for control owners update
        if (!isNil(workspaceId)) {
            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [control.id]));
        }

        if (!isNil(workspaceId)) {
            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlOwnerUpdatedWorkflowTriggerEvent(
                    account,
                    user,
                    ownersToAddIds,
                    control.id,
                    workspaceId,
                ),
            );

            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [control.id]));
        } else {
            this.logger.warn(
                PolloMessage.msg('Unable to get workspace Id ')
                    .setIdentifier({ reason: 'Unable to get workspace Id' })
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('putControlOwnerHelper'),
            );
        }
        return control;
    }

    /**
     * @deprecated Use ControlsOrchestrationService.putAllControlOwnerHelper
     *
     * @param controlId
     * @param ownersToAdd
     * @returns
     */
    private async putAllControlOwnerHelper(
        controlId: number,
        ownersToAdd: User[],
    ): Promise<Control> {
        try {
            const control = await this.controlRepository.findOneOrFail({
                where: { id: controlId },
                relations: ['owners'],
            });

            ownersToAdd.forEach(newOwner => {
                if (
                    !hasRole(newOwner, [
                        Role.ADMIN,
                        Role.TECHGOV,
                        Role.WORKSPACE_ADMINISTRATOR,
                        Role.CONTROL_MANAGER,
                    ])
                ) {
                    throw new BadRequestException(
                        'User must be ADMIN, TECHGOV, CONTROL_MANAGER or WORKSPACE ADMIN',
                    );
                }
            });

            control.owners = ownersToAdd;

            await this.controlRepository.save(control);

            // Trigger reindex for control owners update
            const account = this.tenantAccount;
            if (!isNil(account)) {
                const currentProduct = await this.workspacesCoreService.getCurrentProductOfControl(
                    control.id,
                );
                this._eventBus.publish(
                    new ControlsReindexEvent(account, currentProduct.id, [control.id]),
                );
            }

            return control;
        } catch (error) {
            if (error instanceof BadRequestException) {
                // if the owner addition fails, rethrow the error instance
                throw error;
            } else {
                // catch everything else as 500
                throw new InternalServerErrorException(error);
            }
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getControlOwnersWithCursor
     *
     * @param controlId
     * @param controlOwnerRequestType
     * @returns
     */
    getControlOwnersWithCursor(
        controlId: number,
        controlOwnerRequestType: ControlOwnersRequestType,
    ): Promise<CursorPage<User>> {
        return this.usersCoreService.getControlOwnersWithCursor(controlOwnerRequestType, controlId);
    }

    getControlOwnersForControl(
        controlId: number,
        requestDto: ControlOwnersRequestDto,
    ): Promise<PaginationType<User>> {
        return this.usersCoreService.getControlOwnersByControlId(requestDto, controlId);
    }

    getControlOwners(requestDto: ControlOwnersRequestDto): Promise<PaginationType<User>> {
        return this.usersCoreService.getControlOwnersByControlId(requestDto);
    }

    /**
     * @deprecated Use ControlsOrchestrationService.createControlOwner
     *
     * @param controlId
     * @param ownerId
     * @returns
     */
    async createControlOwner(controlId: number, ownerId: number): Promise<User> {
        const controlUserMap = (
            await this.controlRepository.getControlOwnersIntersection({
                page: 1,
                limit: undefined,
                controlIds: [controlId],
            })
        ).data;
        const ownerIds = controlUserMap.map(controlOwner => controlOwner.id);
        if (ownerIds.includes(ownerId)) {
            throw new BadRequestException(
                `Owner with id: ${ownerId} is already mapped to control: ${controlId}`,
            );
        }
        ownerIds.push(ownerId);
        const allControlOwners = await this.usersCoreService.getUsersByIds(ownerIds);
        await this.putAllControlOwnerHelper(controlId, allControlOwners);

        const addedOwner = allControlOwners.find(owner => owner.id === ownerId);
        if (!addedOwner) {
            throw new UnprocessableEntityException(`Owner with ID ${ownerId} not found`);
        }
        return addedOwner;
    }

    /**
     * @deprecated Use ControlsOrchestrationService.deleteControlOwner
     *
     * @param account
     * @param user
     * @param workspaceIdSpecified
     * @param controlId
     * @param ownerId
     */
    async deleteControlOwner(
        account: Account,
        user: User,
        workspaceIdSpecified: number,
        controlId: number,
        ownerId: number,
    ): Promise<void> {
        await this.controlRepository.findOneOrFail({
            where: [{ id: controlId, owners: { id: ownerId } }],
        });
        await this.bulkDeleteControlOwners(
            account,
            user,
            {
                controlIds: [controlId],
                ownerIds: [ownerId],
            },
            workspaceIdSpecified,
        );
    }

    /**
     * @deprecated Use ControlsOrchestrationService.bulkDeleteControlOwners
     *
     * @param account
     * @param requestUser
     * @param requestDto
     * @param workspaceIdSpecified
     */
    @CacheBusterWithPrefix<void>({
        store: Caches.LIST_CONTROLS,
    })
    async bulkDeleteControlOwners(
        account: Account,
        requestUser: User,
        requestDto: ControlOwnerBulkRequestDto,
        workspaceIdSpecified?: number,
    ): Promise<void> {
        const controlUserMap = (
            await this.controlRepository.getControlOwnersIntersection({
                page: 1,
                limit: null,
                controlIds: requestDto.controlIds,
            })
        ).data;
        const usersToDelete = controlUserMap.filter(foundUser => {
            return requestDto.ownerIds.includes(foundUser.id);
        });
        await Promise.all(
            usersToDelete.map(async user => {
                const fullUser = await this.usersCoreService.getUserByIdWithRelations(user.id, [
                    'controls',
                ]);
                const controlsRemoved = [];
                const controlsToKeep = [];
                fullUser.controls.forEach(control => {
                    if (requestDto.controlIds.includes(control.id)) {
                        controlsRemoved.push(control);
                    } else {
                        controlsToKeep.push(control);
                    }
                });
                fullUser.controls = controlsToKeep;
                await this.usersCoreService.saveUser(fullUser, account.id);

                const controlsRemovedWithProduct = await Promise.all(
                    controlsRemoved.map((control: Control) => this.addProductsToControl(control)),
                );

                this._eventBus.publish(
                    new ControlOwnerDeletedEvent(
                        account,
                        requestUser,
                        controlsRemovedWithProduct,
                        fullUser,
                    ),
                );
            }),
        );
        const userIdsToDelete = usersToDelete.map(user => user.id);

        const workspaceId = requestDto.workspaceId ?? workspaceIdSpecified;

        if (!isNil(workspaceId)) {
            // Trigger reindex for control owners update
            this._eventBus.publish(
                new ControlsReindexEvent(account, workspaceId, requestDto.controlIds),
            );

            requestDto.controlIds.forEach(id => {
                // Trigger for Custom Workflow
                this._eventBus.publish(
                    new ControlOwnerUpdatedWorkflowTriggerEvent(
                        account,
                        requestUser,
                        userIdsToDelete,
                        id,
                        workspaceId,
                    ),
                );
            });
        } else {
            this.logger.warn(
                PolloMessage.msg('Unable to get workspace Id ')
                    .setIdentifier({ reason: 'Unable to get workspace Id' })
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('bulkDeleteControlOwners'),
            );
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.addProductsToControl
     *
     * @param control
     * @returns
     */
    async addProductsToControl(control: Control): Promise<Control> {
        const currentProductOfControl = await this.getCurrentProductOfControl(control.id);
        // current product inside an array because it needs to match the Control entity
        control.products = [currentProductOfControl];
        return control;
    }

    /**
     * @deprecated Use WorkspacesCoreService.getCurrentProductOfControl
     *
     * @param controlId
     * @returns
     */
    async getCurrentProductOfControl(controlId: number): Promise<Product> {
        const currentProduct = await this.productRepository.getProductByControlId(controlId);
        return sanitizeProduct(currentProduct);
    }

    /**
     *
     * @param account
     * @param requestUser
     * @param requestDto
     */
    @CacheBusterWithPrefix<void>({
        stores: [
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async bulkPutControlTests(
        account: Account,
        requestUser: User,
        requestDto: ControlTestBulkRequestDto,
    ): Promise<void> {
        if (
            isNil(
                account.entitlements.find(
                    feat => feat.type === AccountEntitlementType.MAP_CONTROLS_TESTS,
                ),
            )
        ) {
            throw new UnauthorizedException(ErrorCode.CUSTOMER_UNAUTHORIZED);
        }

        const controls = await this.controlRepository.find({
            where: { id: In(requestDto.controlIds) },
            relations: ['controlTestInstances'],
        });

        let controlTests = await this.monitorsService.findControlTestInstances({
            where: { testId: In(requestDto.testIds) },
            relations: ['products'],
        });

        controlTests = filterControlTests(account, controlTests);
        this.fillInDescriptions(account, controls, controlTests);

        let exists = false;

        let controlTestInstancesToMap: ControlTestInstance[] = [];
        for (const control of controls) {
            const newControlTests = controlTests.filter(
                test =>
                    !includes(
                        control.controlTestInstances.map(cti => cti.testId),
                        test.testId,
                    ),
            );

            const newControlTestIds = newControlTests.map(test => test.id);

            if (newControlTests.length > 0) {
                for (const newControlTest of newControlTests) {
                    control.controlTestInstances.push(newControlTest);
                }

                exists = true;

                // eslint-disable-next-line no-await-in-loop
                await this.controlRepository.save(control, { reload: false });
                controlTestInstancesToMap = controlTestInstancesToMap.concat(newControlTests);

                const workspaceId = account.getCurrentProduct()?.id;

                if (!isNil(workspaceId)) {
                    // Trigger reindex for control tests update
                    this._eventBus.publish(
                        new ControlsReindexEvent(account, workspaceId, [control.id]),
                    );

                    // Trigger for Custom Workflow
                    this._eventBus.publish(
                        new ControlMappedToTestWorkflowTriggerEvent(
                            account,
                            requestUser,
                            newControlTestIds,
                            control.id,
                            workspaceId,
                        ),
                    );

                    // Only fire readiness event if at least one of the tests is not a draft
                    const hasNonDraftTests = newControlTests.some(test => !test.draft);
                    if (hasNonDraftTests) {
                        this._eventBus.publish(
                            new ControlReadinessEvent(
                                account,
                                control.id,
                                workspaceId,
                                requestUser,
                            ),
                        );
                    }
                } else {
                    this.logger.warn(
                        PolloMessage.msg('Unable to get workspace Id ')
                            .setIdentifier({ reason: 'Unable to get workspace Id' })
                            .setDomain(account.domain)
                            .setContext(this.constructor.name)
                            .setSubContext('bulkPutControlTests'),
                    );
                }
            }
        }

        if (exists) {
            await this.testEvidenceControlMapping(controlTestInstancesToMap, account);
            this._eventBus.publish(
                new ControlTestAddedEvent(account, requestUser, controls, controlTests),
            );
        }

        // Trigger OpenSearch reindexing for updated controls
        const workspaceId = account.getCurrentProduct()?.id;
        if (!isNil(workspaceId) && !isEmpty(requestDto.controlIds)) {
            this._eventBus.publish(
                new ControlsReindexEvent(account, workspaceId, requestDto.controlIds),
            );
        }

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, null, null),
        );
    }

    /**
     *
     * @param controlTestInstances
     * @param account
     */
    async testEvidenceControlMapping(
        controlTestInstances: ControlTestInstance[],
        account: Account,
    ) {
        const { id: product } = account?.getCurrentProduct();
        const functionName = this.testEvidenceControlMapping.name;

        const testEvidenceFlowFeatureFlagActive = await isTestEvidenceFlowFeatureFlagActive({
            logger: this.logger,
            account,
            featureFlagService: this.featureFlagService,
        });

        const testIdsToMap = controlTestInstances?.map(testInstance => testInstance?.testId);

        if (!testEvidenceFlowFeatureFlagActive || isNil(testIdsToMap) || isNil(account)) {
            this.logger.log(
                PolloAdapter.acct('Skip test evidence control map', account, functionName)
                    .setDomain(account?.domain)
                    .setIdentifier({ product })
                    .setContext(functionName),
            );
            return;
        }

        try {
            this.logger.log(
                PolloAdapter.acct(
                    `Trying to update controls from test evidences`,
                    account,
                    functionName,
                )
                    .setDomain(account?.domain)
                    .setIdentifier({ product })
                    .setContext(functionName),
            );
            await asyncForEach(testIdsToMap, async (testId: number) => {
                const controlMappingResponse =
                    await this.documentLibraryCoreService.testEvidenceWorkflowControlMapping(
                        testId,
                        account,
                    );

                this.logger.log(
                    PolloAdapter.acct('Controls check completed', account, functionName)
                        .setDomain(account?.domain)
                        .setIdentifier({
                            executedActions: controlMappingResponse,
                            testId,
                            product,
                        })
                        .setContext(functionName),
                );
            });
            this.logger.log(
                PolloAdapter.acct(
                    'Finished Control Test Evidence validation',
                    account,
                    functionName,
                )
                    .setDomain(account?.domain)
                    .setIdentifier({
                        product,
                    })
                    .setContext(functionName),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('Control Test Evidence validation failed', account)
                    .setDomain(account?.domain)
                    .setIdentifier({ testIdsToMap, product })
                    .setContext(functionName)
                    .setError(error),
            );
        }
    }

    /**
     *
     * @param account
     * @param requestUser
     * @param requestDto
     */
    @CacheBusterWithPrefix<void>({
        stores: [
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
            Caches.FIND_CONTROL_DETAILS_BY_ID,
        ],
    })
    async bulkDeleteControlTests(
        account: Account,
        requestUser: User,
        requestDto: ControlTestBulkRequestDto,
    ): Promise<void> {
        if (
            isNil(
                account.entitlements.find(
                    feat => feat.type === AccountEntitlementType.MAP_CONTROLS_TESTS,
                ),
            )
        ) {
            throw new UnauthorizedException(ErrorCode.CUSTOMER_UNAUTHORIZED);
        }

        const controls = await this.controlRepository.find({
            where: { id: In(requestDto.controlIds) },
            relations: ['controlTestInstances'],
        });

        let controlTests = await this.monitorsService.findControlTestInstances({
            where: { testId: In(requestDto.testIds) },
            relations: ['products'],
        });

        controlTests = filterControlTests(account, controlTests);
        this.fillInDescriptions(account, controls, controlTests);

        let exists = false;
        let controlTestInstancesToMap: ControlTestInstance[] = [];
        for (const control of controls) {
            const controlTestsToDelete = controlTests.filter(test =>
                includes(
                    control.controlTestInstances.map(cti => cti.testId),
                    test.testId,
                ),
            );

            const controlTestsToDeleteIds = controlTestsToDelete.map(test => test.id);

            if (controlTestsToDelete.length > 0) {
                control.controlTestInstances = differenceBy(
                    control.controlTestInstances,
                    controlTestsToDelete,
                    'testId',
                );

                exists = true;

                // eslint-disable-next-line no-await-in-loop
                await this.controlRepository.save(control, { reload: false });
                controlTestInstancesToMap = controlTestInstancesToMap.concat(controlTestsToDelete);

                const workspaceId = account.getCurrentProduct().id;

                if (!isNil(workspaceId)) {
                    // Trigger reindex for control tests update
                    this._eventBus.publish(
                        new ControlsReindexEvent(account, workspaceId, [control.id]),
                    );

                    // Trigger for Custom Workflow
                    this._eventBus.publish(
                        new ControlUnmappedFromTestWorkflowTriggerEvent(
                            account,
                            requestUser,
                            controlTestsToDeleteIds,
                            control.id,
                            workspaceId,
                        ),
                    );

                    // Only fire readiness event if at least one of the tests is not a draft
                    const hasNonDraftTests = controlTestsToDelete.some(test => !test.draft);
                    if (hasNonDraftTests) {
                        this._eventBus.publish(
                            new ControlReadinessEvent(
                                account,
                                control.id,
                                workspaceId,
                                requestUser,
                            ),
                        );
                    }

                    this._eventBus.publish(
                        new ControlsReindexEvent(account, workspaceId, [control.id]),
                    );
                } else {
                    this.logger.warn(
                        PolloMessage.msg('Unable to get workspace Id ')
                            .setIdentifier({ reason: 'Unable to get workspace Id' })
                            .setDomain(account.domain)
                            .setContext(this.constructor.name)
                            .setSubContext('bulkDeleteControlTests'),
                    );
                }
            }
        }

        if (exists) {
            await this.testEvidenceControlMapping(controlTestInstancesToMap, account);
            this._eventBus.publish(
                new ControlTestDeletedEvent(account, requestUser, controls, controlTests),
            );
        }

        // Trigger OpenSearch reindexing for updated controls
        const workspaceId = account.getCurrentProduct()?.id;
        if (!isNil(workspaceId) && !isEmpty(requestDto.controlIds)) {
            this._eventBus.publish(
                new ControlsReindexEvent(account, workspaceId, requestDto.controlIds),
            );
        }

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, null, null),
        );
    }

    /**
     *
     * @param account
     * @param requestUser
     * @param requestDto
     */
    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    async bulkPutControlRisks(
        account: Account,
        requestUser: User,
        controlId: number,
        requestDto: ControlRiskBulkRequestDto,
    ): Promise<void> {
        const control = await this.controlRepository.findOneOrFail({
            where: { id: controlId },
            relations: ['riskVersions', 'riskVersions.risk'],
        });

        const risksToMap = await this.riskRepository.find({
            where: { riskId: In(requestDto.riskIds) },
            relations: ['currentVersion'],
        });

        const alreadyMappedRiskVersionIds = control.riskVersions.map(riskVersion => riskVersion.id);

        for (const risk of risksToMap) {
            if (alreadyMappedRiskVersionIds.includes(risk.currentVersion.id)) {
                throw new BadRequestException(
                    `Risk version with id: ${risk.currentVersion.id} is already mapped to control: ${control.code}`,
                );
            } else {
                control.riskVersions.push(risk.currentVersion);
                this._eventBus.publish(
                    new RiskMappedToControlEvent(
                        account,
                        requestUser,
                        risk.currentVersion,
                        control.name,
                        control.code,
                    ),
                );
            }
        }
        await this.controlRepository.save(control);

        const workspaceId = account.getCurrentProduct()?.id;
        if (!isNil(workspaceId)) {
            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [control.id]));
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param requestDto
     * @param files
     * @returns
     */
    @CacheBusterWithPrefix<Control>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async createControl(
        account: Account,
        user: User,
        requestDto: CreateControlRequestDto,
        files: UploadedFileType[],
    ): Promise<Control> {
        let savedControl: Control;
        const control = new Control();
        let policiesIds;
        control.name = requestDto.name;
        control.description = requestDto.description; // no need for replace as it's custom
        // TODO: Temporary fix for ENG-45412, may need to create a ticket/effort to remove controlNumber field all together.
        control.controlNumber = null;
        control.code = requestDto.code;
        if (
            !(
                await this.controlService.isAvailable({
                    code: requestDto.code,
                    name: requestDto.name,
                    workspaceId: account.getCurrentProduct().id,
                })
            ).available
        ) {
            throw new BadRequestException(ErrorCode[ErrorCode.DUPLICATE_CONTROL_CODE]);
        }

        if (!isNil(requestDto.activity)) {
            control.activity = requestDto.activity;
        }

        if (!isNil(requestDto.question)) {
            control.question = requestDto.question;
        }

        if (!isNil(requestDto.policyIds)) {
            control.policies = await this.policiesCoreService.getPoliciesByIds(
                requestDto.policyIds,
            );
            policiesIds = control.policies.map(policy => policy.id);
        }

        if (!isNil(requestDto.requirementIds)) {
            control.requirements = await this.requirementRepository.getRequirementsById(
                requestDto.requirementIds,
            );

            if (isEmpty(control.requirements)) {
                throw new BadRequestException(ErrorCode[ErrorCode.REQUIREMENT_DOES_NOT_EXIST]);
            }
            const archivedRequirements = control.requirements.filter(
                requirement => requirement.archivedAt,
            );
            if (control.requirements.length === archivedRequirements.length) {
                control.archivedAt = new Date();
            }
        }

        let ownersToAdd = [];

        if (!isNil(requestDto.owners)) {
            // Validate owners if provided
            if (!isNil(requestDto.owners)) {
                ownersToAdd = await this.usersCoreService.getUsersByIds(requestDto.owners);
            }

            ownersToAdd.forEach(newOwner => {
                if (
                    !hasRole(newOwner, [
                        Role.ADMIN,
                        Role.TECHGOV,
                        Role.WORKSPACE_ADMINISTRATOR,
                        Role.CONTROL_MANAGER,
                    ])
                ) {
                    throw new BadRequestException(
                        'User must be ADMIN, TECHGOV, CONTROL_MANAGER or WORKSPACE ADMIN',
                    );
                }
            });
        }

        const product = await this.workspacesCoreService.getProductForAccount(
            account,
            getProductId(account),
        );

        control.products = [product];
        control.lastUpdatedBy = null;
        control.enabledAt = new Date();
        control.externalEvidence = [];

        if (!isNil(files)) {
            const isScannable = scannable(...files);
            if (isScannable) {
                throw new ConflictException('Cannot scan this file', ErrorCode.FILES_NOT_SCANNABLE);
            }
            const useFiles = [];

            files.forEach(file => {
                const newFile = clone(file) as any;

                const foundMetadata = requestDto.externalEvidenceMetadata.find(
                    metadata => newFile.originalname === metadata.originalFile,
                );

                if (!isNil(foundMetadata)) {
                    newFile.creationDate = foundMetadata.creationDate;
                    newFile.description = foundMetadata.description;
                    newFile.filename = foundMetadata.filename;
                    newFile.renewalDate = foundMetadata.renewalDate;
                    newFile.renewalScheduleType =
                        RenewalScheduleType[foundMetadata.renewalScheduleType];
                    useFiles.push(newFile);
                }
            });

            try {
                const uploadedFiles = [];

                for (const file of useFiles) {
                    // eslint-disable-next-line no-await-in-loop
                    await checkIsDocumentOrImage(file);
                }

                await Promise.all(
                    useFiles.map(async file => {
                        const uploadedFile = await this.uploadExternalEvidenceHelper(
                            file,
                            account,
                            file.filename,
                        );
                        file.key = uploadedFile.key;
                        file.fileName = uploadedFile.fileName;
                        uploadedFiles.push(file);
                    }),
                );

                savedControl = await this.controlRepository.save(control);

                const { documentReferenceIds, externalEvidenceMetadata } = requestDto;
                if (!isEmpty(documentReferenceIds)) {
                    const documentReferenceFiles =
                        await this.scannerUploaderService.getProcessByIds(documentReferenceIds);
                    if (isEmpty(documentReferenceFiles)) {
                        throw new ConflictException(
                            'Scan process not found',
                            ErrorCode.SCAN_PROCESS_NOT_FOUND,
                        );
                    }
                    documentReferenceFiles.forEach(documentReferenceFile => {
                        const { status, id, fileName, file } = documentReferenceFile;
                        if (status === ScannerUploaderStatus.OK) {
                            const metadata: any = externalEvidenceMetadata?.find(
                                externalMetadata => externalMetadata.scanResult?.id === id,
                            );
                            if (!isNil(metadata)) {
                                metadata.renewalScheduleType =
                                    RenewalScheduleType[metadata.renewalScheduleType];
                                metadata.fileName = fileName;
                                metadata.key = file;
                                uploadedFiles.push(metadata);
                            } else {
                                throw new ConflictException(
                                    'Invalid metadata',
                                    ErrorCode.INVALID_SCAN_METADATA,
                                );
                            }
                        }
                    });
                }

                await Promise.all(
                    uploadedFiles.map(uploadedFile => {
                        return this.externalEvidenceService.saveEvidence(
                            uploadedFile.description,
                            user,
                            control,
                            uploadedFile.creationDate,
                            uploadedFile,
                            account,
                            uploadedFile.renewalDate,
                            uploadedFile.renewalScheduleType,
                        );
                    }),
                );
            } catch (ex) {
                if (ex?.code === ErrorCode.INVALID_SCAN_METADATA) {
                    throw new ConflictException(
                        'Invalid metadata',
                        ErrorCode.INVALID_SCAN_METADATA,
                    );
                }
                if (ex?.code === ErrorCode.SCAN_PROCESS_NOT_FOUND) {
                    throw new ConflictException(
                        'Scan process not found',
                        ErrorCode.SCAN_PROCESS_NOT_FOUND,
                    );
                }
                if (ex?.code === ErrorCode.FILE_FORMAT_MISMATCH) {
                    throw new ValidationException(
                        'Mismatch between file extension and file format',
                        ErrorCode.FILE_FORMAT_MISMATCH,
                    );
                }
                throw ex;
            }
        } else {
            savedControl = await this.controlRepository.save(control);
        }

        if (!isEmpty(requestDto.notes) && !isNil(savedControl)) {
            const { notes } = requestDto;
            const notesComment = notes.reverse();
            for (const noteComment of notesComment) {
                const noteRequestDto = new CreateControlNoteRequestDto();
                noteRequestDto.comment = noteComment;

                // eslint-disable-next-line no-await-in-loop
                await this.controlNotesOrchestrationService.createNote(
                    account,
                    user,
                    noteRequestDto,
                    savedControl.id,
                );
            }
        }

        if (!isNil(requestDto.owners)) {
            control.owners = (
                await this.putControlOwnerHelper(control.id, ownersToAdd, account, user)
            ).owners;
        }

        if (!isEmpty(requestDto.testIds)) {
            await this.bulkPutControlTests(account, user, {
                testIds: requestDto.testIds,
                controlIds: [control.id],
            });
        }

        if (!isNil(requestDto.reportIds)) {
            control.libraryDocuments = await this.documentLibraryCoreService.linkEvidenceToControl(
                account,
                user,
                control,
                requestDto.reportIds,
            );
        }

        if (!isNil(requestDto.externalEvidenceMetadata)) {
            for (const evidence of requestDto.externalEvidenceMetadata) {
                if (evidence.url?.length > 0 && isURL(evidence.url)) {
                    const externalEvidence = new ExternalEvidence();
                    externalEvidence.name = evidence.name;
                    externalEvidence.description = evidence.description;
                    externalEvidence.url = evidence.url;
                    externalEvidence.control = control;
                    externalEvidence.filedAt = new Date(evidence.creationDate).toISO8601String();
                    externalEvidence.file = null;
                    externalEvidence.user = user;
                    externalEvidence.renewalDate = evidence.renewalDate;
                    externalEvidence.renewalScheduleType =
                        RenewalScheduleType[evidence.renewalScheduleType];

                    control.externalEvidence.push(externalEvidence);

                    // eslint-disable-next-line no-await-in-loop
                    await this.externalEvidenceRepository.save(externalEvidence);

                    this._eventBus.publish(
                        new UploadExternalEvidenceEvent(
                            account,
                            user,
                            externalEvidence,
                            control,
                            null,
                        ),
                    );
                }
            }
        }

        this._eventBus.publish(new CreateControlEvent(account, user, control));
        if (!isNil(product)) {
            this._eventBus.publish(new ControlsReindexEvent(account, product.id, [control.id]));
        }

        if (!isEmpty(policiesIds)) {
            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlAttachedToPolicyWorkflowTriggerEvent(
                    account,
                    user,
                    policiesIds,
                    control.id,
                    product.id,
                ),
            );
            this._eventBus.publish(
                new ControlReadinessEvent(account, control.id, product.id, user),
            );
        }

        if (!isEmpty(control.requirements)) {
            const requirementIds = control.requirements.map(requirement => requirement.id);
            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlMappedToRequirementWorkflowTriggerEvent(
                    account,
                    user,
                    requirementIds,
                    control.id,
                    product.id,
                ),
            );
        }

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(
                account,
                !isNil(product.id) ? product.id : null,
                null,
            ),
        );

        // map external evidence data
        const externalEvidenceData: TriggerEventEvidenceData[] = [];
        if (!isEmpty(savedControl.externalEvidence)) {
            // map external evidence data
            for (const externalEvidence of savedControl.externalEvidence) {
                externalEvidenceData.push({
                    evidenceId: externalEvidence.id,
                    type: EvidenceType.EXTERNAL_EVIDENCE,
                });
            }
        }

        // library document evidence event is already triggered in the linkEvidenceToControl method above
        const sanitizedProduct = sanitizeProduct(product);
        if (!isEmpty(externalEvidenceData)) {
            this._eventBus.publish(
                new ControlAttachedToEvidenceWorkflowTriggerEvent(
                    account,
                    user,
                    externalEvidenceData,
                    savedControl.id,
                    sanitizedProduct.id,
                ),
            );
        }

        return control;
    }

    /**
     *
     * @param account
     * @param id
     * @returns
     */
    async downloadAllEvidence(
        account: Account,
        user: User,
        id: number,
        requestMetadata: ExtractedRequestProps,
        sendSnackNotificationOnWorkflow = false,
        auditorFrameworkId?: string,
        onlyEvidence = false,
        onlyPolicy = false,
    ): Promise<DownloaderPayloadType> {
        const { url, method, requestId } = requestMetadata;
        const control = await this.controlService.findControlById(id, account, ['products']);
        if (isNil(control)) {
            throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
        }

        this.logger.log(
            PolloAdapter.acct(`Downloading Evidence in Temporal`, account)
                .setIdentifier({
                    url,
                    method,
                    requestId,
                    controlId: id,
                    auditorFrameworkId,
                    onlyEvidence,
                    onlyPolicy,
                })
                .setSubContext('downloadAllEvidence'),
        );

        const temporalClient = await getTemporalClient(account.domain);

        if (sendSnackNotificationOnWorkflow) {
            // @ts-expect-error disabling ts until this is replaced with executeWorkflow
            return temporalClient.startWorkflow(grcEvidenceDownloadWorkflowV1, {
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [
                    {
                        account,
                        user,
                        id,
                        auditorFrameworkId,
                        onlyEvidence,
                        onlyPolicy,
                        sendSnackNotification: sendSnackNotificationOnWorkflow,
                        requestMetadata: { url, method, requestId },
                    },
                ],
                memo: { accountId: account.id, domain: account.domain },
            });
        }
        return temporalClient.executeWorkflow(grcEvidenceDownloadWorkflowV1, {
            taskQueue: config.get('temporal.taskQueues.temporal-default'),
            args: [
                {
                    account,
                    user,
                    id,
                    auditorFrameworkId,
                    onlyEvidence,
                    onlyPolicy,
                    sendSnackNotification: sendSnackNotificationOnWorkflow,
                    requestMetadata: { url, method, requestId },
                },
            ],
            memo: { accountId: account.id, domain: account.domain },
        });
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getEvidence
     * @param id Id of the control for which to retrieve associated evidence.
     * @param account The account on which the operation is being executed
     * @param audit An associated audit to the control for which we are requesting the evidence.
     * @returns An array of buffers for each of the types of evidence attached to the control.
     *
     * **WARNING This method might return undefined values in the middle of the array**
     *
     * The types of evidence returned by this method are the following:
     * - Policies attached to the control;
     * - Evidence Library files attached to the control;
     * - External Evidence files attached to the control;
     *
     * Previously the return type of this function was returning a arrays of arrays but due to the
     * casting made internally and the forced returned types this was not caught. This was fixed.
     */
    async getEvidence(
        id: number,
        account: Account,
        audit?: Audit,
        onlyEvidence = false,
        onlyPolicy = false,
    ): Promise<FileBufferType[]> {
        const evidenceBufferPromiseResults = await Promise.allSettled([
            onlyEvidence ? null : this.getPolicyVersionFile(id, account),
            onlyPolicy ? null : this.getEvidenceLibraryFiles(id, account, audit),
            onlyPolicy ? null : this.getExternalFiles(id, account),
        ]);

        const fulfilledEvidenceBufferPromises = evidenceBufferPromiseResults.filter(
            ({ status }) => status === 'fulfilled',
        ) as PromiseFulfilledResult<FileBufferType[]>[];

        return fulfilledEvidenceBufferPromises.map(({ value }) => value).flat();
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getAllControlsForReport
     * @param account
     * @param productId
     * @param requestDto
     * @returns
     */
    async getAllControlsForReport(
        account: Account,
        productId?: number | null,
        requestDto?: ControlsRequestDto | null,
    ): Promise<CsvDataSetType> {
        if (isNil(productId)) {
            productId = await this.workspacesCoreService.getPrimaryProductId();
        }

        let productFrameworks =
            await this.frameworksCoreService.getFrameworksEnabledByProduct(productId);

        if (!isNil(requestDto?.frameworkSlug)) {
            productFrameworks = !isEmpty(
                productFrameworks.filter(f => f.framework.slug == requestDto?.frameworkSlug),
            )
                ? productFrameworks.filter(f => f.framework.slug == requestDto?.frameworkSlug)
                : productFrameworks;
        }

        const productFrameworkIds = productFrameworks.map(framework => framework.frameworkId);
        let filteredControlIds = [];
        if (!isNil(requestDto)) {
            filteredControlIds = await this.getControlIds(account, requestDto);
        }

        const controls = await this.controlRepository.getDownloadAllControlsUnordered(
            productId,
            filteredControlIds,
        );

        controls.sort((a, b) =>
            new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' }).compare(
                a.code,
                b.code,
            ),
        );

        if (isEmpty(controls)) {
            throw new NotFoundException(ErrorCode.NO_CONTROLS_FOUND);
        }

        const isReadyMap = await this.controlReadyRepository.find();

        const frameworks =
            await this.frameworkRepository.getFrameworksWithPositiveInScopeControlCounts(
                productFrameworkIds,
            );

        const data = [];

        for (const control of controls) {
            const ctrl = reportDataTransformHelper(
                control,
                frameworks,
                account,
                isReadyMap,
                ISO27001AnnexAArray,
                ISO270012022ISMSArray,
                ISO270012022AnnexAArray,
            );
            data.push(ctrl);
        }
        const filename = `Controls-${moment().format('MMDDYYYY')}`;
        return {
            data,
            filename,
        };
    }
    /**
     *
     * @param account
     * @param user
     * @returns
     */
    async downloadControls(
        account: Account,
        user: User,
        requestDto: ControlsRequestDto,
    ): Promise<CsvDataSetType> {
        delete requestDto.limit;
        const { data, filename } = await this.getAllControlsForReport(
            account,
            getProductId(account),
            requestDto,
        );

        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }

        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

        if (hasCustomFieldsEnabled) {
            let controlListWithCustomFields: Control[] = data.map(c => c.control);

            controlListWithCustomFields =
                await this.customFieldsSubmissionsCSVService.getCustomFieldsAndSubmissionsColumnsForCSVControl(
                    controlListWithCustomFields,
                    account,
                );

            data.map(d => {
                return {
                    control: controlListWithCustomFields.find(c => c.id === d.control.id),
                    isReady: d.isReady,
                };
            });
        }

        const additionalData = await this.frameworkRepository.getCustomFrameworkNames();

        return {
            data,
            filename,
            additionalData,
        };
    }

    /**
     *
     * @param account
     * @param ownerId
     * @returns
     */
    async getExpiredEvidence(account: Account, ownerId: number): Promise<Control[]> {
        return this.controlRepository.getExpiredEvidence(account, ownerId);
    }

    /**
     *
     * @param account
     * @param ownerId
     * @returns
     */
    async getExpiringEvidence(account: Account, ownerId: number): Promise<Control[]> {
        return this.controlRepository.getExpiringEvidence(account, ownerId);
    }

    async getControlRequirementComparison(
        account: Account,
        workspaceId: number,
        controlId: number,
    ): Promise<ControlCompare> {
        const ret: ControlCompare = {
            userMappedRequirements: [],
            templateMappedRequirements: [],
            alignedRequirements: [],
        };
        let filteredRequirementTemplates: RequirementTemplate[] = [];

        const control = await this.controlRepository.findOne({
            where: { id: controlId },
            relations: [
                'requirements',
                'requirements.requirementIndex',
                'requirements.requirementIndex.framework',
            ],
        });
        if (isNil(control?.controlTemplateId)) {
            throw new BadRequestException('Custom Controls are not considered');
        }

        const enabledFrameworkTagsByWorkspace = (
            await this.frameworkRepository.getEnabledFrameworksByProductId(workspaceId)
        ).map(fr => fr.tag);

        const controlTemplate: ControlTemplate =
            await this.controlTemplateService.findOneOrFailWithRequirementTemplateRelations(
                control.controlTemplateId,
            );

        filteredRequirementTemplates = controlTemplate.requirementTemplates.filter(rT =>
            enabledFrameworkTagsByWorkspace.includes(
                rT.requirementIndexTemplate.frameworkTemplate.tag,
            ),
        );

        for (const req of control.requirements) {
            const isCustomRequirement =
                req.requirementIndex?.category === RequirementIndexCategory.CUSTOM;

            const foundReqTemplate = filteredRequirementTemplates.find(
                rT =>
                    rT.name === req.name &&
                    rT.requirementIndexTemplate.frameworkTemplate.tag ===
                        req.requirementIndex?.framework?.tag,
            );

            if (!isNil(foundReqTemplate)) {
                ret.alignedRequirements.push({
                    name: req.name,
                    description: req.description,
                    frameworkTag: req.requirementIndex?.framework?.tag,
                    isOutOfScope: !isNil(req.archivedAt),
                });
            } else {
                // custom requirements should not be displayed to unmap
                if (!isCustomRequirement) {
                    const frameworkTag = req.requirementIndex?.framework?.tag;
                    //filter out the requirements with frameworkTags that don't match the enabled frameworks.
                    if (enabledFrameworkTagsByWorkspace.includes(frameworkTag)) {
                        ret.userMappedRequirements.push({
                            name: req.name,
                            description: req.description,
                            frameworkTag: frameworkTag,
                            isOutOfScope: !isNil(req.archivedAt),
                        });
                    }
                }
            }
        }

        const requirements = await this.requirementRepository.find({
            where: {
                name: In(filteredRequirementTemplates.map(frt => frt.name)),
            },
            relations: [
                'requirementIndex',
                'requirementIndex.framework',
                'requirementIndex.framework.products',
            ],
        });

        for (const rT of filteredRequirementTemplates) {
            if (!control.requirements.some(r => r.name === rT.name)) {
                const requirement = requirements.find(
                    r =>
                        r.name === rT.name &&
                        r.requirementIndex?.framework?.tag ===
                            rT.requirementIndexTemplate?.frameworkTemplate.tag,
                );

                if (isNil(requirement)) {
                    // Log it for now
                    this.log(
                        'Some requirement templates are missing in tenant prior to control reset',
                        account,
                        {
                            controlId: control?.id,
                            requirement: rT.name,
                        },
                    );
                }
                const isRequirementOutOfScope = !isNil(requirement?.archivedAt);

                ret.templateMappedRequirements.push({
                    name: rT.name,
                    description: rT.description,
                    frameworkTag: rT.requirementIndexTemplate.frameworkTemplate.tag,
                    isOutOfScope: isRequirementOutOfScope,
                });
            }
        }
        return ret;
    }

    async getControlTestComparison(
        account: Account,
        workspaceId: number,
        controlId: number,
    ): Promise<ControlCompareTest> {
        const ret: ControlCompareTest = {
            userMappedTests: [],
            templateMappedTests: [],
            alignedTests: [],
        };

        const control = await this.controlRepository.findOne({
            where: { id: controlId },
            relations: ['controlTestInstances', 'products'],
        });
        if (isNil(control?.controlTemplateId)) {
            throw new BadRequestException('Custom Controls are not considered');
        }
        let foundProduct = false;
        for (const product of control.products) {
            if (product.id === workspaceId) {
                foundProduct = true;
                break;
            }
        }
        if (!foundProduct) {
            throw new BadRequestException('Control id does not match product id');
        }

        const controlTemplate: ControlTemplate =
            await this.controlTemplateService.findOneOrFailWithControlTestTemplateRelations(
                control.controlTemplateId,
            );

        this.fillInDescriptions(account, [control], control.controlTestInstances);

        for (const test of control.controlTestInstances) {
            const isCustomTest = isNil(test.controlTestTemplateId);

            const foundTestTemplate = controlTemplate.controlTestTemplates.find(
                cTT => cTT.id === test.controlTestTemplateId,
            );

            if (!isNil(foundTestTemplate)) {
                ret.alignedTests.push({
                    name: test.name,
                    description: test.description,
                    testId: test.testId,
                });
            } else {
                // custom tests should not be displayed to unmap
                if (!isCustomTest) {
                    ret.userMappedTests.push({
                        name: test.name,
                        description: test.description,
                        testId: test.testId,
                    });
                }
            }
        }

        const controlTests = await this.controlTestInstanceRepository.find({
            where: {
                controlTestTemplateId: In(controlTemplate.controlTestTemplates.map(ctt => ctt.id)),
            },
        });

        for (const cTT of controlTemplate.controlTestTemplates) {
            if (!control.controlTestInstances.some(cti => cti.name === cTT.name)) {
                const controlTest = controlTests.find(cT => cT.controlTestTemplateId === cTT.id);

                if (isNil(controlTest)) {
                    // Log it for now
                    this.log(
                        'Some control test templates are missing in tenant prior to control reset',
                        account,
                        {
                            controlId: control?.id,
                            controlTest: cTT.name,
                        },
                    );
                }

                ret.templateMappedTests.push({
                    name: cTT.name,
                    description: cTT.description.replace(/%s/g, account.companyName),
                    testId: cTT.testId,
                });
            }
        }
        return ret;
    }

    async getControlPolicyComparison(
        account: Account,
        workspaceId: number,
        controlId: number,
    ): Promise<ControlComparePolicy> {
        const ret: ControlComparePolicy = {
            userMappedPolicies: [],
            templateMappedPolicies: [],
            alignedPolicies: [],
        };

        const control = await this.controlRepository.findOne({
            where: { id: controlId },
            relations: ['policies', 'products'],
        });
        if (isNil(control?.controlTemplateId)) {
            throw new BadRequestException('Custom Controls are not considered');
        }
        let foundProduct = false;
        for (const product of control.products) {
            if (product.id === workspaceId) {
                foundProduct = true;
                break;
            }
        }
        if (!foundProduct) {
            throw new BadRequestException('Control id does not match product id');
        }

        const controlTemplate: ControlTemplate =
            await this.controlTemplateService.findOneOrFailWithPolicyTemplateRelations(
                control.controlTemplateId,
            );

        for (const policy of control.policies) {
            const isCustomPolicy = isNil(policy.templateId);
            const foundPolicyTemplate = controlTemplate.policyTemplates.find(
                p => p.id === policy.templateId,
            );
            if (!isNil(foundPolicyTemplate)) {
                ret.alignedPolicies.push({
                    name: policy.name,
                    description: policy.currentDescription,
                    templateId: foundPolicyTemplate.id,
                    isArchived: policy.policyStatus === PolicyStatus.ARCHIVED,
                    isReplaced: policy.policyStatus === PolicyStatus.REPLACED,
                });
            } else {
                if (!isCustomPolicy) {
                    ret.userMappedPolicies.push({
                        name: policy.name,
                        description: policy.currentDescription,
                        templateId: policy.templateId || null,
                        isArchived: policy.policyStatus === PolicyStatus.ARCHIVED,
                        isReplaced: policy.policyStatus === PolicyStatus.REPLACED,
                    });
                }
            }
        }

        const controlTemplatePolicyIds = controlTemplate.policyTemplates.map(p => p.id);

        if (isEmpty(controlTemplatePolicyIds)) {
            return ret;
        }
        const policies = await this.policyRepository.getPoliciesForActiveFrameworksForTemplateIds(
            controlTemplatePolicyIds,
            workspaceId,
        );

        for (const policyTemplate of controlTemplate.policyTemplates) {
            if (!control.policies.some(p => p.templateId === policyTemplate.id)) {
                const policy = policies.find(p => p.templateId === policyTemplate.id);

                if (isNil(policy)) {
                    // Do not map these policies to controls. This is possible because some
                    // policies are not mapped to enabled frameworks in the tenant.
                    this.log(
                        'Some policy templates are missing in tenant prior to control reset',
                        account,
                        {
                            controlId: control?.id,
                            policy: policyTemplate.name,
                        },
                    );
                    continue;
                }

                ret.templateMappedPolicies.push({
                    name: policyTemplate.name,
                    description: policyTemplate.description,
                    templateId: policyTemplate.id,
                    isArchived: policy.policyStatus === PolicyStatus.ARCHIVED,
                    isReplaced: policy.policyStatus === PolicyStatus.REPLACED,
                });
            }
        }

        return ret;
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getPolicyVersionFile
     */
    private async getPolicyVersionFile(
        id: number,
        account: Account,
    ): Promise<FileBufferType[] | void> {
        const policies = await this.getControlPolicies(id, this.unlimitedRequest);

        const policyVersions = policies.data.map(policy => policy.currentPublishedVersion());

        // filter out policies with no approved versions, ENG-25551
        const validPolicyVersionsToInclude = policyVersions.filter(policy => !isNil(policy));

        if (isEmpty(validPolicyVersionsToInclude)) {
            return;
        }

        const policyVersionIds = validPolicyVersionsToInclude.map(
            policyVersion => policyVersion.id,
        );

        // Could be with or without appendix
        const policyVersionsFiles = await tenantWrapper(account, () => {
            return this.policiesCoreService.getDownloadablePolicyVersionByIds(
                account,
                policyVersionIds,
            );
        });

        if (!isEmpty(policyVersionsFiles)) {
            const streams = await getStreamsFromPolicyVersionsFiles(
                policyVersionsFiles,
                this.downloader,
            );
            return mapFilesToPrefix(streams, FileNames.POLICIES);
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getEvidenceLibraryFiles
     */
    private async getEvidenceLibraryFiles(
        id: number,
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to audit
        auditorFramework?: Audit,
    ): Promise<FileBufferType[]> {
        let evidenceData: LibraryDocument[] = [];
        if (isNil(auditorFramework)) {
            const { data } = await this.getControlEvidenceLibrary(account, id, {
                page: 1,
                limit: undefined,
                excludeIds: undefined,
                excludeSourceless: true,
            } as ControlReportsRequestDto);
            evidenceData = data;
        } else {
            evidenceData = await this.getControlEvidenceWithinATimeFrame(
                id,
                this.unlimitedRequest,
                getAuditorFrameworkTimePeriodForEvidence(auditorFramework),
            );
        }

        if (!isEmpty(evidenceData)) {
            const control = await this.controlService.findControlById(id, account, ['products']);

            const directories = await this.getStreamDirectoryOfEachEvidence(evidenceData, control);

            const directory = directories.map(file => {
                return mapFilesToPrefix(file, FileNames.EVIDENCE_LIBRARY);
            });

            const filesArray = [];
            // Convert [][] to []
            directory.forEach(file => {
                file.forEach(piceOfFile => {
                    filesArray.push(piceOfFile);
                });
            });

            return filesArray;
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getStreamDirectoryOfEachEvidence
     */
    private async getStreamDirectoryOfEachEvidence(
        evidenceData: LibraryDocument[],
        control: Control,
    ): Promise<FileBufferType[][]> {
        const names = [];

        const folders = evidenceData.map(async evidence => {
            let streamsFiles = [];
            const formattedUrls = [];
            const files = [];
            names.push(sanitizeFileName(evidence.name));
            evidence.evidenceRenewalSchemas.forEach(renewalSchema => {
                if (renewalSchema.libraryDocumentVersion.type === LibraryDocumentVersionType.URL) {
                    formattedUrls.push({
                        name: evidence.name,
                        createdAt: renewalSchema.libraryDocumentVersion.filedAt,
                        renewalDate: renewalSchema.renewalDate,
                        url: renewalSchema.libraryDocumentVersion.source,
                        description: isNil(evidence?.description) ? '' : evidence.description,
                    });
                } else {
                    files.push(renewalSchema.libraryDocumentVersion);
                }
                return renewalSchema.libraryDocumentVersion;
            });

            if (!isEmpty(files)) {
                streamsFiles = await getStreamsFromFiles(
                    files.map(file => ({ file: file.source })),
                    this.downloader,
                    true,
                );
            }

            if (!isEmpty(formattedUrls)) {
                streamsFiles.push(
                    await getCSVCustomStream(
                        formattedUrls,
                        `Controls-${control.code}-URL-evidence-library-${moment().format(
                            'MMDDYYYY',
                        )}.csv`,
                    ),
                );
            }

            return streamsFiles;
        });

        const allStreams = await Promise.all(folders);

        //Create a directory for each evidence
        return allStreams.map((streamEvidence, index) => {
            return mapFilesToPrefix(streamEvidence, `${names[index]}`);
        });
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getExternalFiles
     */
    private async getExternalFiles(id: number, account: Account): Promise<FileBufferType[]> {
        const control = await this.controlService.findControlById(id, account, ['products']);
        let externalEvidence = (await this.getControlEvidence(id, this.unlimitedRequest)).data;
        externalEvidence = externalEvidence.map(evidence => {
            return {
                ...evidence,
                description: evidence.description ?? '',
            } as ExternalEvidence;
        });
        if (!isEmpty(externalEvidence)) {
            const files = externalEvidence.filter(ee => {
                return !isNil(ee.file);
            });
            const urls = externalEvidence.filter(ee => {
                return !isNil(ee.url);
            });
            let streams = [];
            if (!isEmpty(files)) {
                streams = await getStreamsFromFiles(files, this.downloader, true);
            }
            if (!isEmpty(urls)) {
                streams.push(
                    await getCSVStream(
                        urls,
                        `Controls-${control.code}-URL-miscellaneous-evidence-${moment().format(
                            'MMDDYYYY',
                        )}.csv`,
                    ),
                );
            }
            return mapFilesToPrefix(streams, FileNames.MISCELLANEOUS_EVIDENCE);
        }
    }

    private getControlNumber(code: string): number | null {
        const matches = code.match(/\d+/g);
        if (!isEmpty(matches)) {
            return parseInt(matches[0]);
        }
        return null;
    }

    private async uploadExternalEvidenceHelper(
        file: any,
        account: Account,
        filename: string,
    ): Promise<UploaderPayloadType> {
        const extension = getFileExtension(file.originalname);
        if (!isNil(filename)) {
            // if the user added the name on the drawer, add the extension and store it
            file.originalname = `${filename}${extension}`;
        } else {
            // if the user did not add the name on the drawer check if is needed to truncate
            // if is not needed to truncate, let the originalname of the file as it's
            if (file?.originalname.length > config.get('validation.maxVarcharText')) {
                const truncateName = file.originalname.substring(
                    0,
                    config.get('validation.maxVarcharText') - extension.length,
                );
                file.originalname = `${truncateName}${extension}`;
            }
        }

        return this.uploader.uploadPrivateFile(file, UploadType.EVIDENCE, account.id);
    }

    private editSection(controls: Control[]) {
        let noSection = false;
        controls.some(control => {
            if (noSection) return true;
            control.requirements.some(requirement => {
                if (
                    !isNil(requirement.requirementIndex.framework) &&
                    FrameworkWithSection.indexOf(requirement.requirementIndex.framework.slug) === -1
                ) {
                    noSection = true;
                    return true;
                }
            });
        });
        if (noSection) {
            controls.forEach(control => {
                control.domain = null;
                control.category = null;
            });
        }
    }

    private sortByCreatedAt(a, b) {
        if (a.createdAt === b.createdAt) {
            return 0;
        }
        return a.createdAt < b.createdAt ? 1 : -1;
    }

    private filterDisabledPolicies(policies: Policy[]): Policy[] {
        return policies.filter(policy => {
            let isEnabledPolicy = false;

            for (const framework of policy.frameworks) {
                if (!isNil(framework.enabledAt)) {
                    isEnabledPolicy = true;
                }
            }

            if (policy.frameworks.length == 0) {
                isEnabledPolicy = true;
            }

            return isEnabledPolicy;
        });
    }

    private async buildControlForAnalyticsEvent(
        account: Account,
        control: Control,
    ): Promise<ControlEvidences> {
        const controlForEvent = cloneDeep(control);
        const id = control.id;

        const controlWithTests = await this.controlRepository.findOneOrFailIgnoreUpcoming(id);

        const [requirements, policies, reports, owners, externalEvidenceForEvent] =
            await Promise.all([
                this.requirementRepository.getControlRequirements(id, {
                    limit: 10,
                    page: 1,
                } as MappedRequirementsRequestDto),
                this.policiesCoreService.getControlPolicies(id, {
                    limit: 10,
                    page: 1,
                } as ControlPoliciesRequestDto),
                this.documentLibraryCoreService.getControlEvidence(account, id, {
                    limit: 10,
                    page: 1,
                } as ControlReportsRequestDto),
                this.usersCoreService.getControlOwnersByControlId(
                    { limit: 50, page: 1 } as ControlOwnersRequestDto,
                    id,
                ),
                this.externalEvidenceRepository.getControlEvidence(id, {
                    limit: 10,
                    page: 1,
                } as ControlEvidenceRequestDto),
            ]);

        controlForEvent.requirements = requirements.data;
        controlForEvent.policies = policies.data;
        controlForEvent.owners = owners.data;
        controlForEvent.controlTestInstances = controlWithTests.controlTestInstances;
        controlForEvent.externalEvidence = externalEvidenceForEvent.data;

        return { control: controlForEvent, evidences: reports.data };
    }

    public getControlById(id: number): Promise<Control> {
        return this.controlRepository.findOneByOrFail({ id });
    }

    public getControlByIdWithOwners(id: number): Promise<Control> {
        return this.controlRepository.findOneOrFail({
            where: { id },
            relations: ['owners'],
        });
    }

    /**
     * @deprecated Use ControlService.getControlsByIdsWithOwners
     *
     * @param controlIds
     * @returns
     */
    public getControlsByIdsWithOwners(controlIds: number[]): Promise<Control[]> {
        return this.controlRepository.find({
            where: { id: In(controlIds) },
            relations: ['owners'],
        });
    }

    public getControlByIdWithOwnersAndApprovals(id: number): Promise<Control> {
        return this.controlRepository.findControlsWithOwnersAndCurrentApprovals(id);
    }

    getControlWithOwnerByExternalEvidenceId(externalEvidenceId: number): Promise<Control | null> {
        return this.controlRepository.findOne({
            where: { externalEvidence: { id: externalEvidenceId } },
            relations: ['owners'],
        });
    }

    /**
     * @deprecated Use ControlsOrchestrationService.enablePreExistingAccountConnectionControlTests
     *
     * @param account
     */
    async enablePreExistingAccountConnectionControlTests(account: Account): Promise<void> {
        if (!hasAssociatedProduct(account)) {
            return;
        }

        for (const providerType of accountConnectionProviderTypes()) {
            const connections =
                // eslint-disable-next-line no-await-in-loop
                await this.connectionsCoreService.getConnectionsByProviderType(
                    providerType,
                    account,
                );

            const providerTypeTasks = getAutopilotTaskTypesByProviderType(providerType);

            for (const accountConnection of connections) {
                // eslint-disable-next-line no-await-in-loop
                await this.monitorsService.enableControlTestInstancesByClientTypeForProduct(
                    account,
                    providerTypeTasks,
                    accountConnection,
                );
            }
        }
    }

    /**
     *
     * @param account
     * @param controls
     * @param tests
     */
    private fillInDescriptions(
        account: Account,
        controls: Control[],
        tests: ControlTestInstance[],
    ): void {
        for (const test of tests) {
            test.description = test.description.replace(/%s/g, account.companyName);
            for (const monitor of test.monitorInstances) {
                monitor.remedyDescription = replace(
                    monitor.remedyDescription,
                    /%s/g,
                    account.companyName,
                );
                monitor.requestDescriptions = replace(
                    monitor.requestDescriptions,
                    /%s/g,
                    account.companyName,
                );
                monitor.failedTestDescription = replace(
                    monitor.failedTestDescription,
                    /%s/g,
                    account.companyName,
                );
                monitor.evidenceCollectionDescription = replace(
                    monitor.evidenceCollectionDescription,
                    /%s/g,
                    account.companyName,
                );
            }
        }
        for (const control of controls) {
            control.description = control.description.replace(/%s/g, account.companyName);
            for (const test of control.controlTestInstances) {
                test.description = test.description.replace(/%s/g, account.companyName);
                for (const monitor of test.monitorInstances) {
                    if (test.source !== TestSource.CUSTOM) {
                        monitor.remedyDescription = replace(
                            monitor.remedyDescription,
                            /%s/g,
                            account.companyName,
                        );
                        monitor.requestDescriptions = replace(
                            monitor.requestDescriptions,
                            /%s/g,
                            account.companyName,
                        );
                        monitor.failedTestDescription = replace(
                            monitor.failedTestDescription,
                            /%s/g,
                            account.companyName,
                        );
                        monitor.evidenceCollectionDescription = replace(
                            monitor.evidenceCollectionDescription,
                            /%s/g,
                            account.companyName,
                        );
                    }
                }
            }
        }
    }

    /**
     * Get custom tasks related to a given control
     * @param controlId Control ID
     * @param dto Control Custom Task Request DTO (paginated)
     * @returns List of custom tasks related to the control
     */
    async listMappedCustomTasks(
        account: Account,
        controlId: number,
        dto: ControlCustomTasksRequestDto,
        user: User,
    ): Promise<PaginationType<CustomTask>> {
        try {
            const tasks = await this.controlRepository.getMappedTasks(controlId, dto, user);
            const queryUserTasks = await this.getTenantRepository(CustomTask)
                .find({
                    where: { id: In(tasks.data.map(t => t.id)) },
                    select: { assignedToMany: true },
                    relations: ['assignedToMany'],
                })
                .then(t => t.map(ta => pick(ta, ['assignedToMany', 'id'])));

            tasks.data = tasks.data.map(ot => {
                ot.assignedToMany = queryUserTasks.find(qt => qt.id === ot.id)?.assignedToMany;

                return ot;
            });

            return tasks;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'Something went wrong retrieving data from custom tasks.',
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.listMappedCustomTasks.name)
                    .setError(error),
            );
            throw error;
        }
    }

    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    async resetControlRequirementMappings(
        account: Account,
        user: User,
        controlId: number,
    ): Promise<void> {
        try {
            if (!isPositiveInteger(Number(controlId)))
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);

            const control = await this.controlRepository.findOneOrFail({
                where: {
                    id: controlId,
                },
                relations: ['requirements', 'requirements.requirementIndex', 'products'],
            });

            // control is a custom control
            if (isNil(control.controlTemplateId)) {
                // we cannot reset custom controls because they are created by the customer
                throw new ConflictException(
                    'Cannot reset requirement mappings for Custom Control',
                    ErrorCode.CONTROL_IS_CUSTOM_CANNOT_RESET_REQUIREMENT_MAPPINGS,
                );
            }

            // determine the workspace the control operates in
            const workspaceId = control.products[0]?.id;
            if (isNil(workspaceId)) {
                // the control does not belong to a workspace/product
                throw new ConflictException(
                    'Control is missing workspace info',
                    ErrorCode.CONFLICT_CONTROL_NEEDS_WORKSPACE,
                );
            }

            const controlTemplate = await this.controlTemplateService.findControlTemplateByCode(
                control.code,
            );

            // I need sets of requirement names grouped by their framework tags
            const requirementMap = new Map();

            controlTemplate.requirementTemplates.forEach(requirementTemplate => {
                // extract framework tag from the requirement template
                const frameworkTag =
                    requirementTemplate.requirementIndexTemplate.frameworkTemplate.tag;

                if (!requirementMap.has(frameworkTag)) {
                    // add index to map if necessary
                    requirementMap.set(frameworkTag, []);
                }

                // add requirement name to the map
                requirementMap.set(
                    frameworkTag,
                    [requirementTemplate.name].concat(requirementMap.get(frameworkTag)),
                );
            });

            const promises = [];
            requirementMap.forEach((value, key, map) => {
                // find the requirements in the correct framework and workspace
                promises.push(
                    this.requirementRepository.getRequirementsByNameAndFrameworkAndWorkspace(
                        map.get(key),
                        key,
                        workspaceId,
                    ),
                );
            });

            const requirements = await Promise.all(promises);

            const requirementsToMapToControl = requirements.flat();

            const customRequirements = control.requirements.filter(
                requirement =>
                    requirement?.requirementIndex?.category === RequirementIndexCategory.CUSTOM,
            );
            // update mappings and save
            control.requirements = requirementsToMapToControl.concat(customRequirements);
            await this.controlRepository.save(control);

            // Trigger reindex for control requirements reset
            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }

            this._eventBus.publish(
                new MappedRequirementsResetOnControlEvent(account, user, control),
            );

            this._eventBus.publish(
                new IndexMonitorResultControlsAndFrameworksEvent(
                    account,
                    !isNil(workspaceId) ? workspaceId : null,
                    null,
                ),
            );
        } catch (err) {
            this.logger.error(PolloAdapter.acct(err.message, account).setError(err));

            throw err;
        }
    }

    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.FIND_CONTROL_DETAILS_BY_ID],
    })
    async resetControlTestMappings(account: Account, user: User, controlId: number): Promise<void> {
        try {
            if (!isPositiveInteger(Number(controlId)))
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);

            const control = await this.controlRepository.findOneOrFail({
                where: {
                    id: controlId,
                },
                relations: ['controlTestInstances', 'products'],
            });

            const totalControlInstances = cloneDeep(control.controlTestInstances);

            // control is a custom control
            if (isNil(control.controlTemplateId)) {
                // we cannot reset custom controls because they are created by the customer
                throw new ConflictException(
                    'Cannot reset test mappings for Custom Control',
                    ErrorCode.CONTROL_IS_CUSTOM_CANNOT_RESET_TEST_MAPPINGS,
                );
            }

            // determine the workspace the control operates in
            const workspaceId = control.products[0]?.id;
            if (isNil(workspaceId)) {
                // the control does not belong to a workspace/product
                throw new ConflictException(
                    'Control is missing workspace info',
                    ErrorCode.CONFLICT_CONTROL_NEEDS_WORKSPACE,
                );
            }

            const controlTemplate =
                await this.controlTemplateService.findOneOrFailWithControlTestTemplateRelations(
                    control.controlTemplateId,
                );

            const localTests = await this.controlTestInstanceRepository.find({
                where: {
                    controlTestTemplateId: In(
                        controlTemplate.controlTestTemplates.map(ctt => ctt.id),
                    ),
                },
            });

            for (const testTemplate of controlTemplate.controlTestTemplates) {
                if (isEmpty(localTests.find(lt => lt.controlTestTemplateId === testTemplate.id))) {
                    this.log(
                        `Test with name ${testTemplate.name} and testTemplateId ${testTemplate.id} not found, creating`,
                    );
                    const controlTestInstance =
                        this.createControlTestInstanceFromControlTestTemplate(
                            testTemplate,
                            control.products[0], // checked above
                        );
                    // eslint-disable-next-line no-await-in-loop
                    await this.controlTestInstanceRepository.save(controlTestInstance); // needed to create an id for mapping
                    localTests.push(controlTestInstance);
                    for (const monitorTemplate of testTemplate.monitorTemplates) {
                        const monitorInstance =
                            this.createMonitorInstanceFromMonitorTemplate(monitorTemplate);
                        monitorInstance.controlTestInstance = controlTestInstance;
                        // eslint-disable-next-line no-await-in-loop
                        await this.monitorInstanceRepository.save(monitorInstance);

                        for (const monitorTemplateCheckType of monitorTemplate.monitorTemplateCheckTypes) {
                            const monitorInstanceCheckType = new MonitorInstanceCheckType();
                            monitorInstanceCheckType.checkType = monitorTemplateCheckType.checkType;
                            monitorInstanceCheckType.monitorInstance = monitorInstance;

                            // eslint-disable-next-line no-await-in-loop
                            await this.monitorInstanceCheckTypeRepository.save(
                                monitorInstanceCheckType,
                            );
                            monitorInstance.monitorInstanceCheckTypes.push(
                                monitorInstanceCheckType,
                            );
                        }
                        controlTestInstance.monitorInstances.push(monitorInstance);
                    }
                }
            }

            // update mappings and save
            const customTests = control.controlTestInstances.filter(cti =>
                isNil(cti.controlTestTemplateId),
            );
            const drataTests = localTests.filter(lt =>
                controlTemplate.controlTestTemplates
                    .map(ctt => ctt.id)
                    .includes(lt.controlTestTemplateId),
            );
            control.controlTestInstances = customTests.concat(drataTests);
            await this.controlRepository.save(control);

            // Trigger reindex for control tests reset
            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }

            this._eventBus.publish(new MappedTestsResetOnControlEvent(account, user, control));

            this._eventBus.publish(
                new IndexMonitorResultControlsAndFrameworksEvent(
                    account,
                    !isNil(workspaceId) ? workspaceId : null,
                    null,
                ),
            );

            const controlTestInstancesToMap = mergeControlTestInstances(
                totalControlInstances,
                cloneDeep(control.controlTestInstances),
            );

            await this.testEvidenceControlMapping(controlTestInstancesToMap, {
                ...account,
                getCurrentProduct: () => ({ id: workspaceId }),
            } as unknown as Account);
        } catch (err) {
            this.logger.error(PolloAdapter.acct(err.message, account).setError(err));

            throw err;
        }
    }

    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS],
    })
    async resetControlPolicyMappings(
        account: Account,
        user: User,
        controlId: number,
    ): Promise<void> {
        try {
            if (!isPositiveInteger(Number(controlId)))
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);

            const control = await this.controlRepository.findOneOrFail({
                where: {
                    id: controlId,
                },
                relations: ['policies', 'products'],
            });

            // control is a custom control
            if (isNil(control.controlTemplateId)) {
                // we cannot reset custom controls because they are created by the customer
                throw new ConflictException(
                    'Cannot reset policy mappings for Custom Control',
                    ErrorCode.CONTROL_IS_CUSTOM_CANNOT_RESET_POLICY_MAPPINGS,
                );
            }

            // determine the workspace the control operates in
            const workspaceId = control.products[0]?.id;
            if (isNil(workspaceId)) {
                // the control does not belong to a workspace/product
                throw new ConflictException(
                    'Control is missing workspace info',
                    ErrorCode.CONFLICT_CONTROL_NEEDS_WORKSPACE,
                );
            }

            const controlTemplate =
                await this.controlTemplateService.findOneOrFailWithPolicyTemplateRelations(
                    control.controlTemplateId,
                );

            // Do not map policies without any framework mappings
            const globalMappedPolicies = controlTemplate.policyTemplates.map(p => p.id);
            let localPolicies: Policy[] = [];
            if (!isEmpty(globalMappedPolicies)) {
                localPolicies =
                    await this.policyRepository.getPoliciesForActiveFrameworksForTemplateIds(
                        globalMappedPolicies,
                        workspaceId,
                    );
            }

            // update mappings and save
            const customPolicies = control.policies.filter(p => isNil(p.templateId));
            const drataPolicies = localPolicies.filter(
                lt =>
                    !isNil(lt.templateId) &&
                    controlTemplate.policyTemplates.map(ctt => ctt.id).includes(lt.templateId),
            );
            control.policies = customPolicies.concat(drataPolicies);
            await this.controlRepository.save(control);

            // Trigger reindex for control policies reset
            if (!isNil(workspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, [control.id]),
                );
            }

            this._eventBus.publish(new MappedPoliciesResetOnControlEvent(account, user, control));

            // Reindex the control to reflect updated policy associations in search
            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [control.id]));

            this._eventBus.publish(
                new IndexMonitorResultControlsAndFrameworksEvent(
                    account,
                    !isNil(workspaceId) ? workspaceId : null,
                    null,
                ),
            );
        } catch (err) {
            this.logger.error(PolloAdapter.acct(err.message, account).setError(err));

            throw err;
        }
    }

    // @AUDIT-REFACTOR: TODO rename to getAudit and auditId
    private async getAuditorFramework(auditorFrameworkId: string): Promise<Audit> {
        // @AUDIT-REFACTOR: TODO rename to audit
        const auditorFramework: Audit = await this.auditorFrameworkRepository.findOne({
            where: {
                id: auditorFrameworkId,
            },
        });
        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${auditorFrameworkId} was not found`,
            );
        }
        return auditorFramework;
    }

    /**
     * @deprecated Use GrcCoreService.getFilteredControlsWithApprovalsByOwners
     *
     * @param controlIds
     * @param ownerIds
     * @returns
     */
    async getFilteredControlsWithApprovalsByOwners(
        controlIds: number[],
        ownerIds: number[],
    ): Promise<Control[]> {
        const controlsWithApprovals =
            await this.controlRepository.findControlsWithCurrentApprovals(controlIds);

        // Returns controls with approvals that are left without owners
        return controlsWithApprovals.filter(control =>
            control.owners.every(owner => ownerIds.includes(owner.id)),
        );
    }

    async getControlAndOwnersToSendControlEvidenceUpdateNotificationsByControlId(
        controlId: number,
        account: Account,
    ): Promise<{ control: Control; usersToSendNotification: User[] }> {
        const control = await this.getControlByIdWithOwners(controlId);

        const usersToSendNotification =
            await this.getControlOwnersToSendControlEvidenceUpdateNotifications(account, control);
        return {
            control,
            usersToSendNotification,
        };
    }

    /**
     * @deprecated Use NotificationsOrchestrationService.getControlOwnersToSendControlEvidenceUpdateNotifications
     *
     * @param account
     * @param control
     * @returns
     */
    async getControlOwnersToSendControlEvidenceUpdateNotifications(
        account: Account,
        control: Control,
    ): Promise<User[]> {
        const userIds = control.owners.map(owner => owner.id);

        if (isEmpty(userIds)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Control ${control.code} does not have owners. Skipping sending notification.`,
                    account,
                )
                    .setAccountId(account.id)
                    .setIdentifier('getControlOwnersToSendControlEvidenceUpdateNotifications'),
            );

            return [] as User[];
        }

        try {
            const usersToSendNotification =
                await this.usersCoreService.getUsersByIdsAndEnabledFeatureType(
                    userIds,
                    FeatureType.CONTROL_EVIDENCE_UPDATE_NOTIFICATION,
                );

            if (isEmpty(usersToSendNotification)) {
                this.logger.log(
                    PolloAdapter.acct(
                        // eslint-disable-next-line max-len
                        `Users with UserFeature of type ${FeatureType.CONTROL_EVIDENCE_UPDATE_NOTIFICATION} not found. Skipping sending notification.`,
                        account,
                    )
                        .setAccountId(account.id)
                        .setIdentifier('getControlOwnersToSendControlEvidenceUpdateNotifications'),
                );

                return [] as User[];
            }

            return usersToSendNotification;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'Error getting control owners able to receive control evidence update notifications',
                    account,
                )
                    .setError(error)

                    .setIdentifier({
                        name: 'getControlOwnersToSendControlEvidenceUpdateNotifications',
                        userIds: userIds,
                        control,
                    }),
            );

            return [] as User[];
        }
    }

    async getCustomControlsReportCsvDownload(account: Account): Promise<CsvDataSetType> {
        const filename = `Controls-Custom-Report-${moment().format('MMDDYYYY')}`;
        try {
            const controlsReportData = await this.controlRepository.getControlsReportData();

            if (isEmpty(controlsReportData)) {
                return { data: [], filename };
            }

            const controlIds = controlsReportData.map(control => control.id);

            const controlsReadyMap = await this.controlReadyRepository.find({
                where: { controlId: In(controlIds) },
            });

            const data = formatCustomControlReportData(controlsReportData, controlsReadyMap);

            return {
                data,
                filename,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`getCustomControlsReportCsvDownload error:`, account)
                    .setIdentifier({
                        error: JSON.stringify(error),
                    })
                    .setError(error),
            );
            throw error;
        }
    }

    async validateModifiedOwners(
        ownersIds: number[] | null | undefined,
        controlId: number,
    ): Promise<number[]> {
        const { owners: originalControlOwners } = await this.controlRepository.findOneOrFail({
            where: { id: controlId },
            relations: ['owners'],
        });
        let modifiedOwners: number[] = [];
        const originalOwnersIds = originalControlOwners.map(owner => owner.id);

        if (isNil(ownersIds) || isEmpty(ownersIds) || ownersIds.length === 0) {
            // If ownersIds is empty or null, all original owners are considered as modified
            modifiedOwners = originalOwnersIds;
        } else {
            // Add owners present in ownersIds but not in originalControlOwners (added owners)
            modifiedOwners = ownersIds.filter(ownerId => !originalOwnersIds.includes(ownerId));

            // Add owners present in originalOwnersIds but not ownersIds (removed owners)
            modifiedOwners = modifiedOwners.concat(
                originalOwnersIds.filter(ownerId => !ownersIds.includes(ownerId)),
            );
        }

        return modifiedOwners;
    }

    private createControlTestInstanceFromControlTestTemplate(
        controlTestTemplate: ControlTestTemplate,
        product: Product,
    ): ControlTestInstance {
        const controlTestInstance = new ControlTestInstance();
        controlTestInstance.controlTestTemplateId = controlTestTemplate.id;
        controlTestInstance.testId = controlTestTemplate.testId;
        controlTestInstance.name = controlTestTemplate.name;
        controlTestInstance.description = controlTestTemplate.description ?? '';
        controlTestInstance.checkResultStatus = CheckResultStatus.READY;
        controlTestInstance.priority = controlTestTemplate.priority;
        controlTestInstance.source = isNil(controlTestTemplate.source)
            ? TestSource.DRATA
            : controlTestTemplate.source;
        controlTestInstance.draft = false;
        controlTestInstance.autoEnabledAt = controlTestTemplate.autoEnabledAt;
        controlTestInstance.ap2EnabledAt = controlTestTemplate.ap2EnabledAt;
        controlTestInstance.runMode = controlTestTemplate.runMode;
        controlTestInstance.checkStatus = CheckStatus.ENABLED;
        controlTestInstance.controls = [];
        controlTestInstance.products = [product];
        controlTestInstance.monitorInstances = [];
        return controlTestInstance;
    }

    private createMonitorInstanceFromMonitorTemplate(
        monitorTemplate: MonitorTemplate,
    ): MonitorInstance {
        const monitorInstance = new MonitorInstance();
        monitorInstance.monitorTemplateId = monitorTemplate.id;
        monitorInstance.checkResultStatus = CheckResultStatus.READY;
        monitorInstance.checkFrequency = monitorTemplate.checkFrequency;
        monitorInstance.autopilotTaskType = monitorTemplate.autopilotTaskType;
        monitorInstance.requestDescriptions = monitorTemplate.requestDescriptions;
        monitorInstance.failedTestDescription = monitorTemplate.failedTestDescription;
        monitorInstance.evidenceCollectionDescription =
            monitorTemplate.evidenceCollectionDescription;
        monitorInstance.remedyDescription = monitorTemplate.remedyDescription;
        monitorInstance.url = monitorTemplate.url;
        monitorInstance.enabled = true;
        monitorInstance.monitorInstanceCheckTypes = [];
        return monitorInstance;
    }

    async getControlTestInstanceWithRecipe(
        product: Product,
        testId: number,
    ): Promise<ControlTestInstance> {
        return this.controlTestInstanceRepository.getControlTestInstanceWithDraftsAndParentForCloning(
            testId,
            product.id,
        );
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }
    private get index(): RequirementIndexViewRepository {
        return this.getCustomTenantRepository(RequirementIndexViewRepository);
    }
    private get controlIsReadyViewRepository(): ControlIsReadyViewRepository {
        return this.getCustomTenantRepository(ControlIsReadyViewRepository);
    }
    private get controlTicketViewRepository(): ControlTicketViewRepository {
        return this.getCustomTenantRepository(ControlTicketViewRepository);
    }
    private get controlFlagsViewRepository(): ControlFlagsViewRepository {
        return this.getCustomTenantRepository(ControlFlagsViewRepository);
    }
    private get requirementRepository(): RequirementRepository {
        return this.getCustomTenantRepository(RequirementRepository);
    }
    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }
    private get externalEvidenceRepository(): ExternalEvidenceRepository {
        return this.getCustomTenantRepository(ExternalEvidenceRepository);
    }
    private get companyRepository(): Repository<Company> {
        return this.getTenantRepository(Company);
    }
    private get controlReadyRepository(): Repository<ControlIsReadyView> {
        return this.getTenantRepository(ControlIsReadyView);
    }
    private get productRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }
    private get monitoringControlRepository(): TrustCenterMonitoringControlRepository {
        return this.getCustomTenantRepository(TrustCenterMonitoringControlRepository);
    }
    private get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }
    private get riskRepository(): RiskRepository {
        return this.getCustomTenantRepository(RiskRepository);
    }
    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }
    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }
    private get monitorInstanceCheckTypeRepository(): Repository<MonitorInstanceCheckType> {
        return this.getTenantRepository(MonitorInstanceCheckType);
    }

    private async isSecurityReportPerformanceEnabled(account: Account) {
        const isFFEnabled = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_SECURITY_REPORT_PERFORMANCE,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            account,
        );
        if (!isFFEnabled) {
            this.logger.log(
                PolloAdapter.acct(
                    'GET /controls/security-report endpoint optimizations Flag is disabled',
                    account,
                )
                    .setContext(this.getSecurityReport.name)
                    .setIdentifier({
                        flag: isFFEnabled,
                    }),
            );
        }
        return isFFEnabled;
    }
}
