import { ApprovalStatus, ErrorCode } from '@drata/enums';
import { NotFoundException } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { TestingModule } from '@nestjs/testing';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { ProductFrameworkService } from 'app/companies/products/services/product-framework.service';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlService } from 'app/control/control.service';
import { CustomFieldsSubmissionsCSVService } from 'app/custom-fields/services/custom-fields-submission-csv.service';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { EvidenceLibraryService } from 'app/document-library/evidence-library/services/evidence-library.service';
import { DocumentLibraryCoreService } from 'app/document-library/services/document-library-core.service';
import { EventsService } from 'app/events/events.service';
import { RequirementRepository } from 'app/frameworks/repositories/requirement.repository';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { Control } from 'app/grc/entities/control.entity';
import { GRCRoute } from 'app/grc/grc.routes';
import { grcConnectionMock } from 'app/grc/mocks/connection/grc-connection.mock';
import { listControlsMapping, mockControlRequestDto } from 'app/grc/mocks/control.mock';
import { ControlsReindexEvent } from 'app/grc/observables/events/controls-reindex.event';
import { MappedPoliciesResetOnControlEvent } from 'app/grc/observables/events/mapped-policies-reset-on-control.event';
import { GrcEvidenceDownloadOrchestrationService } from 'app/grc/orchestration/grc-evidence-download-orchestration.service';
import { ControlFlagsViewRepository } from 'app/grc/repositories/control-flags-view.repository';
import { ControlIsReadyViewRepository } from 'app/grc/repositories/control-is-ready-view.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ExternalEvidenceRepository } from 'app/grc/repositories/external-evidence.repository';
import { ControlApprovalsService } from 'app/grc/services/control-approvals.service';
import { ExternalEvidenceService } from 'app/grc/services/external-evidence.service';
import { GrcService } from 'app/grc/services/grc.service';
import { ControlModifyRequestType } from 'app/grc/types/control-modify-request.type';
import { IndexMonitorResultControlsAndFrameworksEvent } from 'app/monitors/observables/events/index-monitor-result-controls-and-frameworks.event';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { MonitorsService } from 'app/monitors/services/monitors.service';
import { ControlNotesOrchestrationService } from 'app/notes/services/control-notes-orchestration.service';
import { ScannerUploaderService } from 'app/scanner-uploader/services/scanner-uploader.service';
import { SecurityReportOrchestrationService } from 'app/security-report/security-report-orchestration.service';
import { StatsService } from 'app/stats/stats.service';
import { TrustCenterMonitoringControlRepository } from 'app/trust-center/repositories/monitoring-controls.repository';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { PoliciesService } from 'app/users/policies/services/policies.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { grcEvidenceDownloadWorkflowV1 } from 'app/worker/workflows';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { Account } from 'auth/entities/account.entity';
import { AccountsService } from 'auth/services/accounts.service';
import { CacheService } from 'cache/cache.service';
import { Role } from 'commons/enums/users/role.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import * as temporalConfig from 'commons/helpers/temporal/client-config.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { defaultRepositoryMock } from 'commons/mocks/repositories/default-repository.mock';
import { MockType } from 'commons/mocks/types/mock.type';
import config from 'config';
import * as databaseHelpers from 'database/typeorm/typeorm.extensions.helper';
import { Analytics } from 'dependencies/analytics/analytics';
import { Downloader } from 'dependencies/downloader/downloader';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { ControlTemplateService } from 'template/services/control-template.service';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { Repository } from 'typeorm';
import { factory, useSeeding } from 'typeorm-seeding';

const eventBusMock: Partial<EventBus> = {
    publish: jest.fn().mockResolvedValue(true),
};

jest.mock('commons/helpers/temporal/client', () => ({
    ...jest.requireActual('commons/helpers/temporal/client'),
    getTemporalClient: jest.fn().mockResolvedValue({
        executeWorkflow: jest.fn(),
    }),
}));

describe('Grc Service', (): void => {
    let grcService: GrcService;
    const userService = MockFactory.getMock(UsersCoreService);
    let productRepository: MockType<ProductRepository>;
    let controlRepository: MockType<ControlRepository>;
    let controlIsReadyViewRepository: MockType<Repository<ControlIsReadyView>>;
    let controlFlagsViewRepository: MockType<ControlFlagsViewRepository>;
    let featureFlagService: MockType<FeatureFlagService>;
    let grcEvidenceDownloadOrchestrationService: MockType<GrcEvidenceDownloadOrchestrationService>;
    let monitoringControlRepository: MockType<TrustCenterMonitoringControlRepository>;
    let externalEvidenceRepository: MockType<ExternalEvidenceRepository>;
    let requirementRepository: MockType<RequirementRepository>;

    const controlServiceMock = MockFactory.getMock(ControlService);
    const policiesCoreServiceMock = MockFactory.getMock(PoliciesCoreService);
    const documentLibraryCoreServiceMock = MockFactory.getMock(DocumentLibraryCoreService);
    const workspacesCoreServiceMock = MockFactory.getMock(WorkspacesCoreService);
    let mockTemporalClient;

    const accountForControlIssues = new Account();
    accountForControlIssues.id = 'test-account-id';
    accountForControlIssues.companyName = 'Test Company';

    beforeAll(async () => {
        mockTemporalClient = await getTemporalClient();
    });

    beforeEach(async () => {
        jest.spyOn(databaseHelpers, 'getCustomRepository').mockImplementation(
            grcConnectionMock.getCustomRepository,
        );

        featureFlagService = {
            evaluate: jest.fn(),
            evaluateAsTenant: jest.fn(),
        };

        grcEvidenceDownloadOrchestrationService = {
            getEvidenceDataFiles: jest.fn(),
            uploadEvidenceDataFiles: jest.fn(),
            getDownloadUrl: jest.fn(),
        };

        const module: TestingModule = await createAppTestingModule({
            providers: [
                GrcService,
                {
                    provide: ControlService,
                    useValue: controlServiceMock,
                },
                {
                    provide: MonitorsService,
                    useValue: {},
                },
                {
                    provide: EventsService,
                    useValue: {},
                },
                {
                    provide: Uploader,
                    useValue: {},
                },
                {
                    provide: PoliciesService,
                    useValue: { getControlPolicies: jest.fn() },
                },
                {
                    provide: UsersCoreService,
                    useValue: userService,
                },
                {
                    provide: FrameworksCoreService,
                    useValue: {},
                },
                {
                    provide: FrameworksCoreService,
                    useValue: {},
                },
                {
                    provide: Downloader,
                    useValue: {},
                },
                {
                    provide: EventBus,
                    useValue: eventBusMock,
                },
                {
                    provide: StatsService,
                    useValue: {},
                },
                {
                    provide: CacheService,
                    useValue: {},
                },
                {
                    provide: AccountsService,
                    useValue: {},
                },
                {
                    provide: WorkspacesCoreService,
                    useValue: workspacesCoreServiceMock,
                },
                {
                    provide: AuditorFrameworkRepository,
                    useValue: {},
                },
                {
                    provide: Analytics,
                    useValue: {},
                },

                {
                    provide: ExternalEvidenceService,
                    useValue: {},
                },
                {
                    provide: ConnectionsCoreService,
                    useValue: {},
                },
                {
                    provide: ProductFrameworkService,
                    useValue: {},
                },
                {
                    provide: EvidenceLibraryService,
                    useValue: {},
                },
                {
                    provide: SecurityReportOrchestrationService,
                    useValue: {},
                },
                { provide: ScannerUploaderService, useValue: {} },
                { provide: FeatureFlagService, useValue: featureFlagService },
                {
                    provide: CustomFieldsSubmissionsCSVService,
                    useValue: {},
                },
                { provide: ControlTemplateService, useValue: {} },
                { provide: ControlNotesOrchestrationService, useValue: {} },
                { provide: MonitoringSummaryIndexingService, useValue: {} },
                {
                    provide: GrcEvidenceDownloadOrchestrationService,
                    useValue: grcEvidenceDownloadOrchestrationService,
                },
                { provide: PoliciesCoreService, useValue: policiesCoreServiceMock },
                { provide: DocumentLibraryCoreService, useValue: documentLibraryCoreServiceMock },
                {
                    provide: FrameworksCoreService,
                    useValue: {
                        getFrameworksEnabledByProduct: jest.fn().mockResolvedValue([]),
                        getEnabledFrameworkByTag: jest.fn().mockResolvedValue(null),
                    },
                },
                { provide: ControlApprovalsService, useValue: {} },
            ],
        }).compile();
        grcService = module.get<GrcService>(GrcService);

        const tenancyContext = module.get<ReturnType<typeof TenancyContextMock>>(TenancyContext);

        productRepository = grcConnectionMock.getCustomRepository(ProductRepository);
        controlRepository = grcConnectionMock.getCustomRepository(ControlRepository);
        monitoringControlRepository = grcConnectionMock.getCustomRepository(
            TrustCenterMonitoringControlRepository,
        );
        externalEvidenceRepository = grcConnectionMock.getCustomRepository(
            ExternalEvidenceRepository,
        );
        requirementRepository = grcConnectionMock.getCustomRepository(RequirementRepository);
        controlIsReadyViewRepository = defaultRepositoryMock;
        controlFlagsViewRepository = {
            getControlFlags: jest.fn(),
        } as any;

        tenancyContext.getCustomRepository.mockImplementation((repository): Repository<any> => {
            switch (repository) {
                case ControlRepository:
                    return controlRepository;
                case ProductRepository:
                    return productRepository;
                case ControlIsReadyViewRepository:
                    return controlIsReadyViewRepository;
                case ControlFlagsViewRepository:
                    return controlFlagsViewRepository;
                case TrustCenterMonitoringControlRepository:
                    return monitoringControlRepository;
                case ExternalEvidenceRepository:
                    return externalEvidenceRepository;
                case RequirementRepository:
                    return requirementRepository;

                default:
                    return {} as Repository<any>;
            }
        });
    });

    afterEach(() => jest.clearAllMocks());

    describe('Controls with Product Added', () => {
        it('Should add product to controls', async () => {
            const mockControl = { id: 1 };
            const mockControlWithProduct = { ...mockControl, products: [1] };

            productRepository.getProductByControlId.mockResolvedValue(mockControlWithProduct);

            const controlWithProductAdded = await productRepository.getProductByControlId(
                mockControl.id,
            );

            expect(controlWithProductAdded).toEqual(mockControlWithProduct);
        });
    });

    describe('Public api GRC events', () => {
        it('Public api event should trigger when updating control owners', async () => {
            const mockAccount = new Account();
            mockAccount.companyName = 'companyName';
            const currentProduct = new Product();
            currentProduct.id = 1;
            mockAccount.setCurrentProduct(currentProduct);
            const mockControl = { id: 1 };
            const mockUser = new User();
            const mockReport = new LibraryDocument();
            const adminRole = new UserRole();
            adminRole.role = Role.ADMIN;
            mockUser.roles = [adminRole];
            const mockOwners = [mockUser];
            const mockReports = [mockReport];
            const mockOwnersIds = [1, 2];
            const mockControlWithProduct = { ...mockControl, products: [currentProduct.id] };

            userService.getUsersByIds = jest.fn().mockResolvedValue(mockOwners);

            controlRepository.findOneOrFail.mockResolvedValue({
                id: 1,
                description: 'description',
                owners: [{ id: 1 }, { id: 2 }],
            });

            productRepository.getProductByControlId.mockResolvedValue(mockControlWithProduct);

            grcService.buildControlForAnalyticsEvent = jest
                .fn()
                .mockResolvedValue({ control: mockControl, evidences: [] });

            const spyEventPublicApi = jest.spyOn(eventBusMock, 'publish');
            // Mock the getCurrentProductOfControl method
            workspacesCoreServiceMock.getCurrentProductOfControl = jest
                .fn()
                .mockResolvedValue(currentProduct);

            await grcService.putControlOwners(
                mockAccount,
                mockControl.id,
                { ownerIds: mockOwnersIds },
                mockReports,
                mockUser,
            );

            expect(spyEventPublicApi).toBeCalledTimes(4);
        });
    });

    describe('getControlOwnersToSendControlEvidenceUpdateNotifications', () => {
        const controlOwners = [
            {
                id: 1,
                email: '<EMAIL>',
            },
            {
                id: 2,
                email: '<EMAIL>',
            },
        ] as User[];
        const account = new Account();
        account.id = 'new-account-id';
        const control = new Control();
        control.id = 1;
        control.owners = controlOwners;

        it('should return users to send notification', async () => {
            userService.getUsersByIdsAndEnabledFeatureType = jest
                .fn()
                .mockResolvedValue(controlOwners);

            const result =
                await grcService.getControlOwnersToSendControlEvidenceUpdateNotifications(
                    account,
                    control,
                );

            expect(result).toEqual(controlOwners);
        });

        it('should return an empty array when control has no owners', async () => {
            const controlWithoutOwners = new Control();
            controlWithoutOwners.id = 2;
            controlWithoutOwners.code = 'DCF-1';
            controlWithoutOwners.owners = [];

            const result =
                await grcService.getControlOwnersToSendControlEvidenceUpdateNotifications(
                    account,
                    controlWithoutOwners,
                );

            expect(result).toEqual([]);
        });

        it('should return an empty array when no users found with the specified feature type', async () => {
            userService.getUsersByIdsAndEnabledFeatureType = jest.fn().mockResolvedValue([]);

            const result =
                await grcService.getControlOwnersToSendControlEvidenceUpdateNotifications(
                    account,
                    control,
                );

            expect(result).toEqual([]);
        });
    });

    describe('getControlAndOwnersToSendControlEvidenceUpdateNotificationsByControlId', () => {
        test('should return a control and a list of users with control evidence update notification turned on', async () => {
            await useSeeding({
                configName: './src/tests/ormconfig-unit-tests',
            });

            const getControlWithOwnersSpy = jest.spyOn(grcService, 'getControlByIdWithOwners');
            const getControlOwnersToSendNotificationSpy = jest.spyOn(
                grcService,
                'getControlOwnersToSendControlEvidenceUpdateNotifications',
            );
            const control = await factory(Control)().make();
            const controlOwner = await factory(User)().make();
            const account = await factory(Account)().make();
            control.owners = [controlOwner];
            getControlWithOwnersSpy.mockResolvedValue(control);
            getControlOwnersToSendNotificationSpy.mockResolvedValue([controlOwner]);

            const { control: controlResult, usersToSendNotification } =
                await grcService.getControlAndOwnersToSendControlEvidenceUpdateNotificationsByControlId(
                    1,
                    account,
                );

            expect(controlResult).toBe(control);
            expect(usersToSendNotification).toStrictEqual([controlOwner]);
        });
    });

    describe('resetControlPolicyMappings', () => {
        const account = new Account();
        const user = new User();
        const controlId = 1;
        const workspaceId = 1;

        beforeEach(() => {
            jest.clearAllMocks();
        });

        test('should validate input parameters', async () => {
            await expect(grcService.resetControlPolicyMappings(account, user, -1)).rejects.toThrow(
                NotFoundException,
            );
        });

        test('should publish correct events when resetting policy mappings', async () => {
            const control = new Control();
            control.id = controlId;
            control.controlTemplateId = 1; // Template-based control
            control.policies = [];
            control.products = [{ id: workspaceId } as Product];

            controlRepository.findOneOrFail?.mockResolvedValue(control);
            controlRepository.save?.mockResolvedValue(control);

            const eventBusSpy = jest.spyOn(eventBusMock, 'publish');

            const originalControlTemplateService = (grcService as any).controlTemplateService;

            (grcService as any).controlTemplateService = {
                findOneOrFailWithPolicyTemplateRelations: jest.fn().mockResolvedValue({
                    policyTemplates: [],
                } as any),
            };

            Object.defineProperty(grcService, 'policyRepository', {
                get: () => ({
                    getPoliciesForActiveFrameworksForTemplateIds: jest.fn().mockResolvedValue([]),
                }),
                configurable: true,
            });

            try {
                await grcService.resetControlPolicyMappings(account, user, controlId);

                expect(eventBusSpy).toHaveBeenCalledWith(
                    expect.any(MappedPoliciesResetOnControlEvent),
                );
                expect(eventBusSpy).toHaveBeenCalledWith(expect.any(ControlsReindexEvent));
                expect(eventBusSpy).toHaveBeenCalledWith(
                    expect.any(IndexMonitorResultControlsAndFrameworksEvent),
                );

                expect(eventBusSpy).toHaveBeenCalledTimes(4);

                const controlsReindexCall = eventBusSpy.mock.calls.find(
                    call => call[0] instanceof ControlsReindexEvent,
                );
                expect(controlsReindexCall).toBeDefined();
                if (controlsReindexCall) {
                    const event = controlsReindexCall[0] as ControlsReindexEvent;
                    expect(event.account).toBe(account);
                    expect(event.workspaceId).toBe(workspaceId);
                    expect(event.controlIds).toEqual([controlId]);
                }
            } finally {
                (grcService as any).controlTemplateService = originalControlTemplateService;
            }
        });
    });

    describe('getControlFindings', () => {
        const mockControlService = {
            findControlDetailsById: jest.fn(),
        };

        const mockControlApprovalsService = {
            listControlApprovals: jest.fn(),
        };

        beforeEach(() => {
            (grcService as any).controlService = mockControlService;
            (grcService as any).controlApprovalsService = mockControlApprovalsService;
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        test('should return empty object when controlIds array is empty', async () => {
            const result = await grcService.getControlFindings([], accountForControlIssues);

            expect(result).toEqual({});
        });

        test('should calculate control issues stats correctly with no approval needed', async () => {
            const controlIds = [1, 2];

            // Mock control details for control 1
            mockControlService.findControlDetailsById.mockResolvedValueOnce({
                policies: { notReady: [{ id: 1 }, { id: 2 }] }, // 2 not ready policies
                evidence: {
                    external: { notReady: [{ id: 1 }] }, // 1 not ready external evidence
                    library: { notReady: [{ id: 2 }, { id: 3 }] }, // 2 not ready library evidence
                },
                tests: { notReady: [{ id: 1 }] }, // 1 not ready test
            });

            // Mock control details for control 2
            mockControlService.findControlDetailsById.mockResolvedValueOnce({
                policies: { notReady: [] }, // 0 not ready policies
                evidence: {
                    external: { notReady: [] }, // 0 not ready external evidence
                    library: { notReady: [{ id: 4 }] }, // 1 not ready library evidence
                },
                tests: { notReady: [] }, // 0 not ready tests
            });

            // Mock no approval needed for both controls
            mockControlApprovalsService.listControlApprovals
                .mockResolvedValueOnce([]) // No approval for control 1
                .mockResolvedValueOnce([]); // No approval for control 2

            const result = await grcService.getControlFindings(controlIds, accountForControlIssues);

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 2,
                        unapprovedPoliciesIds: [{ id: 1 }, { id: 2 }],
                    },
                    evidence: 3, // 1 external + 2 library
                    tests: 1,
                    total: 6, // 2 + 3 + 1
                    approvals: 0,
                },
                2: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 1, // 0 external + 1 library
                    tests: 0,
                    total: 1, // 0 + 1 + 0
                    approvals: 0,
                },
            });

            expect(mockControlService.findControlDetailsById).toHaveBeenCalledTimes(2);
            expect(mockControlService.findControlDetailsById).toHaveBeenCalledWith(
                1,
                accountForControlIssues,
            );
            expect(mockControlService.findControlDetailsById).toHaveBeenCalledWith(
                2,
                accountForControlIssues,
            );
            expect(mockControlApprovalsService.listControlApprovals).toHaveBeenCalledTimes(2);
        });

        test('should include approval count when approval is needed', async () => {
            const controlIds = [1];

            mockControlService.findControlDetailsById.mockResolvedValueOnce({
                policies: { notReady: [{ id: 1 }] },
                evidence: {
                    external: { notReady: [] },
                    library: { notReady: [] },
                },
                tests: { notReady: [] },
            });

            // Mock approval needed (status = READY_FOR_REVIEWS)
            mockControlApprovalsService.listControlApprovals.mockResolvedValueOnce([
                { id: 1, status: ApprovalStatus.READY_FOR_REVIEWS },
            ]);

            const result = await grcService.getControlFindings(controlIds, accountForControlIssues);

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 1,
                        unapprovedPoliciesIds: [{ id: 1 }],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 2, // 1 policy + 1 approval
                    approvals: 1,
                },
            });
        });

        test('should not include approval count when approval exists but not ready for review', async () => {
            const controlIds = [1];

            mockControlService.findControlDetailsById.mockResolvedValueOnce({
                policies: { notReady: [] },
                evidence: {
                    external: { notReady: [] },
                    library: { notReady: [] },
                },
                tests: { notReady: [] },
            });

            // Mock approval exists but not ready for review
            mockControlApprovalsService.listControlApprovals.mockResolvedValueOnce([
                { id: 1, status: 0 }, // Different status, not READY_FOR_REVIEWS
            ]);

            const result = await grcService.getControlFindings(controlIds, accountForControlIssues);

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 0,
                    approvals: 0,
                },
            });
        });

        test('should handle Promise.allSettled correctly even if some promises fail', async () => {
            const controlIds = [1, 2];

            // Mock first control succeeds
            mockControlService.findControlDetailsById.mockResolvedValueOnce({
                policies: { notReady: [{ id: 1 }] },
                evidence: {
                    external: { notReady: [] },
                    library: { notReady: [] },
                },
                tests: { notReady: [] },
            });

            // Mock second control fails
            mockControlService.findControlDetailsById.mockRejectedValueOnce(
                new Error('Control not found'),
            );

            mockControlApprovalsService.listControlApprovals
                .mockResolvedValueOnce([]) // First control - no approval
                .mockResolvedValueOnce([]); // Second control - no approval

            const result = await grcService.getControlFindings(controlIds, accountForControlIssues);

            // Should still return result for the successful control
            expect(result).toEqual({
                1: {
                    policies: {
                        count: 1,
                        unapprovedPoliciesIds: [{ id: 1 }],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 1,
                    approvals: 0,
                },
            });
        });
    });

    describe('listControlsGroupBy', () => {
        const account = new Account();
        account.id = 'test-account-id';
        account.companyName = 'Test Company';

        const mockUser = new User();
        mockUser.id = 1;

        let mockGetPaginatedControlIds: jest.SpyInstance;
        let mockGetControlFindings: jest.SpyInstance;

        beforeEach(() => {
            jest.spyOn(featureFlagService, 'evaluateAsTenant').mockResolvedValue(true);
            mockGetPaginatedControlIds = jest.spyOn(grcService as any, 'getPaginatedControlIds');
            mockGetControlFindings = jest.spyOn(grcService as any, 'getControlFindings');

            // Mock the controlRepository and controlFlagsViewRepository
            controlRepository.getControlsFromIdsGroupBy = jest.fn();
            controlFlagsViewRepository.getControlFlags = jest.fn();

            // Mock workspacesCoreService
            workspacesCoreServiceMock.getPrimaryProduct = jest.fn().mockResolvedValue({ id: 1 });
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        test('should return paginated controls without issues when no controls found', async () => {
            mockGetPaginatedControlIds.mockResolvedValueOnce([[], 0]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce([]);

            const result = await grcService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result).toEqual({
                data: [],
                page: 1,
                limit: 10,
                total: 0,
            });

            expect(mockGetControlFindings).not.toHaveBeenCalled();
            expect(controlRepository.getControlsFromIdsGroupBy).toHaveBeenCalledWith(
                [],
                'name',
                'ASC',
            );
        });

        test('should return controls with issues field populated when controls are not ready', async () => {
            const { controlIds, mockControls, mockControlFlags, mockIssuesMapping } =
                listControlsMapping;

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 2]);
            controlRepository.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce(mockIssuesMapping);

            const result = await grcService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(2);

            // First control (not ready) should have issues
            expect(result.data[0]).toMatchObject({
                control: expect.objectContaining({
                    id: 1,
                    name: 'Control 1',
                    description: 'Test control Test Company description', // %s replaced
                }),
                hasEvidence: true,
                hasPolicy: true,
                hasOwner: true,
                isMonitored: false,
                isReady: false,
                hasTicket: false,
                issues: {
                    policies: 2,
                    evidence: 1,
                    tests: 1,
                    total: 4,
                    approvals: 0,
                },
            });

            // Second control (ready) should have issues but they should be empty/zero
            expect(result.data[1]).toMatchObject({
                control: expect.objectContaining({
                    id: 2,
                    name: 'Control 2',
                    description: 'Another test control Test Company description', // %s replaced
                }),
                hasEvidence: false,
                hasPolicy: false,
                hasOwner: false,
                isMonitored: false,
                isReady: true,
                hasTicket: false,
                issues: {
                    policies: 0,
                    evidence: 0,
                    tests: 0,
                    total: 0,
                    approvals: 0,
                },
            });
        });

        test('should not populate issues field when getControlFindings returns empty mapping', async () => {
            const controlIds = [1];
            const mockControls = [
                {
                    ...listControlsMapping.mockControls[0],
                },
            ];

            const mockControlFlags = [
                {
                    ...listControlsMapping.mockControlFlags[0],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({}); // Empty mapping

            const result = await grcService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(1);
            expect(result.data[0]).not.toHaveProperty('issues');
            expect(mockGetControlFindings).toHaveBeenCalledWith(controlIds, account);
        });

        test('should handle company name replacement in control descriptions', async () => {
            const controlIds = [1];
            const mockControls = [
                {
                    id: 1,
                    name: 'Control 1',
                    code: 'CTRL-001',
                    description: '%s must implement security controls for %s systems',
                    frameworkTags: [],
                    topics: [],
                },
            ];

            const mockControlFlags = [
                {
                    controlId: 1,
                    hasEvidence: true,
                    hasPolicy: true,
                    hasOwner: true,
                    isMonitored: false,
                    isReady: true,
                    hasTicket: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({});

            const result = await grcService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data[0].control.description).toBe(
                'Test Company must implement security controls for Test Company systems',
            );
        });

        test('should not include issues field when getControlIssuesFeature is false', async () => {
            jest.spyOn(featureFlagService, 'evaluateAsTenant').mockResolvedValue(false);
            const controlIds = [1];
            const mockControls = [
                {
                    id: 1,
                    name: 'Test Control',
                    description: 'Test Description',
                    isReady: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];
            const mockControlFlags = [
                {
                    controlId: 1,
                    hasEvidence: true,
                    hasPolicy: true,
                    hasOwner: true,
                    isMonitored: false,
                    isReady: false,
                    hasTicket: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({});

            const result = await grcService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(1);
            expect(result.data[0].control).not.toHaveProperty('issues');
            expect(mockGetControlFindings).not.toHaveBeenCalled();
        });
    });

    describe('getCustomControlsReportCsvDownload', () => {
        const account = new Account();

        test('should return empty data if there are no enabled controls found', async () => {
            controlRepository.getControlsReportData?.mockResolvedValueOnce([]);

            const { data } = await grcService.getCustomControlsReportCsvDownload(account);

            expect(data).toMatchObject([]);
        });

        test('should formatted control data for report if there are enabled controls found', async () => {
            const controls: Control[] = [
                {
                    id: 1,
                    code: 'ControlCode',
                    name: 'ControlName',
                    archivedAt: null,
                } as Control,
            ];

            const controlIsReadyData: ControlIsReadyView[] = [
                { controlId: 1, isReady: true, hasEvidence: true } as ControlIsReadyView,
            ];

            controlRepository.getControlsReportData?.mockResolvedValueOnce(controls);
            controlIsReadyViewRepository.find?.mockResolvedValueOnce(controlIsReadyData);

            const { data } = await grcService.getCustomControlsReportCsvDownload(account);

            expect(data).toHaveLength(1);
        });

        test('should throw an error if something goes wrong', async () => {
            controlRepository.getControlsReportData?.mockRejectedValue(new Error('Error'));

            await expect(grcService.getCustomControlsReportCsvDownload(account)).rejects.toThrow(
                new Error('Error'),
            );
        });
    });

    describe('downloadAllEvidence', () => {
        const controlId = 284;
        const mockControl = new Control();

        beforeEach(() => {
            mockControl.id = controlId;
            mockControl.name = 'Test Control';
            mockControl.code = 'DCF-284';
        });

        it('should run as a workflow if waas is enabled and GRC_DOWNLOAD_EVIDENCE_URL_LIST includes the given path', async () => {
            const mockAccount = new Account();
            const mockUser = new User();
            const requestMetadata = {
                url: '',
                method: '',
                path: GRCRoute.GET_DOWNLOAD_ALL_EVIDENCE,
            };

            // Mock control exists
            controlServiceMock.findControlById?.mockResolvedValueOnce(mockControl);

            jest.spyOn(temporalConfig, 'isTemporalEnabled').mockReturnValueOnce(true);
            featureFlagService.evaluate.mockReset().mockResolvedValueOnce(true);

            await grcService.downloadAllEvidence(mockAccount, mockUser, controlId, requestMetadata);

            expect(controlServiceMock.findControlById).toHaveBeenCalledWith(
                controlId,
                mockAccount,
                ['products'],
            );
            expect(mockTemporalClient.executeWorkflow).toHaveBeenCalledWith(
                grcEvidenceDownloadWorkflowV1,
                expect.objectContaining({
                    taskQueue: config.get('temporal.taskQueues.temporal-default'),
                    args: [
                        expect.objectContaining({
                            account: mockAccount,
                            user: mockUser,
                            id: controlId,
                        }),
                    ],
                }),
            );
        });

        it('should run as a workflow even if waas is disabled', async () => {
            const mockAccount = new Account();
            const mockUser = new User();
            const requestMetadata = {
                url: '',
                method: '',
                path: GRCRoute.GET_DOWNLOAD_ALL_EVIDENCE,
            };

            // Mock control exists
            controlServiceMock.findControlById?.mockResolvedValueOnce(mockControl);

            jest.spyOn(temporalConfig, 'isTemporalEnabled').mockReturnValueOnce(false);

            const result = await grcService.downloadAllEvidence(
                mockAccount,
                mockUser,
                controlId,
                requestMetadata,
            );

            expect(controlServiceMock.findControlById).toHaveBeenCalledWith(
                controlId,
                mockAccount,
                ['products'],
            );
            expect(mockTemporalClient.executeWorkflow).toHaveBeenCalledWith(
                grcEvidenceDownloadWorkflowV1,
                expect.objectContaining({
                    taskQueue: config.get('temporal.taskQueues.temporal-default'),
                    args: [
                        expect.objectContaining({
                            account: mockAccount,
                            user: mockUser,
                            id: controlId,
                        }),
                    ],
                }),
            );
            expect(result).toBeUndefined();
        });

        it('should throw NotFoundException when control does not exist', async () => {
            const mockAccount = new Account();
            const mockUser = new User();
            const requestMetadata = {
                url: '',
                method: '',
                path: GRCRoute.GET_DOWNLOAD_ALL_EVIDENCE,
            };

            // Mock control does not exist
            controlServiceMock.findControlById?.mockResolvedValueOnce(null);

            await expect(
                grcService.downloadAllEvidence(mockAccount, mockUser, controlId, requestMetadata),
            ).rejects.toThrow(new NotFoundException(ErrorCode.CONTROL_NOT_FOUND));

            expect(controlServiceMock.findControlById).toHaveBeenCalledWith(
                controlId,
                mockAccount,
                ['products'],
            );
            expect(mockTemporalClient.executeWorkflow).not.toHaveBeenCalled();
        });
    });

    describe('modifyControl', () => {
        describe('Given an account, user, control Id, and control modify request', () => {
            const controlId = 1;
            let account: Account;
            let user: User;
            let controlModifyRequestType: ControlModifyRequestType;
            let control: Control;
            const originalControlDescription =
                `%s's has implemented automated` +
                ` edit checks in the system to limit input to defined value ranges and formats.`;

            beforeEach(() => {
                account = new Account();
                account.companyName = 'Drata';

                const product = new Product();
                product.id = 1;
                account.setCurrentProduct(product);

                control = new Control();
                control.code = 'DCF-32';
                control.id = 1;
                control.description = originalControlDescription.replace('%s', account.companyName);
                controlServiceMock.findControlById?.mockResolvedValue(control);
                controlRepository.getUnfilledControlDescriptionByControlId?.mockResolvedValue(
                    originalControlDescription,
                );
                controlServiceMock.findControlWithEvidenceByIdOrFail?.mockResolvedValue({
                    control,
                    hasEvidence: false,
                    isReady: false,
                    hasTicket: false,
                    isMonitored: false,
                    hasPolicy: false,
                });
                monitoringControlRepository.findOne?.mockResolvedValue(null);

                requirementRepository.getControlRequirements?.mockResolvedValue([]);
                policiesCoreServiceMock.getControlPolicies?.mockResolvedValue([]);
                documentLibraryCoreServiceMock.getControlEvidence?.mockResolvedValue([]);
                userService.getControlOwnersByControlId?.mockResolvedValue([]);
                externalEvidenceRepository.getControlEvidence?.mockResolvedValue({ data: [] });
                controlRepository.findOneOrFailIgnoreUpcoming?.mockResolvedValue(control);
                user = new User();
                controlModifyRequestType = {
                    name: 'Drata Control',
                    description: `Drata's has implemented automated edit checks in the system to limit input to defined value ranges and formats.`,
                };
            });

            describe('Given that there are no modifications to the description', () => {
                it('Should save the description into the control without modifications', async () => {
                    controlRepository.update?.mockImplementation(input => input);
                    await grcService.modifyControl(
                        account,
                        user,
                        controlId,
                        controlModifyRequestType,
                    );
                    expect(controlRepository.update).toHaveBeenCalledWith(
                        { id: 1 },
                        expect.objectContaining({ description: originalControlDescription }),
                    );
                });
            });

            describe('Given that there are modifications to the description', () => {
                const newDescription = `New Description for Drata`;
                beforeEach(() => {
                    controlModifyRequestType.description = newDescription;
                });

                it('Should save the updated description into the control', async () => {
                    controlRepository.update?.mockImplementation(input => input);
                    await grcService.modifyControl(
                        account,
                        user,
                        controlId,
                        controlModifyRequestType,
                    );
                    expect(controlRepository.update).toHaveBeenCalledWith(
                        { id: 1 },
                        expect.objectContaining({ description: newDescription }),
                    );
                });
            });
        });
    });
});
