import { CheckResultStatus, ErrorCode } from '@drata/enums';
import { CheckStatus } from '@drata/enums/dist/monitors/check-status.enum';
import { Injectable, NotFoundException } from '@nestjs/common';
import { AuditHubControlsPaginatedRequestDto } from 'app/audit-hub/dtos/audit-hub-controls-paginated-request.dto';
import { Product } from 'app/companies/products/entities/product.entity';
import { CustomFieldLocation } from 'app/custom-fields/entities/custom-field-location.entity';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { Requirement } from 'app/frameworks/entities/requirement.entity';
import { RequirementIndexViewRepository } from 'app/frameworks/repositories/requirement-index-view.repository';
import { AvailableControlFieldValueRequest } from 'app/grc/dtos/available-code-request.dto';
import { ControlOwnersIntersectionRequestDto } from 'app/grc/dtos/control-owners-intersection-request.dto';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { Control } from 'app/grc/entities/control.entity';
import { ControlExpand } from 'app/grc/enums/control-expand.enum';
import { ControlsExpand } from 'app/grc/enums/controls-expand.enum';
import { extractIds } from 'app/grc/helpers/entity.helper';
import { ControlsDeleteFromIndexEvent } from 'app/grc/observables/events/controls-delete-from-index.event';
import { ControlsReindexEvent } from 'app/grc/observables/events/controls-reindex.event';
import { ControlCustomFieldsSubmissionsRepository } from 'app/grc/repositories/control-custom-fields-submissions.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ControlReadyDetailsType } from 'app/grc/types/control-ready-details.type';
import { ControlRequestType } from 'app/grc/types/control-request.type';
import { ControlWithCustomFields } from 'app/grc/types/control-with-custom-fields.type';
import { ControlWithEvidence } from 'app/grc/types/control-with-evidence.type';
import { ControlsRequestType } from 'app/grc/types/controls-request.type';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Caches } from 'commons/enums/cache.enum';
import { LibraryDocumentVersionType } from 'commons/enums/library-document-version-type.enum';
import { Action as UserAction } from 'commons/enums/users/action.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { fqtn } from 'commons/helpers/database.helper';
import {
    filterControls,
    getProductId,
    hasAssociatedProduct,
} from 'commons/helpers/products.helper';
import { syncControlScopeFromRequirementMappings } from 'commons/helpers/scope-management.helper';
import { hasUserPermission } from 'commons/helpers/user.helper';
import { JoinTypes } from 'commons/repositories/query.builder';
import { AppService } from 'commons/services/app.service';
import { CursorPage } from 'commons/types/cursor-page.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { Dictionary, isEmpty, isNil, keyBy } from 'lodash';
import { compareEntities } from 'scripts/recurring/framework/helpers/framework.helpers';
import {
    FrameworkContext,
    FrameworkLog,
} from 'scripts/recurring/framework/helpers/framework.types';
import { defaultPropertiesComparisonConfig } from 'scripts/recurring/framework/helpers/property-comparison-configs/default-property-comparison.config';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { In, IsNull, Not } from 'typeorm';

@Injectable()
export class ControlService extends AppService {
    private relations = [
        'requirements',
        'requirements.requirementIndex',
        'requirements.requirementIndex.framework',
        'owners',
        'controlTestInstances',
        'policies',
        'reports',
        'externalEvidence',
        'lastUpdatedBy',
    ];

    constructor(private readonly featureFlagService: FeatureFlagService) {
        super();
    }

    async getPaginatedControlsByCustomerRequestId(
        customerRequestId: number,
        requestDto: AuditHubControlsPaginatedRequestDto,
    ): Promise<PaginationType<Control>> {
        return this.controlRepository.getPaginatedControlsByCustomerRequestId(
            customerRequestId,
            requestDto,
        );
    }

    async getControlIsReady(id: number): Promise<Control | null> {
        return this.controlRepository.findOneWithQuery({
            where: {
                id,
            },
            relations: {
                controlIsReady: [JoinTypes.leftJoinAndMapOne, true],
            },
            withRelationsOptimization: true,
        });
    }

    async findControlWithEvidenceByIdOrFail(
        account: Account,
        id: number,
        controlRequestType?: ControlRequestType,
    ): Promise<ControlWithEvidence> {
        return this.controlRepository.findControlWithEvidenceByIdOrFail(
            account,
            id,
            controlRequestType,
        );
    }

    async findControlWithCustomFieldsOrFail(
        account: Account,
        id: number,
        controlRequestType: ControlRequestType,
    ): Promise<ControlWithCustomFields> {
        const control: Control = await this.controlRepository.findControlByIdOrFail(
            account,
            id,
            controlRequestType,
        );

        let customFields: CustomFieldLocation[] | undefined;
        if (controlRequestType.expand?.includes(ControlExpand.customFields)) {
            customFields =
                await this.customFieldsLocationsRepository.getCustomFieldControlSubmissions(id);
        }

        return {
            control,
            customFields,
        };
    }

    async listControlsWithCursor(
        controlsRequestType: ControlsRequestType,
        account: Account,
    ): Promise<CursorPage<ControlWithCustomFields>> {
        const controlsList = await this.controlRepository.listControlsWithCursor(
            controlsRequestType,
            account,
        );
        const controlsEnriched: CursorPage<ControlWithCustomFields> = {
            data: [],
            cursor: controlsList.cursor,
        };

        const includeCustomFields =
            controlsRequestType.expand?.includes(ControlsExpand.customFields) ?? false;
        for (const control of controlsList.data) {
            const controlWithEvidenceAndCustomFields: Partial<ControlWithCustomFields> = {
                control,
            };
            if (includeCustomFields) {
                controlWithEvidenceAndCustomFields.customFields =
                    // eslint-disable-next-line no-await-in-loop
                    await this.customFieldsLocationsRepository.getCustomFieldLocationsWithControlSubmissions(
                        control.id,
                    );
            }
            controlsEnriched.data.push(
                controlWithEvidenceAndCustomFields as ControlWithCustomFields,
            );
        }
        return controlsEnriched;
    }

    @CacheWithPrefix<ControlReadyDetailsType>(null, {
        store: Caches.FIND_CONTROL_DETAILS_BY_ID,
        useArgs: 1,
        ttl: config.get('cache.ttl.hour'),
    })
    async findControlDetailsById(id: number, account: Account): Promise<ControlReadyDetailsType> {
        const control = await this.controlRepository.getControlWithFlagsById(account, id); // ensure control is linked to product
        if (isNil(control)) {
            throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
        }

        const hideLibraryDocumentTypes = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_HIDE_TEST_EVIDENCES,
                category: FeatureFlagCategory.NONE,
                defaultValue: true,
            },
            account,
        );

        const [rawResult, controlReportData, controlWithMonitorInstances] = await Promise.all([
            this.controlRepository.query(
                `SELECT * FROM ${fqtn(
                    account.databaseName,
                    'vw_control_is_ready_index',
                )} where control_id = ? LIMIT 1`,
                [id],
            ),
            this.controlRepository.getControlEvidenceVersionsById(id),
            this.controlRepository.getControlTestInstancesById(id),
        ]);

        const libraryDocumentTypes = hideLibraryDocumentTypes
            ? undefined
            : controlReportData?.libraryDocuments.flatMap(doc => {
                  return (
                      doc.libraryDocument?.libraryDocumentVersions?.map(version => version.type) ??
                      []
                  );
              });

        const checkResultStatuses = controlWithMonitorInstances?.controlTestInstances.map(
            testInstance => testInstance.checkResultStatus,
        );

        const libraryDocumentsWithExpiredDate =
            controlReportData?.libraryDocuments.filter(doc =>
                doc.libraryDocument?.libraryDocumentWorkflows.some(workflow =>
                    workflow.libraryDocumentRenewalSchema?.some(
                        schema =>
                            schema.renewalDate !== null &&
                            schema.renewalDate !== undefined &&
                            new Date(schema.renewalDate) <= new Date() &&
                            schema.libraryDocumentVersion?.current === true,
                    ),
                ),
            ).length || 0;

        //writing this to ensure we are not getting dupes with the set
        const documentsWithEmptySource =
            [
                ...new Set(
                    controlReportData?.libraryDocuments
                        .filter(doc =>
                            doc.libraryDocument?.libraryDocumentVersions.some(
                                version => version.current && version.source === '',
                            ),
                        )
                        .map(doc => doc.libraryDocument?.id),
                ),
            ].length || 0;

        const testResultDocument = controlReportData?.libraryDocuments.filter(doc =>
            doc.libraryDocument?.libraryDocumentVersions.some(
                version => version.type === LibraryDocumentVersionType.TEST_RESULT,
            ),
        );

        const passingTestResultDocuments = [];
        const failingTestResultDocuments = [];

        if (testResultDocument?.length) {
            const testStatusMap = {};
            const checkStatusMap = {};
            controlWithMonitorInstances?.controlTestInstances.forEach(testInstance => {
                testStatusMap[testInstance.name] = testInstance.checkResultStatus;
                checkStatusMap[testInstance.name] = testInstance.checkStatus;
            });

            testResultDocument.forEach(doc => {
                const docName = doc.libraryDocument?.name;
                if (docName && testStatusMap[docName] !== undefined) {
                    const resultStatus = testStatusMap[docName];
                    const checkStatus = checkStatusMap[docName];

                    if (
                        checkStatus === CheckStatus.UNUSED ||
                        checkStatus === CheckStatus.DISABLED ||
                        resultStatus === CheckResultStatus.ERROR
                    ) {
                        failingTestResultDocuments.push(doc.id);
                    } else if (
                        (resultStatus === CheckResultStatus.PASSED ||
                            resultStatus === CheckResultStatus.FAILED) &&
                        checkStatus === CheckStatus.ENABLED
                    ) {
                        passingTestResultDocuments.push(doc.id);
                    }
                }
            });
        }

        const checkStatuses = controlWithMonitorInstances?.controlTestInstances.map(
            testInstance => testInstance.checkStatus,
        );

        const checkSources = controlWithMonitorInstances?.controlTestInstances.map(
            testInstance => testInstance.source,
        );
        if (isEmpty(rawResult)) {
            throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
        }
        const ret: ControlReadyDetailsType = {
            controlId: rawResult[0]['control_id'],
            policies: {
                ready: rawResult[0]['approved_policies']?.split(',').map(i => parseInt(i)) ?? [],
                notReady:
                    rawResult[0]['unapproved_policies']?.split(',').map(i => parseInt(i)) ?? [],
            },
            evidence: {
                external: {
                    ready:
                        rawResult[0]['valid_external_evidence_ids']
                            ?.split(',')
                            .map(i => parseInt(i)) ?? [],
                    notReady:
                        rawResult[0]['invalid_external_evidence_ids']
                            ?.split(',')
                            .map(i => parseInt(i)) ?? [],
                },
                library: {
                    ready: rawResult[0]['valid_report_ids']?.split(',').map(i => parseInt(i)) ?? [],
                    notReady:
                        rawResult[0]['incomplete_document_ids']?.split(',').map(i => parseInt(i)) ??
                        [],
                    types: libraryDocumentTypes,
                    needsRenewal: libraryDocumentsWithExpiredDate,
                    needsSource: documentsWithEmptySource,
                    testResultsReady: passingTestResultDocuments,
                    testResultsNotFactored: failingTestResultDocuments,
                },
            },
            tests: {
                ready: rawResult[0]['passing_test_ids']?.split(',').map(i => parseInt(i)) ?? [],
                notReady: rawResult[0]['failing_test_ids']?.split(',').map(i => parseInt(i)) ?? [],
                checkStatuses: checkStatuses,
                checkResultStatuses: checkResultStatuses,
                checkSources: checkSources,
            },
        };
        this.logger.log(
            PolloAdapter.acct(`Control ready details: ${JSON.stringify(ret)}`, account),
        );
        return ret;
    }

    async findControlWithEvidenceById(
        account: Account,
        user: User,
        id: number,
    ): Promise<ControlWithEvidence> {
        const control = await this.controlRepository.findControlWithEvidenceById(account, id);

        if (isNil(control)) {
            const exists = await this.controlRepository.controlExists(id);

            const message = exists
                ? `Control ${id} does not exist for workspace ${getProductId(account)}.`
                : `Control ${id} does not exists.`;

            this.logger.error(PolloAdapter.acct(message, account));

            const code = exists
                ? ErrorCode.CONTROL_NOT_FROM_WORKSPACE
                : ErrorCode.CONTROL_NOT_FOUND;

            throw new NotFoundException(code);
        }

        await this.validateUserHasPermissionToViewControlsOrFail(account, user, control.control.id);

        return control;
    }

    /**
     *
     * @param id
     * @param account
     * @param relations
     * @returns
     */
    async findControlById(
        id: number,
        account: Account,
        relations: Array<string> = [],
    ): Promise<Control> {
        return this.controlRepository.findControlById(id, account, relations);
    }

    async findControlBySlug(controlSlug: string, account: Account): Promise<Control> {
        const control = await this.controlRepository.findOneOrFail({
            where: {
                slug: controlSlug,
                enabledAt: Not(IsNull()),
            },
            relations: this.relations,
        });

        return this.controlRepository.fillInControl(control, account);
    }

    /**
     *
     * @param account
     * @param code
     * @returns
     */
    async findControlByCode(account: Account, code: string): Promise<Control> {
        let controls = await this.controlRepository.find({
            where: {
                code,
            },
            relations: ['products'],
        });

        if (hasAssociatedProduct(account)) {
            controls = filterControls(account, controls);
        }

        if (controls.length === 1) {
            return controls.shift();
        }

        throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
    }

    /**
     *
     * @param codes
     * @param workspaceId
     * @returns
     */
    async findControlsByCodes(codes: string[], workspaceId: number): Promise<Control[]> {
        return this.controlRepository.find({
            where: {
                code: In(codes),
                products: { id: workspaceId },
            },
            relations: ['products'],
        });
    }

    async findControlsByCodeAndName(code: string, name: string, workspaceId: number): Promise<Control[]> {
        return this.controlRepository.find({
            where: {
                code,
                name,
                products: { id: workspaceId },
            },
            relations: ['products'],
        });
    }

    /**
     * @deprecated Use ControlsCoreService.findControlsByIdAndCurrentProduct
     *
     * @param {Account} account
     * @param {number[]} controlIds
     * @returns {Control[]}
     */
    async findControlsByIdAndCurrentProduct(
        account: Account,
        controlIds: number[],
    ): Promise<Control[]> {
        if (isEmpty(controlIds)) {
            return [];
        }

        const controls = await this.controlRepository.find({
            where: {
                id: In(controlIds),
            },
            relations: ['products'],
        });

        return filterControls(account, controls);
    }

    getControlsDisabled(): Promise<Control[]> {
        const query = this.controlRepository
            .createQueryBuilder('Control')
            .select(['Control.id', 'Control.code'])
            .innerJoin('Control.products', 'Product')
            .where('Control.enabled_at IS NULL')
            .andWhere('Product.primary = 1');

        return query.getMany();
    }

    /**
     *
     * @returns
     */
    getControlsEnabled(): Promise<Control[]> {
        return this.controlRepository.find({
            where: {
                enabledAt: Not(IsNull()),
            },
        });
    }

    /**
     * Get controls with data from IsReady view
     * @returns Controls with IsReady view data
     */
    getControlsWithReady(): Promise<Control[]> {
        return this.controlRepository.findWithQuery({
            relations: {
                controlIsReady: [JoinTypes.leftJoinAndMapOne, true],
                products: true,
            },
            withRelationsOptimization: true,
        });
    }

    /**
     * @deprecated
     * This is a temporary fix, we don't have a feature to correctly delete
     * workspaces and all associated mappings. So this does a INNER JOIN to ensure we don't pull
     * undefined workspaces
     *
     * This ideally should not be used anywhere else in the code other than the customer request proxy service
     */
    getControlsWithReadyForCustomerRequests(): Promise<Control[]> {
        return this.controlRepository.findWithQuery({
            relations: {
                controlIsReady: [JoinTypes.leftJoinAndMapOne, true],
                products: [JoinTypes.innerJoinAndSelect, true],
            },
            withRelationsOptimization: true,
        });
    }

    getControlOwnersIntersection(
        requestDto: ControlOwnersIntersectionRequestDto,
    ): Promise<PaginationType<User>> {
        return this.controlRepository.getControlOwnersIntersection(requestDto);
    }

    /**
     * returns controls by Ids. It is meant to be a simple, fast query
     * without any relations attached.
     * @param controlIds
     * @returns
     */
    getEnabledControlsByIds(controlIds: number[]): Promise<Control[]> {
        if (isEmpty(controlIds)) {
            return Promise.resolve([]);
        }
        return this.controlRepository.find({
            where: {
                id: In(controlIds),
                enabledAt: Not(IsNull()),
            },
        });
    }

    async isAvailable({
        name,
        code,
        excludeIds,
        workspaceId,
    }: AvailableControlFieldValueRequest): Promise<{
        available: boolean;
        invalidCode?: number;
    }> {
        const CODE_SUBSTRING_INITIAL_INDEX = 0;
        const CODE_SUBSTRING_FINAL_INDEX = 3;
        const controlQuery = this.controlRepository
            .createQueryBuilder('controls')
            .leftJoinAndSelect('controls.products', 'products')
            .where('products.id = :workspaceId', { workspaceId });

        if (!isNil(code)) {
            if (
                code
                    .substring(CODE_SUBSTRING_INITIAL_INDEX, CODE_SUBSTRING_FINAL_INDEX)
                    .toLowerCase() === config.get('api.codePrefix')
            ) {
                return {
                    available: false,
                    invalidCode: ErrorCode.INVALID_CODE_PREFIX,
                };
            }
            controlQuery.andWhere('controls.code = :code', { code });
        } else {
            controlQuery.andWhere('controls.name = :name', { name });
        }

        if (!isEmpty(excludeIds)) {
            controlQuery.andWhere('controls.id NOT IN (:...excludeIds)', {
                excludeIds,
            });
        }

        const control = await controlQuery.getOne();

        return { available: isNil(control) };
    }

    public getControlsByPolicyId(policyId: number): Promise<Control[]> {
        return this.controlRepository.getControlsByPolicyId(policyId);
    }

    public getWorkspaceAwareControlOrFail(
        controlId: number,
        workspaceId: number,
    ): Promise<Control> {
        return this.controlRepository.getWorkspaceAwareControlOrFail(controlId, workspaceId);
    }

    public getControlById(id: number): Promise<Control> {
        return this.controlRepository.findOneByOrFail({ id });
    }

    public getControlByIdWithOwnersAndApprovers(id: number): Promise<Control> {
        return this.controlRepository.findOneOrFail({
            where: { id },
            relations: ['owners', 'approvals.approvalReviews.user'],
        });
    }
    public getControlByIdWithOwnersAndApprovals(id: number): Promise<Control> {
        return this.controlRepository.findControlsWithOwnersAndCurrentApprovals(id);
    }

    async updateControlsByScopeChangedRequirementIds(requirementIds: number[]): Promise<Control[]> {
        const controlsToUpdate: Control[] = [];

        const controls = await this.controlRepository.getControlsByRequirementIds(requirementIds);

        if (isEmpty(controls)) {
            return [];
        }

        for (const control of controls) {
            const nonArchivedRequirements = control.requirements?.filter(requirement =>
                isNil(requirement.archivedAt),
            );

            if (isEmpty(nonArchivedRequirements)) {
                control.archivedAt = new Date();
                controlsToUpdate.push(control);
            } else if (!isEmpty(nonArchivedRequirements) && !isNil(control.archivedAt)) {
                control.archivedAt = null;
                control.rationale = null;
                controlsToUpdate.push(control);
            }
        }

        if (isEmpty(controlsToUpdate)) {
            return [];
        }

        const savedControls = await this.controlRepository.save(controlsToUpdate);

        const account = this.tenantAccount;
        if (!isEmpty(savedControls) && !isNil(account)) {
            const workspaceIds = new Set<number>();
            savedControls.forEach(control => {
                control.products?.forEach(product => {
                    if (product?.id) {
                        workspaceIds.add(product.id);
                    }
                });
            });

            workspaceIds.forEach(workspaceId => {
                const controlIds = savedControls
                    .filter(control =>
                        control.products?.some(product => product.id === workspaceId),
                    )
                    .map(control => control.id);

                this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, controlIds));
            });
        }

        return savedControls;
    }

    async getControlsMap(controlCodes: string[]): Promise<Dictionary<Control>> {
        const controlEntities =
            await this.controlRepository.getEnabledControlsByCodes(controlCodes);
        return keyBy(controlEntities, 'code');
    }

    async filterValidControls(originalControlsIds: number[]): Promise<Control[]> {
        if (isEmpty(originalControlsIds)) return Promise.resolve([]);
        return this.controlRepository.getEnabledControlsByControlIds(originalControlsIds);
    }
    async getControlsFromActiveFrameworksByCodes(codes?: string[]): Promise<Control[]> {
        return this.controlRepository.getControlsFromActiveFrameworksByCodes(codes);
    }

    async getControlByCode(code: string): Promise<Control | null> {
        return this.controlRepository.findOne({
            where: {
                code: code,
            },
        });
    }

    /**
     * Note: this method is for a control reset edge case,
     * otherwise provisioning controls should go through the existing
     * provisioning logic
     * @param templateControlsToProvision
     * @param products
     * @returns
     */
    async provisionMissingControlsToTenant(
        templateControlsToProvision: ControlTemplate[],
        products: Product[],
    ): Promise<Control[]> {
        const createdControls: Control[] = templateControlsToProvision.map(tempControl => {
            const newControl = new Control();
            newControl.code = tempControl.code;
            newControl.controlNumber = tempControl.controlNumber;
            newControl.name = tempControl.name;
            newControl.description = tempControl.description ?? '';
            newControl.activity = tempControl.activity;
            newControl.question = tempControl.question;
            newControl.domain = tempControl.domain;
            newControl.category = tempControl.category;
            newControl.enabledAt = new Date();
            newControl.controlTemplateId = tempControl.id;
            newControl.products = products;

            return newControl;
        });

        const savedControls = await this.controlRepository.save(createdControls);

        const account = this.tenantAccount;
        if (!isEmpty(savedControls) && !isEmpty(products) && !isNil(account)) {
            const workspaceIds = new Set<number>();
            products.forEach(product => {
                if (product?.id) {
                    workspaceIds.add(product.id);
                }
            });

            workspaceIds.forEach(workspaceId => {
                const controlIds = savedControls.map(control => control.id);

                this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, controlIds));
            });
        }

        return savedControls;
    }

    getControlsByIdsWithOwners(controlIds: number[]): Promise<Control[]> {
        return this.controlRepository.find({
            where: { id: In(controlIds) },
            relations: ['owners'],
        });
    }

    async listControls(
        account: Account,
        requestDto: ControlsRequestDto,
    ): Promise<PaginationType<Control>> {
        const { page, limit } = requestDto;

        const [controlIds, total] = await this.index.getControlIds(account, requestDto);
        const data = await this.controlRepository.getControlsFromIds(controlIds);

        for (const control of data) {
            control.description = control.description.replace(/%s/g, account.companyName);
        }

        return {
            data,
            page,
            limit: limit ?? 0,
            total,
        };
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get index(): RequirementIndexViewRepository {
        return this.getCustomTenantRepository(RequirementIndexViewRepository);
    }

    @CacheWithPrefix<ControlWithEvidence>(null, {
        store: Caches.LIST_CONTROLS,
        useArgs: 1,
        ttl: config.get('cache.ttl.hour'),
    })
    async getShortControlWithEvidenceById(
        controlId: number,
        account: Account,
        user: User,
    ): Promise<ControlWithEvidence> {
        try {
            const control = await this.controlRepository.getControlWithFlagsById(
                account,
                controlId,
            );

            await Promise.all([
                this.validateControlExistsForWorkspace(account, controlId, control),
                this.validateUserHasPermissionToViewControlsOrFail(account, user, controlId),
            ]);

            return {
                control,
                hasTicket:
                    (control?.controlTicket?.inProgressTicketIds?.length ?? 0) > 0 ||
                    (control?.controlTicket?.doneTicketIds?.length ?? 0) > 0,
                hasEvidence: !!control?.controlIsReady?.hasEvidence,
                hasPolicy: !!control?.controlIsReady?.hasPolicy,
                isReady: !!control?.controlIsReady?.isReady,
                isMonitored: !!control?.controlIsReady?.isMonitored,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Something went wrong trying to get short control with evidence by ID: ${controlId}`,
                    account,
                ),
            );
            throw error;
        }
    }

    private async validateControlExistsForWorkspace(
        account: Account,
        controlId: number,
        workspaceControl: Control | null,
    ): Promise<void> {
        if (!isNil(workspaceControl)) {
            return;
        }

        const exists = await this.controlRepository.controlExists(controlId);

        const message = exists
            ? `Control ${controlId} does not exist for workspace ${getProductId(account)}.`
            : `Control ${controlId} does not exists.`;

        this.logger.error(PolloAdapter.acct(message, account));

        const code = exists ? ErrorCode.CONTROL_NOT_FROM_WORKSPACE : ErrorCode.CONTROL_NOT_FOUND;

        throw new NotFoundException(code);
    }

    private async validateUserHasPermissionToViewControlsOrFail(
        account: Account,
        user: User,
        controlId: number,
    ): Promise<void> {
        if (hasUserPermission(user, UserAction.READ, Subject.ViewAllControls)) {
            return;
        }

        const hasRestrictedAccess =
            await this.controlRepository.isUserControlOwnerApproverOrTaskOwner(user, controlId);

        if (hasRestrictedAccess) {
            return;
        }

        this.logger.error(
            PolloAdapter.acct(
                `User ${user.id} is not a control owner, approver, or task owner and should not be authorized to view this control`,
                account,
            ),
        );

        throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
    }

    async getControlAdditionsAndUpdates(
        tenantControls: Map<string, Control>,
        templateControls: ControlTemplate[],
        compare: string[],
    ): Promise<{ add: Control[]; update: { control: Control; updates: Partial<Control> }[] }> {
        const add: Control[] = [];
        const update: { control: Control; updates: Partial<Control> }[] = [];

        for (const control of templateControls) {
            const existingControl = tenantControls.get(control.code);
            if (!existingControl) {
                const addControl = new Control();
                Object.assign(addControl, control);
                add.push(addControl);
            } else {
                // eslint-disable-next-line no-await-in-loop
                const updates = await compareEntities(
                    existingControl,
                    control,
                    defaultPropertiesComparisonConfig(compare),
                );

                if ('enabledAt' in updates) {
                    if (isNil(existingControl.enabledAt)) {
                        updates.enabledAt = new Date();
                    } else {
                        delete updates.enabledAt;
                    }
                }

                if (Object.keys(updates).length > 0) {
                    update.push({ control: existingControl, updates });
                }
            }
        }

        return { add, update };
    }

    async saveNewControls(
        newControls: Control[],
        frameworkContext: FrameworkContext,
        logs: FrameworkLog,
    ): Promise<void> {
        newControls.forEach(control => {
            control.products = [frameworkContext.product];
            control.enabledAt = new Date();
        });

        frameworkContext.controls = await this.controlRepository.save(newControls);
        logs.controls.push(
            ...frameworkContext.controls.map(control => ({
                action: 'Control Added',
                id: control.id,
                code: control.code,
                name: control.name,
                product: control.products[0]?.id,
            })),
        );

        const account = this.tenantAccount;
        if (!isEmpty(frameworkContext.controls) && frameworkContext.product && !isNil(account)) {
            const newControlIds = extractIds(frameworkContext.controls);
            this._eventBus.publish(
                new ControlsReindexEvent(account, frameworkContext.product.id, newControlIds),
            );
        }
    }

    async saveControlUpdates(
        updateControls: { control: Control; updates: Partial<Control> }[],
        logs: FrameworkLog,
        account?: Account,
        workspaceId?: number,
    ): Promise<void> {
        const log = updateControls.map(({ control, updates }) => ({
            action: 'Control Updated',
            id: control.id,
            code: control.code,
            name: control.name,
            description: control.description,
            updates: updates,
        }));

        const updatedControls = updateControls.map(({ control, updates }) => {
            Object.assign(control, updates);
            return control;
        });

        if (updatedControls.length > 0) {
            await this.controlRepository.save(updatedControls);
        }

        logs.controls.push(...log);

        if (account && workspaceId && updatedControls.length > 0) {
            const enabledControlIds = updatedControls
                .filter(control => control.enabledAt !== null)
                .map(control => control.id);

            if (enabledControlIds.length > 0) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, workspaceId, enabledControlIds),
                );
            }
        }
    }

    async disableControls(frameworkContext: FrameworkContext, logs: FrameworkLog): Promise<void> {
        const disablingFrameworkTag = frameworkContext.framework.tag;

        const controlsWithRequirements = frameworkContext.controls.filter(control => {
            if (!control.requirements || control.requirements.length === 0) {
                return false;
            }

            const isExclusivelyMappedToDisablingFramework = control.requirements.every(
                requirement => {
                    const requirementFrameworkTag = requirement?.requirementIndex?.framework?.tag;
                    return requirementFrameworkTag === disablingFrameworkTag;
                },
            );

            return isExclusivelyMappedToDisablingFramework;
        });

        const controlsToDisable = controlsWithRequirements.map(control => ({
            control,
            updates: { enabledAt: null },
        }));

        if (controlsToDisable.length > 0) {
            const existingControlIds = controlsToDisable.map(item => item.control.id);

            const account = this.tenantAccount;
            await this.saveControlUpdates(
                controlsToDisable,
                logs,
                account,
                frameworkContext.product?.id,
            );
            if (frameworkContext.product && !isNil(account)) {
                this._eventBus.publish(
                    new ControlsDeleteFromIndexEvent(
                        account,
                        frameworkContext.product.id,
                        existingControlIds,
                    ),
                );
            }
        }
    }

    async syncControlScopeOnFrameworkUpdate(requirements: Requirement[]): Promise<Control[]> {
        const controlsMap = new Map<number, Control>();

        requirements.forEach(requirement => {
            if (isEmpty(requirement.controls)) {
                return;
            }

            requirement.controls.forEach(control => {
                if (!controlsMap.has(control.id)) {
                    controlsMap.set(control.id, control);
                }
            });
        });

        if (isEmpty(controlsMap)) {
            return [];
        }

        const controls = Array.from(controlsMap.values());

        const { controlsToUpdate } = syncControlScopeFromRequirementMappings(controls);

        if (isEmpty(controlsToUpdate)) {
            return [];
        }

        await this.controlRepository.save(controlsToUpdate);

        // Trigger reindex for control scope updates
        const account = this.tenantAccount;

        // Group controls by workspace for efficient reindexing
        const workspaceControlMap = new Map<number, number[]>();

        for (const control of controlsToUpdate) {
            // Get workspaces where this control is present
            const workspaceIds = control.products?.map(product => product.id) || [];
            workspaceIds.forEach(workspaceId => {
                if (!workspaceControlMap.has(workspaceId)) {
                    workspaceControlMap.set(workspaceId, []);
                }
                workspaceControlMap.get(workspaceId)?.push(control.id);
            });
        }

        if (!isNil(account)) {
            // Trigger reindex per workspace
            workspaceControlMap.forEach((controlIds, workspaceId) => {
                this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, controlIds));
            });
        }

        return controlsToUpdate;
    }

    getWorkspaceControlsByIdsOrFail(
        controlIdsForEvidencePackage: number[],
        productId: number,
    ): Promise<Control[]> {
        return this.controlRepository.getWorkspaceControlsByIdsOrFail(
            controlIdsForEvidencePackage,
            productId,
        );
    }

    private get controlCustomFieldsSubmissionsRepository(): ControlCustomFieldsSubmissionsRepository {
        return this.getCustomTenantRepository(ControlCustomFieldsSubmissionsRepository);
    }

    private get customFieldsLocationsRepository(): CustomFieldsLocationsRepository {
        return this.getCustomTenantRepository(CustomFieldsLocationsRepository);
    }
}
