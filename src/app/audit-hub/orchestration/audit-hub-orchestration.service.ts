import {
    AuditorFrameworkOverhaulStatus,
    AuditorFrameworkType as AuditTypeEnum,
    ErrorCode,
    FrameworkTag,
} from '@drata/enums';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { AuditPaginatedRequestDto } from 'app/audit-hub/dtos/audit-paginated-request.dto';
import { AuditPersonnelRequestDto } from 'app/audit-hub/dtos/audit-personnel-request.dto';
import { getAuditPeriodByFramework } from 'app/audit-hub/helpers/audit.helper';
import { AuditHubValidationService } from 'app/audit-hub/services/audit-hub-validation.service';
import { AuditWithAuditors } from 'app/audit-hub/types/audit-with-auditors.type';
import { CustomerRequestMessageFileData } from 'app/audit-hub/types/customer-request-message-file-data.type';
import { PaginatedCustomerRequestMessageFilesParams } from 'app/audit-hub/types/paginated-customer-request-message-files-params.type';
import { ControlsRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/controls-request.auditor-api.dto';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesOrchestrationService } from 'app/companies/services/companies-orchestration.service';
import { ControlService } from 'app/control/control.service';
import { AuditorFrameworkListView } from 'app/customer-request/entities/auditor-framework-list-view.entity';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { AuditSampleRepository } from 'app/customer-request/repositories/audit-sample.repository';
import { CustomerRequestMessageFileRepository } from 'app/customer-request/repositories/customer-request-message-file.repository';
import { CustomerRequestRepository } from 'app/customer-request/repositories/customer-request.repository';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { Control } from 'app/grc/entities/control.entity';
import { ControlsOrchestrationService } from 'app/grc/services/controls-orchestration.service';
import { ControlListWithFlags } from 'app/grc/types/control-list.type';
import { User } from 'app/users/entities/user.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkAuditors } from 'auditors/entities/auditor-framework-auditors.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { FrameworkTypeTags } from 'auditors/entities/framework-type-tags.map';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { AuditListType } from 'auditors/types/audit-list.type';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { CompanyArchiveCategory } from 'commons/enums/company-archive-category.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { NotFoundException as NotFoundExceptionWithErrorCode } from 'commons/exceptions/not-found.exception';
import { getGlobalDataSource } from 'commons/factories/data-source.manager';
import { frameworkToFrameworkTemplateEntity } from 'commons/helpers/publish-frameworks.helper';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { PaginationType } from 'commons/types/pagination.type';
import { getCustomRepository } from 'database/typeorm/typeorm.extensions.helper';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { isEmpty, isNil } from 'lodash';
import { ServiceUserRepository } from 'service-user/repositories/service-user.repository';
import { FindOptionsRelations, In, Repository } from 'typeorm';

@Injectable()
export class AuditHubOrchestrationService extends AppService {
    constructor(
        private readonly auditorRepository: AuditorRepository,
        private readonly usersCoreService: UsersCoreService,
        private readonly auditHubValidationService: AuditHubValidationService,
        private readonly entryCoreService: EntryCoreService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly controlService: ControlService,
        private readonly downloader: Downloader,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly companiesOrchestrationService: CompaniesOrchestrationService,
        private readonly controlsOrchestrationService: ControlsOrchestrationService,
    ) {
        super();
    }

    public async getAuditWithAuditorsOrFail(
        accountId: AccountIdType,
        user: User,
        auditId: string,
    ): Promise<AuditWithAuditors> {
        const auditorFrameworkRepository = await this.auditorFrameworkRepository();
        let audit = await auditorFrameworkRepository.getAuditByIdOrFail(auditId, accountId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.validateAuditorAccessToAuditOrFail(user.entryId, audit.id, accountId);
        }

        if (audit.frameworkType === AuditTypeEnum.CUSTOM) {
            audit = await this.correctAuditorAuditTypeForCustomAudit(audit);
        }

        const auditors = await this.auditorRepository.getAuditorsByAccountAndAudit(
            audit.id,
            accountId,
        );

        return { audit, auditors };
    }

    public async getAuditorClientForTenantOrFail(
        auditor: Auditor,
        accountId: AccountIdType,
    ): Promise<AuditorClient> {
        const auditorClientRepository = await this.auditorClientRepository();
        const auditorClient = await auditorClientRepository.findOne({
            where: {
                entry: { id: auditor.entry.id },
                account: { id: accountId },
            },
        });

        if (auditorClient === null) {
            throw new ForbiddenException(
                'Auditor not allowed in tenant.',
                ErrorCode.TENANT_AUDITOR_FORBIDDEN,
            );
        }

        return auditorClient;
    }

    private async correctAuditorAuditTypeForCustomAudit(audit: Audit): Promise<Audit> {
        const customFramework = await this.frameworkRepository.getEnabledFrameworkByTag(
            FrameworkTag.CUSTOM,
            null,
            audit.customFrameworkId,
        );

        if (!isNil(customFramework)) {
            return Object.assign(Object.create(Object.getPrototypeOf(audit)), audit, {
                auditorFrameworkType: {
                    ...audit.auditorFrameworkType,
                    label: customFramework.name,
                    relatedFramework: frameworkToFrameworkTemplateEntity(customFramework),
                },
            });
        }

        return audit;
    }

    async validateAuditorAccessToAuditOrFail(
        entryId: string,
        auditId: string,
        accountId: AccountIdType,
    ): Promise<void> {
        const auditor = await this.getAuditorForUser(entryId);

        // find a service user for this entry and account
        const serviceUserRepository = await this.serviceUserRepository();
        const serviceUser = await serviceUserRepository.findOne({
            where: { entry: { id: entryId }, accounts: { id: accountId } },
            loadEagerRelations: false,
        });

        // if the user performing this action is both a service user AND an auditor
        if (!isNil(serviceUser) && !isNil(auditor)) {
            // service user access overrides auditor access
            // proceed as an assumed Admin with full access because the user is a service user
            return;
        }

        /**
         * If the user is an auditor, we need to make a few validations,
         * otherwise we assume that we are dealing with a tenant user whose
         * permissions were checked in the controller layer.
         */
        if (!isNil(auditor)) {
            const auditorClient = await this.getAuditorClientForTenantOrFail(auditor, accountId);

            await this.isAuditorAllowedInAuditOrFail(auditId, auditorClient);
        }
    }

    async getAuditorsByAuditOrFail(
        accountId: AccountIdType,
        entryId: string,
        auditId: string,
    ): Promise<Auditor[]> {
        const user = await this.usersCoreService.getUserWithSupportByEntryId(entryId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                entryId,
                auditId,
                accountId,
            );
        }

        return this.auditorRepository.getAuditorsByAccountAndAudit(auditId, accountId);
    }

    async getAuditList(
        account: Account,
        user: User,
        dto: AuditPaginatedRequestDto,
    ): Promise<PaginationType<AuditListType>> {
        const entry = await this.entryCoreService.getEntryWithoutRelationsByIdOrFail(user.entryId);
        const { id: accountId } = account;
        const isReleaseOptimizedAuditHubEndpoint = await this.featureFlagService.evaluateAsTenant(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.RELEASE_OPTIMIZED_AUDIT_HUB_AUDITS_ENDPOINT,
                defaultValue: false,
            },
            account,
        );

        // find an auditor for this entry
        let auditorUser = await (isReleaseOptimizedAuditHubEndpoint
            ? this.auditorRepository
                  .createQueryBuilder('auditor')
                  .innerJoinAndSelect('auditor.entry', 'entry')
                  .where('entry.id = :id', { id: entry.id })
                  .getOne()
            : this.auditorRepository.findOne({
                  where: { entry: { id: entry.id } },
              }));

        const serviceUserRepository = await this.serviceUserRepository();

        // find a service user for this entry and account
        const serviceUser = await serviceUserRepository.findOne({
            where: { entry: { id: entry.id }, accounts: { id: accountId } },
        });

        // if the user performing this action is both a service user AND an auditor
        // then rely on the dto to determine if we should display all audits or only audits assigned to you
        // if you should only show audits assigned to you then retain the auditor filter
        if (!isNil(serviceUser) && !isNil(auditorUser) && !dto.onlyShowAssignedAudits) {
            auditorUser = null;
        }

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!isNil(auditorUser) && hasRole(user, [Role.ADMIN])) {
            auditorUser = null;
        }

        let paginatedAuditList: PaginationType<Audit>;

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        // if user is auditor, get assigned audits
        if (!isNil(auditorUser)) {
            const client = await this.auditHubValidationService.getAuditorClientForTenantOrFail(
                auditorUser,
                accountId,
            );

            paginatedAuditList =
                await auditorFrameworkRepository.getFilteredAuditsByAccountIdAndClientId(
                    accountId,
                    client.id,
                    dto.limit,
                    dto.page,
                    !!dto.ignoreLimit,
                    {
                        auditStatuses: dto.auditStatuses,
                        workspaceId: dto.workspaceId,
                        q: dto.q,
                    },
                    { isReleaseOptimizedAuditHubEndpoint },
                );
        } else {
            // if tenant user, get all audits for account
            paginatedAuditList = await auditorFrameworkRepository.getAuditsByAccountId(
                accountId,
                dto.limit,
                dto.page,
                !!dto.ignoreLimit,
                {
                    auditStatuses: dto.auditStatuses,
                    workspaceId: dto.workspaceId,
                    q: dto.q,
                },
                { isReleaseOptimizedAuditHubEndpoint },
            );
        }

        // add related framework info to audits with framework type of CUSTOM
        const customFrameworkPromises = paginatedAuditList.data.map(audit =>
            this.correctAuditorAuditTypeForCustomAudit(audit),
        );
        const processedAudits = await Promise.all(customFrameworkPromises);

        // add message counts to audits
        const auditListWitNotifications = await this.populateAuditMessageCountsByAccount(
            processedAudits,
            auditorUser,
        );

        const auditListWithNotificationsAndControlCount = await Promise.all(
            auditListWitNotifications.map(async audit =>
                this.populateAuditFrameworkControlCount(audit),
            ),
        );

        return {
            limit: paginatedAuditList.limit,
            page: paginatedAuditList.page,
            nextPage: paginatedAuditList.nextPage,
            total: paginatedAuditList.total,
            data: auditListWithNotificationsAndControlCount,
        };
    }

    async getControlIdsForCustomerRequestOrFail(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        selectedControlIds?: number[],
    ): Promise<number[]> {
        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                user.entryId,
                auditId,
                account.id,
            );
        }

        const audit = await this.getAccountAuditByIdOrFail(account, auditId, {
            auditorFrameworkAuditors: {
                auditorClient: {
                    entry: true,
                },
            },
        });

        const customerRequest =
            await this.customerRequestRepository.getAuditCustomerRequestByIdWithControlsOrFail(
                customerRequestId,
                auditId,
            );
        const controlIdsForEvidencePackage = await this.getControlIdsForEvidencePackageOrFail(
            customerRequest,
            audit,
            selectedControlIds,
        );
        await this.auditHubValidationService.validateAuditHasAllValidAuditorsOrFail(
            account,
            audit,
            {
                auditId,
                customerRequestId,
                controlIdsForEvidencePackage,
            },
        );

        return controlIdsForEvidencePackage;
    }

    async getAccountAuditByIdOrFail(
        account: Account,
        auditId: string,
        relations?: FindOptionsRelations<Audit>,
    ): Promise<Audit> {
        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        const audit = await auditorFrameworkRepository.findOne({
            where: {
                id: auditId,
            },
            relations: {
                ...relations,
                account: true,
            },
        });

        if (isNil(audit)) {
            throw new NotFoundException(`Audit with ID ${auditId} does not exist`);
        }

        this.auditHubValidationService.validateAuditForAccountOrFail(account, audit);

        return audit;
    }

    async getControlIdsForEvidencePackageOrFail(
        customerRequest: CustomerRequest,
        audit: Audit,
        selectedControlIds?: number[],
    ): Promise<number[]> {
        if (isNil(customerRequest.controls) || isEmpty(customerRequest.controls)) {
            throw new BadRequestException('Customer request does not have controls mapped');
        }

        let controlIdsForEvidencePackage: number[];

        if (isNil(selectedControlIds) || isEmpty(selectedControlIds)) {
            controlIdsForEvidencePackage = customerRequest.controls.map(control => control.id);
        } else {
            const customerRequestMappedControlsMapById: Record<number, Control> =
                customerRequest.controls.reduce(
                    (acc, control) => ({
                        ...acc,
                        [control.id]: control,
                    }),
                    {} as Record<number, Control>,
                );

            const areAllControlsMappedToCustomerRequest = selectedControlIds.every(controlId =>
                Boolean(customerRequestMappedControlsMapById[controlId]),
            );

            if (!areAllControlsMappedToCustomerRequest) {
                throw new BadRequestException(
                    'Selected controls do not belong to the customer request',
                );
            }

            controlIdsForEvidencePackage = selectedControlIds;
        }

        await this.controlService.getWorkspaceControlsByIdsOrFail(
            controlIdsForEvidencePackage,
            audit.productId,
        );

        return controlIdsForEvidencePackage;
    }

    private async populateAuditMessageCountsByAccount(
        audits: Audit[],
        auditor?: Auditor | null,
    ): Promise<AuditListType[]> {
        const frameworkIds = audits.map(a => a.id);

        const auditListData = await this.auditListViewRepository.find({
            where: { auditorFrameworkId: In(frameworkIds) },
        });

        const auditListMap = new Map(auditListData.map(view => [view.auditorFrameworkId, view]));

        return audits.map(audit => {
            const listView = auditListMap.get(audit.id);
            if (!listView) {
                return {
                    audit,
                    unreadMessages: 0,
                    acceptedRequests: 0,
                    totalRequests: 0,
                    hasControls: false,
                };
            }

            return {
                audit,
                unreadMessages: auditor
                    ? (listView.auditorUnreadMessages ?? 0)
                    : (listView.customerUnreadMessages ?? 0),
                acceptedRequests: listView.acceptedRequests ?? 0,
                totalRequests: listView.totalRequests ?? 0,
                hasControls: false,
            };
        });
    }

    private async populateAuditFrameworkControlCount(
        auditList: AuditListType,
    ): Promise<AuditListType> {
        const { audit } = auditList;
        const { frameworkType, customFrameworkId, productId } = audit;
        const frameworkTag = FrameworkTypeTags.get(frameworkType);

        const framework = await this.frameworkRepository.getFrameworkWithControlsByTagAndWorkspace(
            frameworkTag,
            productId,
            customFrameworkId,
        );

        return {
            ...auditList,
            hasControls: !isNil(framework),
        };
    }

    private async isAuditorAllowedInAuditOrFail(
        auditId: string,
        auditorClient: AuditorClient,
    ): Promise<void> {
        const auditorFrameworkAuditorsRepository = await this.auditorFrameworkAuditorsRepository();
        const auditToAuditorMapping = await auditorFrameworkAuditorsRepository.findOne({
            where: {
                auditorFramework: { id: auditId },
                auditorClient: { id: auditorClient.id },
            },
        });

        if (isNil(auditToAuditorMapping)) {
            throw new ForbiddenException(
                'Auditor cannot access audit.',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }
    }

    private async getAuditorForUser(entryId: string): Promise<Auditor | null> {
        return this.auditorRepository.findOne({
            where: { entry: { id: entryId } },
        });
    }

    async getFirstAuditorUserFromAuditOrFail(account: Account, audit: Audit): Promise<User> {
        const auditor = await this.auditorRepository.getFirstAuditorByAccountAndAudit(
            audit.id,
            account.id,
        );

        if (isNil(auditor)) {
            throw new NotFoundExceptionWithErrorCode(ErrorCode.AUDITOR_NOT_FOUND);
        }

        return this.usersCoreService.getUserByEntryId(auditor.entry.id);
    }

    async getLatestAuditControlsEvidencePackageOrFail(
        account: Account,
        user: User,
        auditId: string,
    ): Promise<DownloaderPayloadType> {
        // first I need to validate that the given user has access to the given audit
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const file =
            await this.auditSampleRepository.getLatestAuditSampleWithValidFrameworkArchiveOrFail(
                auditId,
            );

        return this.downloader.getDownloadUrl(file);
    }

    async getLatestPreAuditPackageOrFail(
        account: Account,
        user: User,
        auditId: string,
    ): Promise<DownloaderPayloadType> {
        // first I need to validate that the given user has access to the given audit
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        // set current product based on the selected audit id
        const audit = await auditorFrameworkRepository.findOneOrFail({
            where: {
                id: auditId,
            },
        });

        const currentProduct = await this.workspacesCoreService.getProductById(audit.productId);

        if (isNil(currentProduct)) {
            throw new NotFoundException('Audit Product not found.');
        }

        return this.companiesOrchestrationService.getLastCompanyDownloadArchiveByCategory(
            account,
            user,
            CompanyArchiveCategory.PRE_AUDIT,
            currentProduct,
            auditId,
        );
    }

    async updateAuditStatusOrFail(
        account: Account,
        user: User,
        auditId: string,
        status: AuditorFrameworkOverhaulStatus,
    ): Promise<void> {
        // first I need to validate that the given user has access to the given audit
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        try {
            const audit = await auditorFrameworkRepository.findOneByOrFail({
                id: auditId,
            });

            audit.status = status;

            if (status === AuditorFrameworkOverhaulStatus.COMPLETED) {
                audit.completedAt = new Date();
                const hasAuditorRole = hasRole(user, [Role.AUDITOR]);
                const hasAdminRole = hasRole(user, [Role.ADMIN]);

                if (hasAuditorRole) {
                    audit.completedRole = Role.AUDITOR;
                } else if (hasAdminRole) {
                    audit.completedRole = Role.ADMIN;
                } else {
                    audit.completedRole = user.roles[0]?.role; // get first role from user
                }
            } else {
                audit.completedAt = null;
                audit.completedRole = null;
            }

            await auditorFrameworkRepository.save(audit, {
                reload: false,
            });
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Auditor: ${error.message}`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateAuditStatusOrFail.name),
            );
        }
    }

    async getControlsForAuditOrFail(
        account: Account,
        entryId: string,
        auditId: string,
        requestDto: ControlsRequestAuditorApiDto,
    ): Promise<PaginationType<ControlListWithFlags>> {
        // first I need to validate that the given user has access to the given audit
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            entryId,
            auditId,
            account.id,
        );

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        // set current product based on the selected audit id
        const audit = await auditorFrameworkRepository.findOneOrFail({
            where: {
                id: auditId,
            },
        });

        const currentProduct = await this.workspacesCoreService.getProductById(audit.productId);

        if (isNil(currentProduct)) {
            throw new NotFoundException();
        }

        // current product is used in the grc service and must be set for accurate results
        account.setCurrentProduct(currentProduct);

        const controlRequestDto = new ControlsRequestDto();

        controlRequestDto.q = requestDto.q;
        if (!isNil(controlRequestDto.page)) {
            controlRequestDto.page = requestDto.page;
        }

        if (!isNil(controlRequestDto.limit)) {
            controlRequestDto.limit = requestDto.limit;
        }

        if (audit.frameworkType !== AuditTypeEnum.CUSTOM) {
            const frameworkTag = FrameworkTypeTags.get(audit.frameworkType);
            if (!isNil(frameworkTag)) {
                controlRequestDto.frameworkTags = [frameworkTag];
            }
        }

        return this.controlsOrchestrationService.listControlsGroupBy(controlRequestDto, account);
    }

    public async getPaginatedCustomerRequestMessageFiles({
        accountId,
        userEntryId,
        auditId,
        requestId,
        messageId,
        page,
        limit,
    }: PaginatedCustomerRequestMessageFilesParams): Promise<
        PaginationType<CustomerRequestMessageFileData>
    > {
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            userEntryId,
            auditId,
            accountId,
        );

        const paginatedData =
            await this.customerRequestMessageFileRepository.getRequestMessageFilesByRequestAndMessageId(
                auditId,
                requestId,
                messageId,
                page,
                limit,
            );

        const signedUrls = await Promise.allSettled(
            paginatedData.data.map(async ({ file }) => {
                const fileData = await this.downloader.getDownloadUrl(file);

                return fileData.signedUrl;
            }),
        );

        const data = paginatedData.data.map(({ id, name }, index) => {
            let url: string | null = null;

            const signedUrlPromiseResult = signedUrls[index];

            if (signedUrlPromiseResult.status === 'fulfilled') {
                url = signedUrlPromiseResult.value || null;
            }

            return {
                id,
                name,
                url,
            } as CustomerRequestMessageFileData;
        });

        return {
            ...paginatedData,
            data,
        };
    }

    async getAvailablePersonnelForAudit(
        accountId: AccountIdType,
        entryId: string,
        auditId: string,
        dto: AuditPersonnelRequestDto,
    ): Promise<PaginationType<Personnel>> {
        const { auditPersonnelType, limit, page = 1, q: personnelSearchTerms } = dto;

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        const audit = await auditorFrameworkRepository.findOneOrFail({
            where: { id: auditId, account: { id: accountId } },
        });

        const user = await this.usersCoreService.getUserWithSupportByEntryId(entryId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                entryId,
                audit.id,
                accountId,
            );
        }

        const { startDate, endDate } = getAuditPeriodByFramework(audit);

        return this.personnelRepository.getPaginatedPersonnelByTypeWithinRange(
            auditPersonnelType,
            startDate,
            endDate,
            page,
            limit,
            personnelSearchTerms,
        );
    }

    async getAuditPersonnelOrFail(
        accountId: AccountIdType,
        entryId: string,
        auditId: string,
        dto: AuditPersonnelRequestDto,
    ): Promise<PaginationType<Personnel>> {
        const { auditPersonnelType, limit, page = 1, q: personnelSearchTerms } = dto;

        const auditorFrameworkRepository = await this.auditorFrameworkRepository();

        const audit = await auditorFrameworkRepository.findOneOrFail({
            where: { id: auditId, account: { id: accountId } },
        });

        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            entryId,
            audit.id,
            accountId,
        );

        const latestSample = await this.auditSampleRepository.getLatestAuditSampleOrFail(auditId);

        return this.personnelRepository.getPaginatedPersonnelForAuditSample({
            audit,
            auditSampleId: latestSample.id,
            page,
            limit,
            auditPersonnelType,
            searchTerms: personnelSearchTerms,
        });
    }

    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }

    private async auditorFrameworkRepository(): Promise<AuditorFrameworkRepository> {
        const globalDataSource = await getGlobalDataSource();
        return getCustomRepository(AuditorFrameworkRepository, globalDataSource);
    }

    private async auditorClientRepository(): Promise<AuditorClientRepository> {
        const globalDataSource = await getGlobalDataSource();
        return getCustomRepository(AuditorClientRepository, globalDataSource);
    }

    private async serviceUserRepository(): Promise<ServiceUserRepository> {
        const globalDataSource = await getGlobalDataSource();
        return getCustomRepository(ServiceUserRepository, globalDataSource);
    }

    private async auditorFrameworkAuditorsRepository(): Promise<
        Repository<AuditorFrameworkAuditors>
    > {
        const globalDataSource = await getGlobalDataSource();
        return globalDataSource.getRepository(AuditorFrameworkAuditors);
    }

    private get auditListViewRepository(): Repository<AuditorFrameworkListView> {
        return this.getTenantRepository(AuditorFrameworkListView);
    }

    private get customerRequestRepository(): CustomerRequestRepository {
        return this.getCustomTenantRepository(CustomerRequestRepository);
    }

    private get auditSampleRepository(): AuditSampleRepository {
        return this.getCustomTenantRepository(AuditSampleRepository);
    }

    private get customerRequestMessageFileRepository(): CustomerRequestMessageFileRepository {
        return this.getCustomTenantRepository(CustomerRequestMessageFileRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }
}
