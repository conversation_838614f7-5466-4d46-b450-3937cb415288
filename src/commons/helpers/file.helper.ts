import { ErrorCode } from '@drata/enums';
import { BadRequestException } from '@nestjs/common';
import { addLeadingSlash } from '@nestjs/common/utils/shared.utils';
import { insertTextBeforeFileExtension } from 'commons/helpers/string.helper';
import { FileTypes } from 'commons/maps/file-extensions-and-mime-types-validation.map';
import { FileBufferType } from 'commons/types/file-buffer.type';
import config from 'config';
import crypto from 'crypto';
import { format } from 'date-fns';
import { isEmpty, replace } from 'lodash';
import moment from 'moment';
import path from 'path';

type FileNameInfo = {
    fileName: string;
    fileExtension: string;
};

const MAX_PATH_LENGTH_FOR_WINDOWS = 260;
const MAX_FILENAME_LENGTH_FOR_WINDOWS = 100;
// Reserved characters for path where the zip file will be extracted in the customer machine (e.g. C:\Users\<USER>\Downloads )
const PATH_PREFIX_LENGTH_FOR_WINDOWS = 30;

function validateOriginalFileNameOrFail(originalFileName: string): void {
    if (originalFileName.length >= config.get('db.varcharLength')) {
        throw new BadRequestException(ErrorCode[ErrorCode.INVALID_FILE_NAME]);
    }
}

function getFileNameFromPath(file: string): FileNameInfo {
    const fileName: string = path.basename(file);
    const fileExtension = path.extname(file);

    return {
        fileName,
        fileExtension,
    };
}

function sanitizeFileName(name: string): string {
    return replace(name, /(?:\.(?![^.]+$)|[^\w.]+)/g, '-');
}

/**
 * Based on local dev or on a server
 * the system needs to know to load the file
 * on the "src" or "dist" path.
 *
 * @param filePath The file path after "src" or "dist"
 */
function pathResolve(filePath: string): string {
    const prefix = config.get('api.pathPrefix');

    if (isEmpty(filePath) || isEmpty(prefix)) {
        return '';
    }

    return path.resolve(`${prefix}${addLeadingSlash(filePath)}`);
}

function makeFileNamesUniqueByEnumeration<T extends FileBufferType>(files: T[]): T[] {
    const fileNameMapAndCount = new Map<string, number>();

    return files.map(item => {
        const originalFileName = item.filename;
        const itemNameAlreadyInList = fileNameMapAndCount.get(originalFileName);
        if (itemNameAlreadyInList) {
            item.filename = insertTextBeforeFileExtension(
                originalFileName,
                `_${itemNameAlreadyInList}`,
            );
        }
        const updatedNameCount = itemNameAlreadyInList ? itemNameAlreadyInList + 1 : 1;
        fileNameMapAndCount.set(originalFileName, updatedNameCount);

        return item;
    });
}

function getMimeTypeFromFileTypes(fileName: string): string | null {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (!extension) return null;

    for (const [, fileTypes] of FileTypes.entries()) {
        for (const fileType of fileTypes) {
            if (fileType.extension.toLowerCase() === `.${extension}`) {
                return fileType.mimeTypes[0] || null;
            }
        }
    }

    return null;
}

/**
 * Generates a short hash ID for a given path.
 */
function abbreviate(name) {
    return crypto.createHash('md5').update(name).digest('hex').slice(0, 8);
}

/**
 * Generates an HTML manifest file that maps abbreviated filenames to their original paths.
 * The HTML includes clickable links that show the original routes.
 */
function generateHtmlManifest(manifest: {
    [key: string]: { originalPath: string; abbreviatedPath: string };
}): string {
    const manifestEntries = Object.entries(manifest);

    if (manifestEntries.length === 0) {
        return `<!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>File Manifest</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #333; }
                        .no-files { color: #666; font-style: italic; }
                    </style>
                </head>
                <body>
                    <h1>File Manifest</h1>
                    <p class="no-files">No abbreviated files found.</p>
                </body>
                </html>`;
    }

    const tableRows = manifestEntries
        .map(([abbreviatedFilename, paths]) => {
            return `        <tr>
            <td><code>${abbreviatedFilename}</code></td>
            <td><a href="${paths.abbreviatedPath}" title="${abbreviatedFilename}" download>${paths.originalPath}</a></td>
        </tr>`;
        })
        .join('\n');

    return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Control Evidence Manifest</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        line-height: 1.6;
                    }
                    h1 {
                        color: #333;
                        border-bottom: 2px solid #007acc;
                        padding-bottom: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                    }
                    th, td {
                        padding: 12px;
                        text-align: left;
                        border-bottom: 1px solid #ddd;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                        color: #333;
                    }
                    tr:hover {
                        background-color: #f5f5f5;
                    }
                    code {
                        background-color: #f1f1f1;
                        padding: 2px 4px;
                        border-radius: 3px;
                        font-family: 'Courier New', monospace;
                    }
                    a {
                        color: #007acc;
                        text-decoration: none;
                    }
                    a:hover {
                        text-decoration: underline;
                    }
                    .description {
                        color: #666;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>Control Evidence Manifest</h1>
                <p class="description">
                    This manifest shows the mapping between abbreviated filenames and their original paths.
                    Files were abbreviated to comply with Windows path length limitations.
                </p>
                <table>
                    <thead>
                        <tr>
                            <th>Abbreviated Filename</th>
                            <th>Original Path</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </body>
            </html>`;
}

function abbreviateFilenamesForWindows(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
) {
    const exceedsFilePathLimitForWindows = evidenceFilesJson.filter(file => {
        const relativeFilePath = file.filename;
        const fileName = path.basename(relativeFilePath);
        return (
            relativeFilePath.length >
                MAX_PATH_LENGTH_FOR_WINDOWS - PATH_PREFIX_LENGTH_FOR_WINDOWS ||
            fileName.length > MAX_FILENAME_LENGTH_FOR_WINDOWS
        );
    });

    if (!isEmpty(exceedsFilePathLimitForWindows)) {
        const manifest: { [key: string]: { originalPath: string; abbreviatedPath: string } } = {};
        exceedsFilePathLimitForWindows.forEach(file => {
            const dirs = file.filename.split('/');
            const fileName = dirs.pop() as unknown as string;
            const extension = path.extname(fileName);
            const fileNameWithoutExtension = path.basename(fileName, extension);
            const abbreviatedFileName = abbreviate(fileNameWithoutExtension);
            file.filename = `${dirs.join('/')}/${abbreviatedFileName}${extension}`; // updates by reference
            manifest[`${abbreviatedFileName}${extension}`] = {
                originalPath: `${dirs.join('/')}/${fileName}`,
                abbreviatedPath: `${dirs.join('/')}/${abbreviatedFileName}${extension}`,
            };
        });

        // Generate HTML manifest content
        const htmlContent = generateHtmlManifest(manifest);

        // Add manifest.html to evidenceFilesJson
        evidenceFilesJson.push({
            filename: 'control evidence manifest.html',
            stream: Buffer.from(htmlContent).toString('base64'),
        });
    }
}

function removeDuplicatedFiles(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
) {
    const filenames = [...new Set(evidenceFilesJson.map(file => file.filename))];
    return filenames.map(
        filename =>
            evidenceFilesJson.find(file => file.filename === filename) as {
                stream: string;
                filename: string;
            },
    );
}

/*
 * Generate a common file name for monitor test reports
 * @param type - Report type ('included' for failing resources, 'excluded' for excluded results)
 * @param testId - The test ID
 * @param testName - The test name (will be sanitized)
 * @param date - Optional date to use (defaults to current date)
 * @returns Common file name without extension
 */
function generateMonitorTestReportFileName(
    type: 'included' | 'excluded',
    testId: number,
    testName: string,
    date?: Date,
): string {
    const reportType = type === 'included' ? 'Failing-Resources' : 'Excluded-Results';
    const sanitizedTestName = sanitizeFileName(testName);
    const formattedDate = format(date || moment().toDate(), 'MMddyyyy');

    return sanitizeFileName(
        `${reportType}-For-Test-${testId}-${sanitizedTestName}-${formattedDate}`,
    );
}

export {
    abbreviate,
    abbreviateFilenamesForWindows,
    generateMonitorTestReportFileName,
    getFileNameFromPath,
    getMimeTypeFromFileTypes,
    makeFileNamesUniqueByEnumeration,
    MAX_PATH_LENGTH_FOR_WINDOWS,
    pathResolve,
    removeDuplicatedFiles,
    sanitizeFileName,
    validateOriginalFileNameOrFail,
};
