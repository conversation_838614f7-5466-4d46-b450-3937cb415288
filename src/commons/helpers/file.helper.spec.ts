import {
    abbreviateFilenamesForWindows,
    generateMonitorTestReportFileName,
    getMimeTypeFromFileTypes,
    makeFileNamesUniqueByEnumeration,
    MAX_PATH_LENGTH_FOR_WINDOWS,
    pathResolve,
    removeDuplicatedFiles,
    sanitizeFileName,
} from 'commons/helpers/file.helper';
import { FileBufferType } from 'commons/types/file-buffer.type';
import config from 'config';
import moment from 'moment';
import path from 'path';

describe('file helper', () => {
    test('change / by -', () => {
        expect(sanitizeFileName('evidence/library 215')).toEqual('evidence-library-215');
    });

    test('removes special characters', () => {
        expect(sanitizeFileName('evidence/library @215?:!')).not.toContain('@!:?/');
    });

    test('removes special characters keeping the file extension', () => {
        const filename = 'evidence/library @215?:!.pdf';
        const sanitizedName = sanitizeFileName(filename);
        expect(sanitizedName).not.toContain('@!:?/');
        expect(sanitizedName).toContain('.pdf');
    });

    test('file path is an EMPTY string if nothing was passed in', () => {
        const output = pathResolve('');
        expect(output).toEqual('');
    });

    test('file path is as expected without leading slash', () => {
        const prefix = config.get('api.pathPrefix');
        const output = pathResolve('test/path.ts');
        expect(output).toEqual(path.resolve(`${prefix}/test/path.ts`));
    });

    test('file path is as expected with leading slash', () => {
        const prefix = config.get('api.pathPrefix');
        const output = pathResolve('/test/path.ts');
        expect(output).toEqual(path.resolve(`${prefix}/test/path.ts`));
    });
});

describe('makeFileNamesUniqueByEnumeration', () => {
    test('Given a list of file buffers with unique names, it should return with the original name', () => {
        const testFileBuffers: FileBufferType[] = [
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.pdf',
                stream: Buffer.alloc(0),
            },
        ];
        expect(makeFileNamesUniqueByEnumeration(testFileBuffers)).toEqual(testFileBuffers);
    });

    describe('Given a list of file buffers with the same names', () => {
        const testFileBuffers: FileBufferType[] = [
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
        ];

        it('should return with enumerated names', () => {
            expect(makeFileNamesUniqueByEnumeration(testFileBuffers)).toEqual([
                {
                    filename: 'sampleFile.doc',
                    stream: Buffer.alloc(0),
                },
                {
                    filename: 'sampleFile_1.doc',
                    stream: Buffer.alloc(0),
                },
                {
                    filename: 'sampleFile_2.doc',
                    stream: Buffer.alloc(0),
                },
            ]);
        });
    });
});

describe('getMimeTypeFromFileTypes', () => {
    it('should return the correct MIME type for a known file extension', () => {
        expect(getMimeTypeFromFileTypes('document.pdf')).toBe('application/pdf');
        expect(getMimeTypeFromFileTypes('image.jpg')).toBe('image/jpeg');
        expect(getMimeTypeFromFileTypes('data.json')).toBe('application/json');
    });

    it('should return null for files without an extension', () => {
        expect(getMimeTypeFromFileTypes('file')).toBeNull();
    });

    it('should return null for unknown file extensions', () => {
        expect(getMimeTypeFromFileTypes('unknownfile.xyz')).toBeNull();
    });

    it('should handle uppercase file extensions correctly', () => {
        expect(getMimeTypeFromFileTypes('IMAGE.JPG')).toBe('image/jpeg');
        expect(getMimeTypeFromFileTypes('Document.PDF')).toBe('application/pdf');
    });

    it('should return null if the file extension is empty after a dot', () => {
        expect(getMimeTypeFromFileTypes('file.')).toBeNull();
    });

    it('should handle file names with multiple dots correctly', () => {
        expect(getMimeTypeFromFileTypes('archive.tar.gz')).toBeNull();
        expect(getMimeTypeFromFileTypes('project.report.pdf')).toBe('application/pdf');
    });
});

describe('abbreviateFilenamesForWindows', () => {
    // Type assertion function for manifest files
    function assertManifestExists(
        manifest: { stream: string; filename: string } | undefined,
    ): asserts manifest is { stream: string; filename: string } {
        expect(manifest).toBeDefined();
    }

    it('should not modify files when no filenames exceed Windows path limit', () => {
        const evidenceFilesJson = [
            {
                filename: 'short/path/file.txt',
                stream: 'base64content1',
            },
            {
                filename: 'another/short/file.pdf',
                stream: 'base64content2',
            },
        ];

        const originalLength = evidenceFilesJson.length;
        abbreviateFilenamesForWindows(evidenceFilesJson);

        expect(evidenceFilesJson).toHaveLength(originalLength);
        expect(evidenceFilesJson[0].filename).toBe('short/path/file.txt');
        expect(evidenceFilesJson[1].filename).toBe('another/short/file.pdf');
    });

    it('should abbreviate filenames that exceed Windows path limit and add manifest', () => {
        // Create a filename that exceeds MAX_PATH_LENGTH_FOR_WINDOWS (260 characters)
        const longPath = 'very/long/path/'.repeat(20); // Creates a path longer than 260 chars
        const longFilename = `${longPath}longDocument.pdf`;

        const evidenceFilesJson = [
            {
                filename: longFilename,
                stream: 'base64content1',
            },
            {
                filename: 'short/path/file.txt',
                stream: 'base64content2',
            },
        ];

        expect(longFilename.length).toBeGreaterThan(MAX_PATH_LENGTH_FOR_WINDOWS);

        abbreviateFilenamesForWindows(evidenceFilesJson);

        // Should have original files plus manifest.html
        expect(evidenceFilesJson).toHaveLength(3);

        // Long filename should be abbreviated
        expect(evidenceFilesJson[0].filename).not.toBe(longFilename);
        expect(evidenceFilesJson[0].filename.length).toBeLessThan(longFilename.length);
        expect(evidenceFilesJson[0].filename).toMatch(/\.pdf$/); // Should keep extension

        // Short filename should remain unchanged
        expect(evidenceFilesJson[1].filename).toBe('short/path/file.txt');

        // Manifest should be added
        const manifest = evidenceFilesJson.find(
            file => file.filename === 'control evidence manifest.html',
        );
        assertManifestExists(manifest);
        expect(manifest.stream).toBeDefined();

        // Verify manifest content is HTML
        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('<!DOCTYPE html>');
        expect(manifestContent).toContain('<title>Control Evidence Manifest</title>');
        expect(manifestContent).toContain('<table>');

        // The abbreviated filename should be in the HTML content
        const abbreviatedFilename = evidenceFilesJson[0].filename.split('/').pop();
        expect(manifestContent).toContain(abbreviatedFilename as unknown as string);
        expect(manifestContent).toContain(longFilename);
    });

    it('should handle multiple files exceeding the path limit', () => {
        const longPath1 = 'very/long/path/number/one/'.repeat(10);
        const longPath2 = 'another/extremely/long/path/number/two/'.repeat(8);
        const longFilename1 = `${longPath1}document1.pdf`;
        const longFilename2 = `${longPath2}document2.xlsx`;

        const evidenceFilesJson = [
            {
                filename: longFilename1,
                stream: 'base64content1',
            },
            {
                filename: longFilename2,
                stream: 'base64content2',
            },
        ];

        expect(longFilename1.length).toBeGreaterThan(MAX_PATH_LENGTH_FOR_WINDOWS);
        expect(longFilename2.length).toBeGreaterThan(MAX_PATH_LENGTH_FOR_WINDOWS);

        abbreviateFilenamesForWindows(evidenceFilesJson);

        // Should have 2 original files plus manifest.html
        expect(evidenceFilesJson).toHaveLength(3);

        // Both long filenames should be abbreviated
        expect(evidenceFilesJson[0].filename).not.toBe(longFilename1);
        expect(evidenceFilesJson[1].filename).not.toBe(longFilename2);
        expect(evidenceFilesJson[0].filename).toMatch(/\.pdf$/);
        expect(evidenceFilesJson[1].filename).toMatch(/\.xlsx$/);

        // Manifest should contain both abbreviated files
        const manifest = evidenceFilesJson.find(
            file => file.filename === 'control evidence manifest.html',
        );
        assertManifestExists(manifest);

        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('<!DOCTYPE html>');
        expect(manifestContent).toContain('<table>');

        // Both original filenames should be in the HTML content
        expect(manifestContent).toContain(longFilename1);
        expect(manifestContent).toContain(longFilename2);
    });

    it('should preserve file extensions when abbreviating', () => {
        const longPath = 'very/long/path/'.repeat(20);
        const longFilename = `${longPath}document.complex.extension.tar.gz`;

        const evidenceFilesJson = [
            {
                filename: longFilename,
                stream: 'base64content',
            },
        ];

        abbreviateFilenamesForWindows(evidenceFilesJson);

        expect(evidenceFilesJson[0].filename).toMatch(/\.gz$/);
        expect(evidenceFilesJson[0].filename).not.toBe(longFilename);
    });

    it('should handle files without extensions', () => {
        const longPath = 'very/long/path/'.repeat(20);
        const longFilename = `${longPath}document_without_extension`;

        const evidenceFilesJson = [
            {
                filename: longFilename,
                stream: 'base64content',
            },
        ];

        abbreviateFilenamesForWindows(evidenceFilesJson);

        expect(evidenceFilesJson[0].filename).not.toBe(longFilename);
        expect(evidenceFilesJson[0].filename.length).toBeLessThan(longFilename.length);

        // Should have manifest
        expect(evidenceFilesJson).toHaveLength(2);
        const manifest = evidenceFilesJson.find(
            file => file.filename === 'control evidence manifest.html',
        );
        assertManifestExists(manifest);

        // Verify it's HTML content
        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('<!DOCTYPE html>');
        expect(manifestContent).toContain(longFilename);
    });

    describe('filtering logic for Windows path limits', () => {
        it('should identify files that exceed path length limit exactly at boundary', () => {
            // Create a path that is exactly at the limit (230 characters)
            const exactLimitPath = 'a'.repeat(230 - 8) + 'file.txt'; // 230 chars total
            const overLimitPath = 'a'.repeat(231 - 8) + 'file.txt'; // 231 chars total

            const evidenceFilesJson = [
                {
                    filename: exactLimitPath,
                    stream: 'content1',
                },
                {
                    filename: overLimitPath,
                    stream: 'content2',
                },
            ];

            expect(exactLimitPath.length).toBe(230); // At limit
            expect(overLimitPath.length).toBe(231); // Over limit

            abbreviateFilenamesForWindows(evidenceFilesJson);

            // File at exact limit should NOT be abbreviated
            expect(evidenceFilesJson[0].filename).toBe(exactLimitPath);

            // File over limit should be abbreviated
            expect(evidenceFilesJson[1].filename).not.toBe(overLimitPath);
            expect(evidenceFilesJson[1].filename).toMatch(/\.txt$/);

            // Should have manifest for the one abbreviated file
            expect(evidenceFilesJson).toHaveLength(3);
            const manifest = evidenceFilesJson.find(
                file => file.filename === 'control evidence manifest.html',
            );
            assertManifestExists(manifest);
        });

        it('should identify files that exceed filename length limit exactly at boundary', () => {
            // Create filenames that are exactly at and over the 100 character limit
            const exactLimitFilename = 'a'.repeat(96) + '.txt'; // 100 chars total
            const overLimitFilename = 'a'.repeat(97) + '.txt'; // 101 chars total

            const evidenceFilesJson = [
                {
                    filename: `short/path/${exactLimitFilename}`,
                    stream: 'content1',
                },
                {
                    filename: `short/path/${overLimitFilename}`,
                    stream: 'content2',
                },
            ];

            expect(exactLimitFilename.length).toBe(100); // At limit
            expect(overLimitFilename.length).toBe(101); // Over limit

            abbreviateFilenamesForWindows(evidenceFilesJson);

            // File at exact limit should NOT be abbreviated
            expect(evidenceFilesJson[0].filename).toBe(`short/path/${exactLimitFilename}`);

            // File over limit should be abbreviated
            expect(evidenceFilesJson[1].filename).not.toBe(`short/path/${overLimitFilename}`);
            expect(evidenceFilesJson[1].filename).toMatch(/\.txt$/);

            // Should have manifest for the one abbreviated file
            expect(evidenceFilesJson).toHaveLength(3);
        });

        it('should handle files that exceed both path and filename limits', () => {
            // Create a file that exceeds both limits
            const longPath = 'very/long/path/'.repeat(20); // Long path
            const longFilename = 'a'.repeat(150) + '.pdf'; // Long filename (154 chars)
            const fullPath = `${longPath}${longFilename}`;

            const evidenceFilesJson = [
                {
                    filename: fullPath,
                    stream: 'content',
                },
            ];

            expect(fullPath.length).toBeGreaterThan(230); // Exceeds path limit
            expect(longFilename.length).toBeGreaterThan(100); // Exceeds filename limit

            abbreviateFilenamesForWindows(evidenceFilesJson);

            // Should be abbreviated
            expect(evidenceFilesJson[0].filename).not.toBe(fullPath);
            expect(evidenceFilesJson[0].filename).toMatch(/\.pdf$/);

            // Should have manifest
            expect(evidenceFilesJson).toHaveLength(2);
        });

        it('should not modify files with short paths and short filenames', () => {
            const evidenceFilesJson = [
                {
                    filename: 'short.txt',
                    stream: 'content1',
                },
                {
                    filename: 'folder/medium-length-filename.pdf',
                    stream: 'content2',
                },
                {
                    filename: 'a/b/c/d/e/f/normal-file.doc',
                    stream: 'content3',
                },
            ];

            // Verify all are under limits
            evidenceFilesJson.forEach(file => {
                expect(file.filename.length).toBeLessThanOrEqual(230);
                expect(path.basename(file.filename).length).toBeLessThanOrEqual(100);
            });

            const originalFilenames = evidenceFilesJson.map(f => f.filename);
            abbreviateFilenamesForWindows(evidenceFilesJson);

            // Should remain unchanged
            expect(evidenceFilesJson).toHaveLength(3);
            evidenceFilesJson.forEach((file, index) => {
                expect(file.filename).toBe(originalFilenames[index]);
            });
        });

        it('should handle edge case with empty filename', () => {
            const evidenceFilesJson = [
                {
                    filename: '',
                    stream: 'content',
                },
            ];

            abbreviateFilenamesForWindows(evidenceFilesJson);

            // Should remain unchanged (empty string doesn't exceed limits)
            expect(evidenceFilesJson).toHaveLength(1);
            expect(evidenceFilesJson[0].filename).toBe('');
        });

        it('should handle files with only directory path and no filename', () => {
            const longPath = 'very/long/directory/path/'.repeat(10); // Ends with slash

            const evidenceFilesJson = [
                {
                    filename: longPath,
                    stream: 'content',
                },
            ];

            expect(longPath.length).toBeGreaterThan(230);
            expect(path.basename(longPath)).toBe(''); // Empty basename

            abbreviateFilenamesForWindows(evidenceFilesJson);

            // Should be abbreviated based on path length
            expect(evidenceFilesJson[0].filename).not.toBe(longPath);
            expect(evidenceFilesJson).toHaveLength(2); // Original + manifest
        });
    });
});

describe('removeDuplicatedFiles', () => {
    it('should return an empty array when given an empty array', () => {
        const result = removeDuplicatedFiles([]);
        expect(result).toEqual([]);
    });

    it('should return the same array when there are no duplicates', () => {
        const files = [
            { filename: 'file1.txt', stream: 'content1' },
            { filename: 'file2.pdf', stream: 'content2' },
            { filename: 'file3.doc', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);
        expect(result).toEqual(files);
        expect(result).toHaveLength(3);
    });

    it('should remove duplicate files and keep the first occurrence', () => {
        const files = [
            { filename: 'document.pdf', stream: 'first-content' },
            { filename: 'image.jpg', stream: 'image-content' },
            { filename: 'document.pdf', stream: 'second-content' },
            { filename: 'text.txt', stream: 'text-content' },
            { filename: 'document.pdf', stream: 'third-content' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result).toEqual([
            { filename: 'document.pdf', stream: 'first-content' },
            { filename: 'image.jpg', stream: 'image-content' },
            { filename: 'text.txt', stream: 'text-content' },
        ]);
    });

    it('should handle files with identical filenames but different content', () => {
        const files = [
            { filename: 'report.xlsx', stream: 'version1' },
            { filename: 'report.xlsx', stream: 'version2' },
            { filename: 'report.xlsx', stream: 'version3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({ filename: 'report.xlsx', stream: 'version1' });
    });

    it('should preserve order of unique files', () => {
        const files = [
            { filename: 'z-file.txt', stream: 'z-content' },
            { filename: 'a-file.txt', stream: 'a-content' },
            { filename: 'm-file.txt', stream: 'm-content' },
            { filename: 'a-file.txt', stream: 'duplicate-a' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result.map(f => f.filename)).toEqual(['z-file.txt', 'a-file.txt', 'm-file.txt']);
        expect(result[1].stream).toBe('a-content'); // First occurrence preserved
    });

    it('should handle files with special characters in filenames', () => {
        const files = [
            { filename: 'file with spaces.txt', stream: 'content1' },
            { filename: 'file-with-dashes.pdf', stream: 'content2' },
            { filename: 'file with spaces.txt', stream: 'duplicate-content' },
            { filename: 'file_with_underscores.doc', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result.find(f => f.filename === 'file with spaces.txt')?.stream).toBe('content1');
    });

    it('should handle single file array', () => {
        const files = [{ filename: 'single-file.txt', stream: 'single-content' }];

        const result = removeDuplicatedFiles(files);

        expect(result).toEqual(files);
        expect(result).toHaveLength(1);
    });

    it('should handle files with empty filenames', () => {
        const files = [
            { filename: '', stream: 'content1' },
            { filename: 'normal-file.txt', stream: 'content2' },
            { filename: '', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({ filename: '', stream: 'content1' });
        expect(result[1]).toEqual({ filename: 'normal-file.txt', stream: 'content2' });
    });

    it('should handle files with very long filenames', () => {
        const longFilename = 'very-long-filename-'.repeat(20) + '.txt';
        const files = [
            { filename: longFilename, stream: 'content1' },
            { filename: 'short.txt', stream: 'content2' },
            { filename: longFilename, stream: 'duplicate-content' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(2);
        expect(result[0].filename).toBe(longFilename);
        expect(result[0].stream).toBe('content1');
    });

    describe('generateMonitorTestReportFileName', () => {
        it('should generate correct filename for included type', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'included',
                123,
                'Test Name',
                testDate,
            );
            expect(result).toBe('Failing-Resources-For-Test-123-Test-Name-01152024');
        });

        it('should generate correct filename for excluded type', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'excluded',
                456,
                'Another Test',
                testDate,
            );
            expect(result).toBe('Excluded-Results-For-Test-456-Another-Test-01152024');
        });

        it('should sanitize test name with special characters', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'included',
                789,
                'Test/Name @Special!',
                testDate,
            );
            expect(result).toBe('Failing-Resources-For-Test-789-Test-Name-Special-01152024');
        });

        it('should use current date when no date provided', () => {
            const result = generateMonitorTestReportFileName('included', 123, 'Test Name');
            // Should contain today's date in MMddyyyy format
            expect(result).toMatch(/^Failing-Resources-For-Test-123-Test-Name-\d{8}$/);
        });
    });
});
