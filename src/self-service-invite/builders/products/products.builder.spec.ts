import { FrameworkTag } from '@drata/enums';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { SalesforceBundlePlans } from 'dependencies/crm/salesforce/enums/salesforce-bundle-plans.enum';
import { mock } from 'jest-mock-extended';
import { AccountPlan } from 'plan-and-usage/enums/account-plan.enum';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { CustomFrameworks } from 'self-service-invite/builders/products/custom-frameworks';
import { EntitlementsBuilder } from 'self-service-invite/builders/products/entitlements.builder';
import { ProductsBuilder } from 'self-service-invite/builders/products/products.builder';
import { WorkspaceBuilder } from 'self-service-invite/builders/products/workspace.builder';

describe('ProductsBuilder class', () => {
    function setupProductsBuilder() {
        return new ProductsBuilder(
            mock<WorkspaceBuilder>(),
            mock<EntitlementsBuilder>(),
            mock<CustomFrameworks>(),
            mock<PolloLogger<PolloMessage>>(),
        );
    }

    function setupProductResult(product2Id: string[]) {
        return product2Id.map(product => {
            return {
                Product2Id: product,
                Quantity: 1.0,
                Name: 'test name',
                Start_F__c: '2023-07-12',
                End_F__c: '2026-07-11',
            };
        });
    }

    describe('buildFrameworks method', () => {
        it('should map 01t8c00000M8kZxAAJ to FrameworkTag.HIPAA', () => {
            const recordsMock = setupProductResult(['01t8c00000M8kZxAAJ']);
            const productsBuilderMock = setupProductsBuilder();

            expect(productsBuilderMock.buildFrameworks(recordsMock)[0]).toBe(FrameworkTag.HIPAA);
        });

        it('should map 01t8c00000M8njMAAR to FrameworkTag.SOX_ITGC', () => {
            const recordsMock = setupProductResult(['01t8c00000M8njMAAR']);
            const productsBuilderMock = setupProductsBuilder();

            expect(productsBuilderMock.buildFrameworks(recordsMock)[0]).toBe(FrameworkTag.SOX_ITGC);
        });

        it('should map 2 products to their respective frameworks', () => {
            const recordsMock = setupProductResult(['01t8c00000M8kZxAAJ', '01t8c00000M8njMAAR']);
            const productsBuilderMock = setupProductsBuilder();

            const enabledControlsForFrameworkTags =
                productsBuilderMock.buildFrameworks(recordsMock);

            expect(enabledControlsForFrameworkTags?.[0]).toBe(FrameworkTag.HIPAA);
            expect(enabledControlsForFrameworkTags?.[1]).toBe(FrameworkTag.SOX_ITGC);
        });

        it('should map 3 products to their respective frameworks', () => {
            const recordsMock = setupProductResult([
                '01t8c00000M8kZxAAJ',
                '01t8c00000M8njMAAR',
                '01t8c00000M8njNAAR',
            ]);
            const productsBuilderMock = setupProductsBuilder();

            const enabledControlsForFrameworkTags =
                productsBuilderMock.buildFrameworks(recordsMock);

            expect(enabledControlsForFrameworkTags?.[0]).toBe(FrameworkTag.HIPAA);
            expect(enabledControlsForFrameworkTags?.[1]).toBe(FrameworkTag.SOX_ITGC);
            expect(enabledControlsForFrameworkTags?.[2]).toBe(FrameworkTag.MSSSPA11);
        });
    });

    describe('buildProducts method', () => {
        it('should map 2 products to their respective entitlements and frameworks', () => {
            const productsBuilderMock = setupProductsBuilder();

            const products = productsBuilderMock.buildProducts(
                'mockOpportunityId',
                setupProductResult(['01t8c00000M8kZxAAJ', '01t8c00000M8njMAAR']),
            );

            expect(products.enabledControlsForFrameworkTags?.[0]).toBe(FrameworkTag.HIPAA);
            expect(products.enabledControlsForFrameworkTags?.[1]).toBe(FrameworkTag.SOX_ITGC);
        });

        it('should map 3 products to their respective entitlements and frameworks', () => {
            const productsBuilderMock = setupProductsBuilder();

            const products = productsBuilderMock.buildProducts(
                'mockOpportunityId',
                setupProductResult([
                    '01t8c00000M8kZxAAJ',
                    '01t8c00000M8njMAAR',
                    '01t8c00000M8T6sAAF',
                ]),
            );

            expect(products.enabledControlsForFrameworkTags?.[0]).toBe(FrameworkTag.HIPAA);
            expect(products.enabledControlsForFrameworkTags?.[1]).toBe(FrameworkTag.SOX_ITGC);
            expect(products.enabledControlsForFrameworkTags?.[2]).toBe(FrameworkTag.CCPA);
        });
    });

    describe('getSalesforcePlan method', () => {
        it('should return LEGACY when no plan is found', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(productsBuilderMock.getSalesforcePlan([])).toBe(AccountPlan.LEGACY);
        });

        it('should return ESSENTIAL according bundle code SalesforceBundlePlans.ESSENTIAL or SalesforceBundlePlans.PARTNER_ESSENTIAL', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([SalesforceBundlePlans.ESSENTIAL]),
                ),
            ).toBe(AccountPlan.FOUNDATION);
            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([SalesforceBundlePlans.PARTNER_ESSENTIAL]),
                ),
            ).toBe(AccountPlan.FOUNDATION);
        });

        it('should return FOUNDATION according bundle code SalesforceBundlePlans.FOUNDATION or SalesforceBundlePlans.PARTNER_FOUNDATION', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([
                        SalesforceBundlePlans.FOUNDATION,
                        SalesforceBundlePlans.ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_ESSENTIAL,
                    ]),
                ),
            ).toBe(AccountPlan.ADVANCED);
            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([
                        SalesforceBundlePlans.PARTNER_FOUNDATION,
                        SalesforceBundlePlans.ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_ESSENTIAL,
                    ]),
                ),
            ).toBe(AccountPlan.ADVANCED);
        });

        it('should return ADVANCED according bundle code SalesforceBundlePlans.ADVANCED or SalesforceBundlePlans.PARTNER_ADVANCED', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([
                        SalesforceBundlePlans.ADVANCED,
                        SalesforceBundlePlans.FOUNDATION,
                        SalesforceBundlePlans.ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_FOUNDATION,
                    ]),
                ),
            ).toBe(AccountPlan.ENTERPRISE);
            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([
                        SalesforceBundlePlans.PARTNER_ADVANCED,
                        SalesforceBundlePlans.FOUNDATION,
                        SalesforceBundlePlans.ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_ESSENTIAL,
                        SalesforceBundlePlans.PARTNER_FOUNDATION,
                    ]),
                ),
            ).toBe(AccountPlan.ENTERPRISE);
        });

        it('should return DRATA_ESSENTIALS according bundle code SalesforceBundlePlans.DRATA_ESSENTIALS', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(
                productsBuilderMock.getSalesforcePlan(
                    setupProductResult([SalesforceBundlePlans.DRATA_ESSENTIALS]),
                ),
            ).toBe(AccountPlan.DRATA_ESSENTIALS);
        });
    });

    describe('areProductsEmpty method', () => {
        it('should return true when all products are empty', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(productsBuilderMock.areProductsEmpty([], null, null, [])).toBe(true);
        });

        it('should return false when at least one product is not empty', () => {
            const productsBuilderMock = setupProductsBuilder();

            expect(
                productsBuilderMock.areProductsEmpty([], null, null, [
                    AccountEntitlementType.PUBLIC_API,
                ]),
            ).toBe(false);
            expect(productsBuilderMock.areProductsEmpty([FrameworkTag.CCPA], null, null, [])).toBe(
                false,
            );
            expect(productsBuilderMock.areProductsEmpty([], 1, null, [])).toBe(false);
            expect(productsBuilderMock.areProductsEmpty([], null, 1, [])).toBe(false);
        });
    });
});
