import { FrameworkTag } from '@drata/enums';

export const SalesforceProductIdToFrameworkTag = new Map<string, FrameworkTag>([
    ['01t8c00000R3eb4AAB', FrameworkTag.CCM],
    ['01t8c00000M8T6sAAF', FrameworkTag.CCPA],
    ['01t8c00000M8T7CAAV', FrameworkTag.CMMC],
    ['01t8c00000P7sT2AAJ', FrameworkTag.COBIT],
    ['01t8c00000QpsfLAAR', FrameworkTag.CYBER_ESSENTIALS_32],
    ['01tQr000007Xp4XIAS', FrameworkTag.DORA],
    ['01tQr000003nkUcIAI', FrameworkTag.FEDRAMP],
    ['01t8c00000P7wV3AAJ', FrameworkTag.FFIEC],
    ['01t8c00000M8T6wAAF', FrameworkTag.GDPR],
    ['01t8c00000M8kZxAAJ', FrameworkTag.HIPAA],
    ['01t8c00000M8T6iAAF', FrameworkTag.ISO270012022],
    ['01t8c00000QqbHyAAJ', FrameworkTag.ISO270172015],
    ['01t8c00000QqbI3AAJ', FrameworkTag.ISO270182019],
    ['01t8c00000M8T6mAAF', FrameworkTag.ISO27701],
    ['01t8c00000M8njNAAR', FrameworkTag.MSSSPA11],
    ['01tQr000006NM2nIAG', FrameworkTag.NIS2],
    ['01tQr000001wgeEIAQ', FrameworkTag.NISTAI],
    ['01t8c00000M8T77AAF', FrameworkTag.NISTCSF2],
    ['01t8c00000M8T7BAAV', FrameworkTag.NIST80053],
    ['01t8c00000M8T76AAF', FrameworkTag.NIST800171],
    ['01t8c00000M8T6rAAF', FrameworkTag.PCI4],
    ['01t8c00000M8T6hAAF', FrameworkTag.SOC_2],
    ['01t8c00000M8njMAAR', FrameworkTag.SOX_ITGC],
    ['01t8c00000On5ghAAB', FrameworkTag.CUSTOM],
    ['01tQr000007aN8YIAU', FrameworkTag.ISO420012023],
    ['01tRL00000A9gLdYAJ', FrameworkTag.DRATA_ESSENTIALS], // One of these is sandbox and one is prod
    ['01tQr000008UXd3IAG', FrameworkTag.DRATA_ESSENTIALS], // After next sandbox refresh we can clean up to 1
]);
