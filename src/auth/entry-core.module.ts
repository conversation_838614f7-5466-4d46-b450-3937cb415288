import { Module } from '@nestjs/common';
import { getDataSourceToken } from '@nestjs/typeorm';
import { EntryCoreSubscriber } from 'auth/observables/subscribers/entry-core.subscriber';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';

@ModuleType(ModuleTypes.CORE)
@Module({
    imports: [TypeOrmExtensionsModule.forGlobalCustomRepository([EntryRepository])],
    providers: [
        EntryCoreService,
        EntryCoreSubscriber,
        {
            inject: [getDataSourceToken()],
            provide: DrataDataSource,
            useFactory: (dataSource: DrataDataSource) => dataSource,
        },
    ],
    exports: [EntryCoreService],
})
export class EntryCoreModule {}
