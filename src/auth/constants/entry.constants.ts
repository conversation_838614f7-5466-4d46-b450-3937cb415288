export const ENTRY_EVENT_BRIDGE_DETAIL_TYPE = {
    ENTRY_CREATED: 'Entry:Created',
    ENTRY_UPDATED: 'Entry:Updated',
    ENTRY_DELETED: 'Entry:Deleted',
    ENTRY_RESTORED: 'Entry:Restored',
    ENTRY_SOFT_DELETED: 'Entry:SoftDeleted',
    ENTRY_SYNC_READ: 'Entry:SyncRead',
    ENTRY_SYNC_READ_SOFT_DELETED: 'Entry:SyncReadSoftDeleted',
} as const;

export const ENTRY_EVENT_BRIDGE_VERSION_ONE = 1;

export type EntryEventBridgeDetailType =
    (typeof ENTRY_EVENT_BRIDGE_DETAIL_TYPE)[keyof typeof ENTRY_EVENT_BRIDGE_DETAIL_TYPE];
