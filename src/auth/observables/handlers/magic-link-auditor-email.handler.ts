import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { MagicLinkAuditorEvent } from 'auth/observables/events/magic-link-auditor.event';
import { AuthService } from 'auth/services/auth.service';
import { EmailConfig } from 'commons/configs/email.config';
import { currentYear } from 'commons/helpers/date.helper';
import { getServerRegion } from 'commons/helpers/environment.helper';
import { GlobalEventHandler } from 'commons/observables/handlers/global-event-handler.base';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType, EmailTimeSensitivity } from 'commons/types/email-options.type';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { format } from 'util';

@EventsHandler(MagicLinkAuditorEvent)
export class MagicLinkAuditorEmailHandler extends GlobalEventHandler<MagicLinkAuditorEvent> {
    /**
     *
     * @param authService
     * @param emailConfig
     * @param emailService
     */
    constructor(
        private readonly authService: AuthService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
    ) {
        super();
    }

    /**
     *
     * @param event
     */
    async handleEvent(event: MagicLinkAuditorEvent): Promise<void> {
        this.logger.debug(PolloMessage.msg('Handling the AuditorMagicLinkEvent'));
        const {
            auditor: {
                firstName,
                entry: { email },
            },
            emailLanguage,
        } = event;
        // Set isAuditor band true
        const token = await this.authService.generateMagicLinkToken({
            email,
            isAuditor: true,
        });
        const magicLinkEmail = await this.emailConfig.magicLinkEmail(emailLanguage);
        const templateVariables = {
            title: format(magicLinkEmail.title, firstName),
            paragraphOne: magicLinkEmail.paragraphOne,
            ctaUrl: `${config.get('url.webApp')}/magic-link?token=${
                token.id
            }&region=${getServerRegion()}&source=auditor`,
            btnText: magicLinkEmail.btnText,
            currentYear: currentYear(),
            buttonNotWorking: magicLinkEmail.buttonNotWorking,
            ...magicLinkEmail.templateCommon,
        };
        const emailOptions: EmailOptionsType = {
            ...magicLinkEmail,
            toEmail: email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.HIGH,
        };
        this.emailService.sendEmail(
            emailOptions,
            undefined, // NO account here
        );
    }
}
