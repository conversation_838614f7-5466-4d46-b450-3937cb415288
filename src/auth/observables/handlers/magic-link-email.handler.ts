import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { TenantEventHandler } from 'app/events/tenant-event.handler';
import { MagicLinkEvent } from 'auth/observables/events/magic-link.event';
import { AuthService } from 'auth/services/auth.service';
import { EmailConfig } from 'commons/configs/email.config';
import { currentYear } from 'commons/helpers/date.helper';
import { getServerRegion } from 'commons/helpers/environment.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType, EmailTimeSensitivity } from 'commons/types/email-options.type';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { format } from 'util';

@EventsHandler(MagicLinkEvent)
export class MagicLinkEmailHandler extends TenantEventHandler<MagicLinkEvent> {
    /**
     *
     * @param authService
     * @param emailConfig
     * @param emailService
     */
    constructor(
        private readonly authService: AuthService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
    ) {
        super();
    }

    /**
     *
     * @param event
     */
    async handleEvent(event: MagicLinkEvent): Promise<void> {
        // log event handler init
        this.logger.debug(PolloMessage.msg('Handling the MagicLinkEvent'));
        // get the user from the event
        const { user, account } = event;
        // create a new magic link token
        const token = await this.authService.generateMagicLinkToken({
            email: user.email,
        });
        // Get the user region
        const region = getServerRegion();
        // get user language
        const emailLanguage = getLanguage(user.language, account.language);
        // get the email options for this email
        const magicLinkEmail = await this.emailConfig.magicLinkEmail(emailLanguage);
        // set the custom email template variables

        const templateVariables = {
            title: format(magicLinkEmail.title, user.firstName),
            paragraphOne: magicLinkEmail.paragraphOne,
            ctaUrl: `${config.get('url.webApp')}/magic-link?token=${token.id}&region=${region}`,
            btnText: magicLinkEmail.btnText,
            currentYear: currentYear(),
            buttonNotWorking: magicLinkEmail.buttonNotWorking,
            ...magicLinkEmail.templateCommon,
        };
        // set the email options
        const emailOptions: EmailOptionsType = {
            ...magicLinkEmail,
            toEmail: user.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.HIGH,
        };
        // send email
        this.emailService.sendEmail(emailOptions, account);
    }
}
