import { Entry } from 'auth/entities/entry.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import {
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    RemoveEvent,
    SoftRemoveEvent,
    UpdateEvent,
} from 'typeorm';

/**
 * This TypeORM subscriber is used to listen to Entry changes and perform actions.
 * This is useful for performing actions when an entry is created, updated, or deleted.
 * For example, we can use this to publish an event to event bridge for global router
 *
 * https://typeorm.io/docs/advanced-topics/listeners-and-subscribers/#what-is-a-subscriber
 */
@EventSubscriber()
export class EntryCoreSubscriber implements EntitySubscriberInterface<Entry> {
    protected logger = PolloLogger.logger(this.constructor.name);

    constructor(
        dataSource: DrataDataSource,
        private readonly entryCoreService: EntryCoreService,
    ) {
        dataSource.subscribers.push(this);
    }

    listenTo() {
        return Entry;
    }

    /**
     * Note that event.entity may not necessarily contain primary key(s) when Repository.update() is used.
     * Only the values provided as the entity partial will be available.
     * In order to make primary keys available in the subscribers, you can explicitly pass primary key value(s)
     * in the partial entity object literal or use Repository.save(), which performs re-fetching.
     *
     * https://typeorm.io/docs/advanced-topics/listeners-and-subscribers/#event-object
     */
    afterUpdate(event: UpdateEvent<Entry>) {
        void this.entryCoreService
            .publishEntryUpdatedEvent(event.databaseEntity, event.entity as Entry)
            .catch(this.handleError);
    }

    afterInsert(event: InsertEvent<Entry>) {
        void this.entryCoreService.publishEntryCreatedEvent(event.entity).catch(this.handleError);
    }

    afterRemove(event: RemoveEvent<Entry>) {
        void this.entryCoreService
            .publishEntryDeletedEvent(event.databaseEntity)
            .catch(this.handleError);
    }

    afterSoftRemove(event: SoftRemoveEvent<Entry>) {
        void this.entryCoreService
            .publishEntrySoftDeletedEvent(event.databaseEntity)
            .catch(this.handleError);
    }

    private handleError(error: Error) {
        this.logger.warn(PolloMessage.msg('Error publishing entry event').setError(error));
    }
}
