import { Injectable, NotFoundException } from '@nestjs/common';
import { EventBridgeService } from 'app/eventbridge/services/eventbridge.service';
import { EventBridgeResult } from 'app/eventbridge/types/event-bridge-result.type';
import {
    ENTRY_EVENT_BRIDGE_DETAIL_TYPE,
    ENTRY_EVENT_BRIDGE_VERSION_ONE,
} from 'auth/constants/entry.constants';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { Cache, CacheBuster } from 'cache/cache.decorator';
import { Caches } from 'commons/enums/cache.enum';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import { isEmpty, isNil } from 'lodash';

@Injectable()
export class EntryCoreService extends AppService {
    constructor(
        private readonly entryRepository: EntryRepository,
        private readonly eventBridgeService: EventBridgeService,
    ) {
        super();
    }

    /**
     * Fetch an Entry by Email. We do not use a cache here.
     */
    async getEntryByEmail(email: string, isAuditor = false, withDeleted = false): Promise<Entry> {
        const entry = await this.entryRepository.findOne({ where: { email }, withDeleted });
        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        } else {
            throw new NotFoundException({
                entryLength: !isNil(entry) ? entry.accounts.length : null,
                email,
            });
        }
    }

    /**
     * Explicitly bust the same cache key used by getEntryById,
     * matching `useArgs: 1` so this method can delete the correct entry ID key.
     */
    @CacheBuster<Entry>({ store: Caches.ENTRY, useArgs: 1 })
    bustEntryCache(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        id: string,
    ): Promise<Entry | null> {
        // We simply return null so that the decorator can do the actual busting.
        return Promise.resolve(null);
    }

    /**
     * Explicitly bust the same cache key used by getEntryByIdWithAccount,
     * matching `useArgs: 2` so this method can delete the correct entry ID and account ID key.
     */
    @CacheBuster<Entry>({ store: Caches.ENTRY, useArgs: 2 })
    bustEntryCacheWithAccount(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        id: string,
        accountId: AccountIdType,
    ): Promise<Entry | null> {
        // We simply return null so that the decorator can do the actual busting.
        return Promise.resolve(null);
    }

    async getEntryByEmailNoFail(email: string, isAuditor = false): Promise<Entry | null> {
        const entry = await this.entryRepository.findOneBy({ email });

        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        }

        return null;
    }

    async getEntryByEmailIncludingDeletedNoFail(
        email: string,
        isAuditor = false,
    ): Promise<Entry | null> {
        const entry = await this.entryRepository.findOne({
            where: { email },
        });

        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        }

        return null;
    }

    async getEntryByEmailNoFailV2(email: string, isAuditor = false): Promise<Entry | null> {
        const entry = await this.entryRepository.getWithAccountsByEmail(email);

        if (!isNil(entry) && (isAuditor || entry.accounts?.length)) {
            return entry;
        }

        return null;
    }

    /**
     * Fetch an Entry by email without any relations.
     * @param email the email of the entry
     * @returns an Entry without any relationship data or null
     */
    async getEntryByEmailNoRelationsNoFail(email: string): Promise<Entry | null> {
        return this.entryRepository.findOne({
            where: { email },
            loadEagerRelations: false,
        });
    }

    async getEntryWithAccountsByEmailOrNull(email: string): Promise<Entry | null> {
        const entry = await this.entryRepository.findOneBy({ email });

        if (!isEmpty(entry?.accounts)) {
            return entry;
        }

        return null;
    }

    /**
     * Fetch an Entry by ID, caching the result in the Caches.ENTRY store.
     * We specify `useArgs: 1` so that the key is derived from the single `id` param.
     */
    @Cache<Entry>(Entry, { store: Caches.ENTRY, useArgs: 1, ttl: config.get('cache.ttl.15min') })
    async getEntryById(entryId: string): Promise<Entry> {
        const entry = await this.entryRepository.findOneBy({ id: entryId });

        if (!isNil(entry) && entry.accounts.length > 0) {
            return entry;
        } else {
            throw new NotFoundException({
                entryId,
                message: 'Entry not found for account',
            });
        }
    }

    /**
     * Fetch an Entry by ID, caching the result in the Caches.ENTRY store.
     * We specify `useArgs: 2` so that the key is derived from the `id` param and `accountId` param.
     */
    @Cache<Entry>(Entry, { store: Caches.ENTRY, useArgs: 2, ttl: config.get('cache.ttl.15min') })
    async getEntryByIdWithAccount(entryId: string, accountId: AccountIdType): Promise<Entry> {
        /**
         * we just want to get the entry with
         * the one account that matches the accountId
         */
        const entry = await this.entryRepository.findOne({
            where: {
                id: entryId,
                accounts: { id: accountId },
            },
            loadEagerRelations: false,
            relations: {
                accounts: {
                    entitlements: true,
                    tenantDatabaseHost: true,
                    safeBaseSettings: true,
                },
            },
        });

        if (!isNil(entry) && entry.accounts.length > 0) {
            return entry;
        } else {
            throw new NotFoundException({
                entryId,
                accountId,
                message: 'Entry not found for account',
            });
        }
    }

    /**
     * Fetch an Entry by ID without loading eager relations.
     * Use this method when you only need the entry data without related entities
     * to improve performance by avoiding unnecessary joins.
     *
     * @param entryId - The entry ID to fetch
     * @returns Promise<Entry> - The entry without relations
     * @throws EntityNotFoundError if entry is not found
     */
    getEntryWithoutRelationsByIdOrFail(entryId: string): Promise<Entry> {
        return this.entryRepository.findOneOrFail({
            where: {
                id: entryId,
            },
            loadEagerRelations: false,
        });
    }

    getEntryByEmailForAuditorNoFail(email: string): Promise<Entry | null> {
        return this.entryRepository.findOneBy({ email });
    }

    createEntryByEmail(email: string, account: Account): Promise<Entry> {
        const entry = new Entry();
        entry.email = email;
        entry.accounts = [account];

        this.bustEntryCache(entry.id).catch(() => {
            // Silently ignore cache busting errors
        });

        return this.saveEntry(entry);
    }

    @CacheBuster<Entry>()
    saveEntry(entry: Entry): Promise<Entry> {
        return this.entryRepository.save(entry);
    }

    @CacheBuster<Entry>()
    saveEntryNoReload(entry: Entry): Promise<Entry> {
        return this.entryRepository.save(entry, { reload: false });
    }

    getEntriesForAccount(account: Account, domain?: string): Promise<Entry[]> {
        return this.entryRepository.getEntriesForAccount(account.id, domain);
    }

    getOwnerEntryForPendingAccount(accountId: string): Promise<Entry> {
        return this.entryRepository.getOwnerEntryForPendingAccount(accountId);
    }

    getEntriesCountForDomain(domain: string): Promise<number> {
        return this.entryRepository.getEntriesCountForDomain(domain);
    }

    saveEntries(entries: Entry[]): Promise<Entry[]> {
        return this.entryRepository.save(entries, { reload: false });
    }

    async getEntryWithTenantRouterAccountsByEmail(
        email: string,
        tenantRouterKey: string,
    ): Promise<Entry> {
        const entry = await this.entryRepository
            .createQueryBuilder('Entry')
            .select(['Entry', 'Account', 'TenantRouter', 'TenantRouterAccount'])
            .innerJoin('Entry.accounts', 'Account')
            .innerJoin('Account.tenantRouters', 'TenantRouter')
            .innerJoin('TenantRouter.account', 'TenantRouterAccount')
            .where('Entry.email = :email', { email })
            .andWhere('TenantRouter.key = :tenantRouterKey', {
                tenantRouterKey,
            })
            .getOne();

        if (isNil(entry)) {
            throw new NotFoundException({ email });
        }

        const tenantRouterAccounts = entry.accounts
            .map(acc => acc.tenantRouters?.map(tr => tr.account))
            .flat()
            .filter(tenantRouterAccount => !isNil(tenantRouterAccount));

        entry.accounts = tenantRouterAccounts;

        return entry;
    }

    async publishEntryCreatedEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_CREATED, {
            id: entry.id,
            email: entry.email,
            version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
            domain: entry.domain,
        });
    }

    async publishEntryRestoredEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(
            ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_RESTORED,
            {
                id: entry.id,
                email: entry.email,
                version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                domain: entry.domain,
            },
        );
    }

    /**
     * Updates encompass many possible scenarios, so we add extra business logic, specific to
     * how Entry entities behave and what we know downstream consumers need.
     *
     * We want to avoid publishing entire entities in before and after properties
     * as entity size can get large and we want to keep event bridge payloads small.
     */
    async publishEntryUpdatedEvent(before: Entry, after: Entry): Promise<EventBridgeResult> {
        const changes = {
            isEmailDifferent: before.email !== after.email,
            isSoftDeleted: before.deletedAt !== after.deletedAt,
            isRestored: before.deletedAt && !after.deletedAt,
        };
        if (changes.isSoftDeleted) {
            return this.publishEntrySoftDeletedEvent(before);
        } else if (changes.isRestored) {
            return this.publishEntryRestoredEvent(before);
        } else if (changes.isEmailDifferent) {
            return this.eventBridgeService.putEventToBus(
                ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_UPDATED,
                {
                    id: after.id,
                    email: after.email,
                    version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                    domain: after.domain,
                    before: {
                        id: before.id,
                        email: before.email,
                        version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                        domain: before.domain,
                    },
                },
            );
        } else {
            return { success: true };
        }
    }

    async publishEntryDeletedEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_DELETED, {
            id: entry.id,
            email: entry.email,
            version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
            domain: entry.domain,
            deletedAt: entry.deletedAt,
        });
    }

    async publishEntrySoftDeletedEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(
            ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_SOFT_DELETED,
            {
                id: entry.id,
                email: entry.email,
                version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                domain: entry.domain,
                deletedAt: entry.deletedAt,
            },
        );
    }

    async publishEntrySyncReadEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(
            ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_SYNC_READ,
            {
                id: entry.id,
                email: entry.email,
                version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                domain: entry.domain,
            },
        );
    }

    async publishEntrySyncReadSoftDeletedEvent(entry: Entry): Promise<EventBridgeResult> {
        return this.eventBridgeService.putEventToBus(
            ENTRY_EVENT_BRIDGE_DETAIL_TYPE.ENTRY_SYNC_READ_SOFT_DELETED,
            {
                id: entry.id,
                email: entry.email,
                version: ENTRY_EVENT_BRIDGE_VERSION_ONE,
                domain: entry.domain,
                deletedAt: entry.deletedAt,
            },
        );
    }
}
