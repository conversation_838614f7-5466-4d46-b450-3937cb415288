import { Injectable, NotFoundException } from '@nestjs/common';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { <PERSON><PERSON><PERSON><PERSON> } from 'cache/cache.decorator';
import { CacheService } from 'cache/cache.service';
import { SuperCacheAccountBuster } from 'cache/super-cache.decorator';
import { Caches } from 'commons/enums/cache.enum';
import { isNil } from 'lodash';

@Injectable()
export class EntryService {
    constructor(
        private readonly entryRepository: EntryRepository,
        private readonly cacheService: CacheService,
    ) {}

    /**
     * Fetch an Entry for auditor usage by ID (no cache here).
     */
    async getEntryForAuditorById(id: string): Promise<Entry> {
        return this.entryRepository.findOneByOrFail({ id });
    }

    /**
     * Find Entry by a public key (no cache).
     */
    async getEntryByPublicKey(key: string): Promise<Entry> {
        return this.entryRepository.findOneOrFail({
            relations: ['ownedPublicKeys'],
            where: {
                ownedPublicKeys: {
                    publicKey: key,
                },
            },
        });
    }

    /**
     * @deprecated Use EntryCoreService.getEntryByEmail
     *
     * Fetch an Entry by Email. We do not use a cache here.
     */
    async getEntryByEmail(email: string, isAuditor = false, withDeleted = false): Promise<Entry> {
        const entry = await this.entryRepository.findOne({ where: { email }, withDeleted });

        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        } else {
            throw new NotFoundException({
                entryLength: !isNil(entry) ? entry.accounts.length : null,
                email,
            });
        }
    }

    /**
     * @deprecated Use EntryCoreService.getEntryWithTenantRouterAccountsByEmail
     *
     * Find an Entry along with tenantRouter accounts by email.
     * (No caching here.)
     */
    async getEntryWithTenantRouterAccountsByEmail(
        email: string,
        tenantRouterKey: string,
    ): Promise<Entry> {
        const entry = await this.entryRepository
            .createQueryBuilder('Entry')
            .select(['Entry', 'Account', 'TenantRouter', 'TenantRouterAccount'])
            .innerJoin('Entry.accounts', 'Account')
            .innerJoin('Account.tenantRouters', 'TenantRouter')
            .innerJoin('TenantRouter.account', 'TenantRouterAccount')
            .where('Entry.email = :email', { email })
            .andWhere('TenantRouter.key = :tenantRouterKey', {
                tenantRouterKey,
            })
            .getOne();

        if (isNil(entry)) {
            throw new NotFoundException({ email });
        }

        const tenantRouterAccounts = entry.accounts
            .map(acc => acc.tenantRouters?.map(tr => tr.account))
            .flat()
            .filter(tenantRouterAccount => !isNil(tenantRouterAccount));

        entry.accounts = tenantRouterAccounts;
        return entry;
    }

    /**
     * @deprecated Use EntryCoreService.getEntryByEmailNoFail
     *
     * Return the entry or null if not found (no cache).
     */
    async getEntryByEmailNoFail(email: string, isAuditor = false): Promise<Entry | null> {
        const entry = await this.entryRepository.findOneBy({ email });
        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        }
        return null;
    }

    /**
     * Return an entry for an auditor or null if not found (no cache).
     */
    async getEntryByEmailForAuditorNoFail(email: string): Promise<Entry | null> {
        return this.entryRepository.findOneBy({ email });
    }

    /**
     * @deprecated Use EntryCoreService.getEntriesForAccount
     *
     * Fetch all entries belonging to an account. (No cache.)
     */
    getEntriesForAccount(account: Account, domain?: string): Promise<Entry[]> {
        return this.entryRepository.getEntriesForAccount(account.id, domain);
    }

    /**
     * Fetch the entries IDs associated with the given account.
     *
     * @param account
     * @returns
     */
    async getEntryIdsForAccount(account: Account): Promise<string[]> {
        const db = this.entryRepository.manager;
        const parameters = [account.id];

        const result: { entryId: string }[] = await db.query(
            'SELECT fk_entry_id as entryId FROM account_entry_map WHERE fk_account_id  = ?',
            parameters,
        );
        if (!result) {
            return [];
        }
        return result.map(({ entryId }) => entryId);
    }

    /**
     * @deprecated Use EntryCoreService.saveEntry
     *
     * Save/update an Entry in the DB (no direct cache operation here).
     */
    saveEntry(entry: Entry): Promise<Entry> {
        return this.entryRepository.save(entry);
    }

    /**
     * @deprecated Use EntryCoreService.saveEntries
     *
     * Bulk save multiple Entries.
     */
    saveEntries(entries: Entry[]): Promise<Entry[]> {
        return this.entryRepository.save(entries, { reload: false });
    }

    /**
     * Create a new Entry with the given email, associating it with a single account.
     */
    @SuperCacheAccountBuster<Entry>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })

    /**
     * @deprecated Use EntryCoreService.createEntryByEmail
     */
    createEntryByEmail(email: string, account: Account): Promise<Entry> {
        const entry = new Entry();
        entry.email = email;
        entry.accounts = [account];

        this.bustEntryCache(entry.id).catch(() => {
            // Silently ignore cache busting errors
        });

        return this.saveEntry(entry);
    }

    /**
     * @deprecated Use EntryCoreService.bustEntryCache
     *
     * Explicitly bust the same cache key used by getEntryById,
     * matching `useArgs: 1` so this method can delete the correct entry ID key.
     */
    @CacheBuster<Entry>({ store: Caches.ENTRY, useArgs: 1 })
    bustEntryCache(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        id: string,
    ): Promise<Entry | null> {
        // We simply return null so that the decorator can do the actual busting.
        return Promise.resolve(null);
    }

    /**
     * @deprecated Use EntryCoreService.getOwnerEntryForPendingAccount
     *
     * Get owner entry for a pending account. (No cache.)
     */
    getOwnerEntryForPendingAccount(accountId: string): Promise<Entry> {
        return this.entryRepository.getOwnerEntryForPendingAccount(accountId);
    }

    /**
     * @deprecated Use EntryCoreService.getEntriesCountForDomain
     *
     * Count how many entries exist for a given domain. (No cache.)
     */
    getEntriesCountForDomain(domain: string): Promise<number> {
        return this.entryRepository.getEntriesCountForDomain(domain);
    }
}
