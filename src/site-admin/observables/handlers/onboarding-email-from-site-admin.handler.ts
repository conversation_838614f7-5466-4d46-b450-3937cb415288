import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { AuthService } from 'auth/services/auth.service';
import { EmailConfig } from 'commons/configs/email.config';
import { currentYear } from 'commons/helpers/date.helper';
import { getServerRegion } from 'commons/helpers/environment.helper';
import { GlobalEventHandler } from 'commons/observables/handlers/global-event-handler.base';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType, EmailTimeSensitivity } from 'commons/types/email-options.type';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { AccountReadyEvent } from 'site-admin/observables/events/account-ready.event';

@EventsHandler(AccountReadyEvent)
export class OnboardingEmailFromSiteAdminHandler extends GlobalEventHandler<AccountReadyEvent> {
    constructor(
        private readonly authService: AuthService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
    ) {
        super();
    }

    async handleEvent(event: AccountReadyEvent): Promise<void> {
        this.logger.debug(PolloMessage.msg('Handling the AccountReadyEvent'));
        // get the entry from the event
        const { readyAct: account, email, emailLanguage } = event;
        // create a new magic link token
        const token = await this.authService.generateMagicLinkToken({
            email,
        });
        // get the email options for this email
        const welcomeEmailFromSiteAdmin = await this.emailConfig.welcomeEmailFromSiteAdmin(
            emailLanguage,
        );
        // set the custom email template variables
        const templateVariables = {
            title: welcomeEmailFromSiteAdmin.title,
            message: welcomeEmailFromSiteAdmin.message,
            subMessage: welcomeEmailFromSiteAdmin.subMessage,
            ctaUrl: `${config.get('url.webApp')}/magic-link?token=${
                token.id
            }&region=${getServerRegion()}`,
            ctaText: welcomeEmailFromSiteAdmin.ctaText,
            currentYear: currentYear(),
            snippet: welcomeEmailFromSiteAdmin.snippet,
            ...welcomeEmailFromSiteAdmin.templateCommon,
        };
        // set the email options
        const emailOptions: EmailOptionsType = {
            ...welcomeEmailFromSiteAdmin,
            toEmail: email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.HIGH,
        };
        // send email
        this.emailService.sendEmail(emailOptions, account);
    }
}
