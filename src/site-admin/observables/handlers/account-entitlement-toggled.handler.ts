import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { TenantEventHandler } from 'app/events/tenant-event.handler';
import { Account } from 'auth/entities/account.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { AccountEntitlementToggledEvent } from 'site-admin/observables/events/account-entitlement-toggled.event';

@EventsHandler(AccountEntitlementToggledEvent)
export class AccountEntitlementToggledHandler extends TenantEventHandler<AccountEntitlementToggledEvent> {
    constructor(private readonly entryCoreService: EntryCoreService) {
        super();
    }

    async handleEvent(event: AccountEntitlementToggledEvent): Promise<void> {
        try {
            const { account, entitlements } = event;

            this.logger.debug(
                PolloMessage.msg(
                    `AccountEntitlementToggledEvent: Handling the AccountEntitlementToggledEvent, account id: ${
                        account.id
                    }, entitlements: ${entitlements.join(',')}`,
                )
                    .setContext(this.constructor.name)
                    .setIdentifier({ entitlements }),
            );

            const entries = await this.entryCoreService.getEntriesForAccount({
                id: account.id,
            } as Account);

            for await (const entry of entries) {
                await this.entryCoreService.bustEntryCache(entry.id);
                await this.entryCoreService.bustEntryCacheWithAccount(entry.id, account.id);
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    'AccountEntitlementToggledEvent: There was an error busting the cache',
                ).setError(error),
            );
        }
    }
}
